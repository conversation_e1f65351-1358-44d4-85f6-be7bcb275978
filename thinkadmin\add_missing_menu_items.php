<?php

/**
 * 添加缺失的菜单项
 */

$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 添加缺失的菜单项 ===\n\n";
    
    // 需要添加的菜单项
    $missingMenus = [
        '文档导出' => [
            [
                'title' => '导出样式模板',
                'node' => 'admin/export_template/index',
                'url' => 'admin/export_template/index',
                'icon' => 'layui-icon layui-icon-template',
                'sort' => 1
            ]
        ]
    ];
    
    $addedCount = 0;
    $errorCount = 0;
    
    foreach ($missingMenus as $parentTitle => $children) {
        echo "处理菜单组: {$parentTitle}\n";
        
        // 查找父菜单ID
        $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = 0");
        $stmt->execute([$parentTitle]);
        $parentMenu = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$parentMenu) {
            echo "  ❌ 未找到父菜单: {$parentTitle}\n";
            $errorCount++;
            continue;
        }
        
        $parentId = $parentMenu['id'];
        
        foreach ($children as $menuInfo) {
            // 检查菜单是否已存在
            $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = ?");
            $stmt->execute([$menuInfo['title'], $parentId]);
            $existingMenu = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existingMenu) {
                echo "  ⚠️  菜单已存在: {$menuInfo['title']}\n";
                continue;
            }
            
            // 添加新菜单
            $stmt = $pdo->prepare("
                INSERT INTO system_menu (pid, title, node, url, icon, sort, status, create_at) 
                VALUES (?, ?, ?, ?, ?, ?, 1, datetime('now'))
            ");
            
            if ($stmt->execute([
                $parentId,
                $menuInfo['title'],
                $menuInfo['node'],
                $menuInfo['url'],
                $menuInfo['icon'],
                $menuInfo['sort']
            ])) {
                echo "  ✅ 添加菜单: {$menuInfo['title']}\n";
                $addedCount++;
            } else {
                echo "  ❌ 添加菜单失败: {$menuInfo['title']}\n";
                $errorCount++;
            }
        }
        echo "\n";
    }
    
    // 更新系统设置中AI模型配置和n8n工作流管理的排序
    echo "更新系统设置菜单排序...\n";
    
    $systemSettingsUpdates = [
        'AI模型配置' => 1,
        'n8n工作流管理' => 2,
        '接口密钥管理' => 3,
        'Webhook配置' => 4,
        '内容风控规则' => 5,
        '基础参数设置' => 6
    ];
    
    // 获取系统设置菜单ID
    $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = '系统设置' AND pid = 0");
    $stmt->execute();
    $systemSettingsMenu = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($systemSettingsMenu) {
        foreach ($systemSettingsUpdates as $menuTitle => $sortOrder) {
            $stmt = $pdo->prepare("UPDATE system_menu SET sort = ? WHERE title = ? AND pid = ?");
            if ($stmt->execute([$sortOrder, $menuTitle, $systemSettingsMenu['id']])) {
                echo "  ✅ 更新排序: {$menuTitle} -> {$sortOrder}\n";
            } else {
                echo "  ❌ 更新排序失败: {$menuTitle}\n";
                $errorCount++;
            }
        }
    }
    
    // 更新AI模型配置和n8n工作流管理的图标
    echo "\n更新图标...\n";
    
    $iconUpdates = [
        'AI模型配置' => 'layui-icon layui-icon-engine',
        'n8n工作流管理' => 'layui-icon layui-icon-component'
    ];
    
    if ($systemSettingsMenu) {
        foreach ($iconUpdates as $menuTitle => $icon) {
            $stmt = $pdo->prepare("UPDATE system_menu SET icon = ? WHERE title = ? AND pid = ?");
            if ($stmt->execute([$icon, $menuTitle, $systemSettingsMenu['id']])) {
                echo "  ✅ 更新图标: {$menuTitle}\n";
            } else {
                echo "  ❌ 更新图标失败: {$menuTitle}\n";
                $errorCount++;
            }
        }
    }
    
    echo "\n=== 添加完成 ===\n";
    echo "成功添加: {$addedCount} 项\n";
    echo "失败: {$errorCount} 项\n";
    
    // 显示最终的完整菜单结构
    echo "\n=== 最终完整菜单结构 ===\n";
    
    $stmt = $pdo->query("
        SELECT id, pid, title, node, icon, sort, status 
        FROM system_menu 
        WHERE pid = 0 AND status = 1 
        ORDER BY sort, id
    ");
    $parentMenus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($parentMenus as $parent) {
        if (in_array($parent['title'], ['写作中心', '降重与查重', '文档导出', '用户中心', '收费系统', '通知与消息', '系统设置'])) {
            echo "\n{$parent['title']} (ID: {$parent['id']}, Sort: {$parent['sort']})\n";
            echo "  图标: {$parent['icon']}\n";
            
            $stmt = $pdo->prepare("
                SELECT title, node, icon, sort, status 
                FROM system_menu 
                WHERE pid = ? 
                ORDER BY sort, id
            ");
            $stmt->execute([$parent['id']]);
            $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($children as $child) {
                $statusIcon = $child['status'] ? '✅' : '❌';
                echo "  {$statusIcon} {$child['title']} -> {$child['node']} (Sort: {$child['sort']})\n";
                echo "    图标: {$child['icon']}\n";
            }
        }
    }
    
    // 生成权限节点配置文档
    echo "\n=== 权限节点配置 ===\n";
    echo "以下是所有模块的权限节点配置，可用于控制器的@auth注解：\n\n";
    
    $allMenus = [];
    $stmt = $pdo->query("
        SELECT p.title as parent_title, c.title, c.node 
        FROM system_menu c 
        JOIN system_menu p ON c.pid = p.id 
        WHERE c.pid > 0 AND c.status = 1 AND c.node != '' 
        ORDER BY p.sort, c.sort
    ");
    $menuNodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $currentParent = '';
    foreach ($menuNodes as $menu) {
        if ($menu['parent_title'] != $currentParent) {
            $currentParent = $menu['parent_title'];
            echo "\n## {$currentParent}\n";
        }
        
        // 生成权限节点
        $baseNode = $menu['node'];
        $controllerParts = explode('/', $baseNode);
        $controller = $controllerParts[1] ?? '';
        
        echo "### {$menu['title']}\n";
        echo "- 基础节点: `{$baseNode}`\n";
        echo "- 列表权限: `admin/{$controller}/index`\n";
        echo "- 添加权限: `admin/{$controller}/add`\n";
        echo "- 编辑权限: `admin/{$controller}/edit`\n";
        echo "- 删除权限: `admin/{$controller}/remove`\n";
        echo "- 状态权限: `admin/{$controller}/state`\n";
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
