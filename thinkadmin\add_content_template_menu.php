<?php

// 直接使用SQLite命令添加正文模板管理菜单

$dbPath = 'database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: $dbPath" . PHP_EOL;
    exit(1);
}

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查找写作中心菜单
    $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = '写作中心'");
    $stmt->execute();
    $writingCenter = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$writingCenter) {
        echo "未找到写作中心菜单" . PHP_EOL;
        exit(1);
    }
    
    $writingCenterId = $writingCenter['id'];
    echo "写作中心菜单ID: $writingCenterId" . PHP_EOL;
    
    // 检查是否已有正文模板管理菜单
    $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE pid = ? AND node = 'admin/content_template/index'");
    $stmt->execute([$writingCenterId]);
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        echo "正文模板管理菜单已存在，ID: " . $existing['id'] . PHP_EOL;
    } else {
        // 添加正文模板管理菜单
        $stmt = $pdo->prepare("
            INSERT INTO system_menu (pid, title, icon, node, url, params, target, sort, status, create_at) 
            VALUES (?, '正文模板管理', 'layui-icon layui-icon-template-1', 'admin/content_template/index', '', '', '_self', 240, 1, ?)
        ");
        
        $result = $stmt->execute([$writingCenterId, date('Y-m-d H:i:s')]);
        
        if ($result) {
            echo "正文模板管理菜单添加成功！" . PHP_EOL;
        } else {
            echo "正文模板管理菜单添加失败！" . PHP_EOL;
        }
    }
    
    // 显示写作中心下的所有子菜单
    $stmt = $pdo->prepare("SELECT title, node FROM system_menu WHERE pid = ? ORDER BY sort ASC");
    $stmt->execute([$writingCenterId]);
    $subMenus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo PHP_EOL . "写作中心子菜单:" . PHP_EOL;
    foreach ($subMenus as $menu) {
        echo "- " . $menu['title'] . " (" . $menu['node'] . ")" . PHP_EOL;
    }
    
} catch (PDOException $e) {
    echo "数据库错误: " . $e->getMessage() . PHP_EOL;
}
