@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

> .layui-layout-admin {
  > .layui-side {
    width: 260px;
    display: block !important;
    box-shadow: none !important;
    background-color: @mainColor;

    .layui-logo {
      z-index: 3;
    }

    .layui-side-target {
      display: block;
    }

    .layui-side-scroll {
      display: flex !important;
      box-sizing: border-box;

      .layui-nav-child {
        background: none !important;
      }

      .layui-side-icon {
        width: 100px;
        display: block;
        background: @mainColor;

        a {
          height: 60px;
          display: block;
          font-size: 14px;
          text-align: center;
          line-height: 60px;
          white-space: nowrap;

          &:hover {
            background-color: rgba(0, 0, 0, 0.1);;
          }

          .layui-icon, span {
            color: #fff;
            overflow: hidden;
            max-width: 4em;
            // margin-right: 5px;
            text-overflow: ellipsis;
            vertical-align: middle;
            display: inline-block !important;
          }
        }

        > .layui-this {
          background: #fff;

          a {
            &:hover {
              background-color: #fff;
            }

            .layui-icon, span {
              color: @textColor;
            }
          }
        }
      }

      .layui-side-tree {
        flex: 1;
        background-color: #fff;

        > .layui-nav-tree {
          width: unset !important;
        }

        .layui-nav-item {
          background: none !important;
          border-bottom-color: #fff;

          a {
            color: #333 !important;
            background: none !important;
            border-bottom: none !important;

            &:hover {
              color: @textColor;
            }
          }

          .layui-this, &.layui-this {
            > a {
              color: @textColor;
              background: none !important;
              font-weight: bold !important;

              &:hover {
                background: none !important;
              }
            }
          }

          &ed > a {
            color: #999 !important;
          }
        }
      }
    }
  }

  > .layui-body {
    left: 260px;

    > .think-page-body > .layui-card {
      &:before {
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 4;
        content: '';
        position: absolute;
        box-shadow: @ShadowBodyRight;
      }

      > .layui-card-header {
        left: 261px;
      }
    }
  }

  > .layui-header {
    left: 260px;
    background-color: @mainColor;

    .layui-layout-left {
      .layui-nav-item {
        display: none !important;
      }
    }

    .layui-nav-item {
      &.layui-this > a {
        color: #fff !important;
        font-weight: bold;
      }

      > a {
        color: #eee !important;

        &:hover {
          color: #fff !important;
          background: none !important;
        }
      }
    }
  }
}

> .layui-layout-left-hide,
> .layui-layout-left-mini {
  > .layui-side {

    .layui-logo {
      .headimg {
        margin-right: 10px !important;
      }

      .headtxt {
        display: inline-block !important;
      }
    }

    .layui-side-target {
      left: @iconWidth;
      margin-left: -12px;
    }

    .layui-side-scroll .layui-side-icon {
      width: @iconWidth;
      display: block;
      background: #098;
    }
  }

  > .layui-body {
    left: @iconWidth;
    z-index: 333;

    > .think-page-body > .layui-card > .layui-card-header {
      left: @iconWidth+1;
    }
  }

  > .layui-header {
    left: 260px !important;
    z-index: 334;
  }
}

.help-label b,
.layui-tab-title .layui-this {
  color: @mainColor
}

.layui-btn-group {
  border-color: @mainColor;
  background-color: @mainColor;

  .layui-btn {
    &:hover:not(.layui-btn-active) {
      color: @mainColor;
    }

    &.layui-btn-active {
      background-color: @mainColor
    }
  }
}