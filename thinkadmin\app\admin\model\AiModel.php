<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * AI模型配置模型
 * @class AiModel
 * @package app\admin\model
 */
class AiModel extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ai_model';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'max_tokens' => 'integer',
        'temperature' => 'float',
        'top_p' => 'float',
        'frequency_penalty' => 'float',
        'presence_penalty' => 'float',
        'cost_per_1k_tokens' => 'float',
        'priority' => 'integer',
        'rate_limit_per_minute' => 'integer',
        'status' => 'integer',
        'health_status' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getLastHealthCheckAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $status = $data['status'] ?? 0;
        return $status ? '启用' : '禁用';
    }

    /**
     * 获取健康状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getHealthStatusTextAttr($value, array $data): string
    {
        $healthStatus = $data['health_status'] ?? 0;
        return $healthStatus ? '健康' : '异常';
    }

    /**
     * 获取提供商文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getProviderTextAttr($value, array $data): string
    {
        $providerMap = [
            'openai' => 'OpenAI',
            'baidu' => '百度',
            'aliyun' => '阿里云',
            'tencent' => '腾讯云',
            'zhipu' => '智谱AI',
            'moonshot' => 'Moonshot',
            'other' => '其他'
        ];
        return $providerMap[$data['provider'] ?? 'other'] ?? '未知';
    }

    /**
     * 获取成本文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getCostTextAttr($value, array $data): string
    {
        $cost = $data['cost_per_1k_tokens'] ?? 0;
        return '¥' . number_format($cost, 6) . '/1K tokens';
    }

    /**
     * 关联AI使用记录
     * @return \think\model\relation\HasMany
     */
    public function usageLogs()
    {
        return $this->hasMany(AiUsageLog::class, 'ai_model_id', 'id');
    }

    /**
     * 获取可用的AI模型列表
     * @param string $provider
     * @return array
     */
    public static function getAvailableModels(string $provider = ''): array
    {
        $query = static::mk()->where(['status' => 1, 'health_status' => 1]);
        
        if (!empty($provider)) {
            $query->where(['provider' => $provider]);
        }
        
        return $query->order('priority desc,id desc')->column('name', 'id');
    }

    /**
     * 获取最佳模型
     * @param string $provider
     * @return array
     */
    public static function getBestModel(string $provider = ''): array
    {
        $query = static::mk()->where(['status' => 1, 'health_status' => 1]);
        
        if (!empty($provider)) {
            $query->where(['provider' => $provider]);
        }
        
        return $query->order('priority desc,id desc')->findOrEmpty()->toArray();
    }

    /**
     * 计算使用成本
     * @param int $modelId
     * @param int $tokens
     * @return float
     */
    public static function calculateCost(int $modelId, int $tokens): float
    {
        $model = static::mk()->findOrEmpty($modelId);
        if ($model->isEmpty()) {
            return 0.0;
        }
        
        $costPer1k = $model['cost_per_1k_tokens'] ?? 0;
        return ($tokens / 1000) * $costPer1k;
    }

    /**
     * 更新健康状态
     * @param int $modelId
     * @param int $healthStatus
     * @return bool
     */
    public static function updateHealthStatus(int $modelId, int $healthStatus): bool
    {
        return static::mk()->where(['id' => $modelId])->update([
            'health_status' => $healthStatus,
            'last_health_check' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]) > 0;
    }

    /**
     * 获取模型统计信息
     * @param int $modelId
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getModelStats(int $modelId, string $startDate = '', string $endDate = ''): array
    {
        $query = AiUsageLog::mk()->where(['ai_model_id' => $modelId]);
        
        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate);
        }
        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate);
        }
        
        $logs = $query->select();
        
        return [
            'total_requests' => $logs->count(),
            'success_requests' => $logs->where('status', 'success')->count(),
            'failed_requests' => $logs->where('status', 'failed')->count(),
            'total_tokens' => $logs->sum('total_tokens'),
            'total_cost' => $logs->sum('cost'),
            'avg_response_time' => $logs->avg('response_time'),
        ];
    }
}
