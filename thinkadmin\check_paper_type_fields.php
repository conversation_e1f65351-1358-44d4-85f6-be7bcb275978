<?php

/**
 * 检查paper_type表字段
 */

echo "=== 检查paper_type表字段 ===\n\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在\n";
        exit(1);
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
    
    // 检查paper_type表结构
    echo "1. paper_type表字段:\n";
    $stmt = $pdo->query("PRAGMA table_info(paper_type)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "  - {$column['name']} ({$column['type']})";
        if ($column['notnull']) echo " NOT NULL";
        if ($column['dflt_value'] !== null) echo " DEFAULT {$column['dflt_value']}";
        echo "\n";
    }
    
    // 检查模板中使用的字段
    echo "\n2. 模板中使用的字段检查:\n";
    $templateFields = [
        'id', 'sort', 'name', 'description', 'word_count_range', 
        'outline_template', 'status', 'create_time'
    ];
    
    $actualFields = array_column($columns, 'name');
    
    foreach ($templateFields as $field) {
        if (in_array($field, $actualFields)) {
            echo "  ✅ {$field} - 字段存在\n";
        } else {
            echo "  ❌ {$field} - 字段不存在\n";
            
            // 查找相似字段
            $similar = [];
            foreach ($actualFields as $actualField) {
                if (strpos($actualField, $field) !== false || strpos($field, $actualField) !== false) {
                    $similar[] = $actualField;
                }
            }
            
            if (!empty($similar)) {
                echo "    💡 相似字段: " . implode(', ', $similar) . "\n";
            }
        }
    }
    
    // 检查实际数据
    echo "\n3. 检查实际数据:\n";
    $stmt = $pdo->query("SELECT * FROM paper_type LIMIT 1");
    $sample = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sample) {
        echo "  📊 样本数据字段:\n";
        foreach ($sample as $field => $value) {
            $displayValue = is_null($value) ? 'NULL' : (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
            echo "    {$field}: {$displayValue}\n";
        }
    } else {
        echo "  ⚠️  表中没有数据\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
