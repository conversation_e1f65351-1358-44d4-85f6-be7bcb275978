<?php

namespace app\admin\controller;

use app\admin\model\PromptTemplateModel;
use think\admin\Controller;

/**
 * 提示词模板管理
 * @class PromptTemplate
 * @package app\admin\controller
 */
class PromptTemplate extends Controller
{
    /**
     * 提示词模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        PromptTemplateModel::mQuery($this)->layPage(function () {
            $this->title = '提示词模板管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加提示词模板管理
     * @auth true
     */
    public function add()
    {
        PromptTemplateModel::mForm('prompt_template/form');
    }

    /**
     * 编辑提示词模板管理
     * @auth true
     */
    public function edit()
    {
        PromptTemplateModel::mForm('prompt_template/form');
    }

    /**
     * 删除提示词模板管理
     * @auth true
     */
    public function remove()
    {
        PromptTemplateModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        PromptTemplateModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}