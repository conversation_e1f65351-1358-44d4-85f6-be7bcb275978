{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">提示词模板详情</div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.name|default=''}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板类型</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {switch $vo.type}
                        {case value="outline"}大纲生成{/case}
                        {case value="writing"}内容写作{/case}
                        {case value="rewrite"}内容改写{/case}
                        {case value="summary"}内容总结{/case}
                        {case value="expansion"}内容扩展{/case}
                        {case value="polish"}内容润色{/case}
                        {default /}{$vo.type}{/switch}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$paperTypes[$vo.paper_type_id]|default='通用'}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">AI模型</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.ai_model|default=''}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">提示词内容</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="white-space: pre-wrap; background: #f8f8f8; padding: 10px; border-radius: 4px; min-height: 200px;">
{$vo.content|default=''}
                    </div>
                </div>
            </div>
            
            {if !empty($vo.variables)}
            <div class="layui-form-item">
                <label class="layui-form-label">变量定义</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="background: #f8f8f8; padding: 10px; border-radius: 4px;">
                        <pre>{$vo.variables|default=''}</pre>
                    </div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">模型参数</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Temperature</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.temperature|default=0.7}</div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Max Tokens</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.max_tokens|default=2000}</div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Top P</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.top_p|default=1}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {if !empty($vo.description)}
            <div class="layui-form-item">
                <label class="layui-form-label">描述说明</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="white-space: pre-wrap;">{$vo.description|default=''}</div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">使用次数</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.usage_count|default=0} 次</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $vo.is_default eq 1}
                        <span class="layui-badge layui-bg-green">是</span>
                        {else}
                        <span class="layui-badge">否</span>
                        {/if}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $vo.status eq 1}
                        <span class="layui-badge layui-bg-green">启用</span>
                        {else}
                        <span class="layui-badge">禁用</span>
                        {/if}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.create_time|format_datetime}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">更新时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.update_time|format_datetime}</div>
                </div>
            </div>
            
            <div class="hr-line-dashed"></div>
            
            <div class="layui-form-item text-center">
                {if auth("edit")}
                <a class="layui-btn" href="{:url('edit')}?id={$vo.id}">编辑模板</a>
                {/if}
                <button class="layui-btn layui-btn-danger" type='button' data-close>关闭窗口</button>
            </div>
            
        </div>
    </div>
</div>
{/block}
