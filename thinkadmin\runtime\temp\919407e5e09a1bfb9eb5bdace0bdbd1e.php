<?php /*a:2:{s:81:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\ai_model\index.html";i:1754095841;s:72:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\table.html";i:1754094170;}*/ ?>
<div class="layui-card"><?php if(!(empty($title) || (($title instanceof \think\Collection || $title instanceof \think\Paginator ) && $title->isEmpty()))): ?><div class="layui-card-header"><span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span><?php echo htmlentities((string) lang($title)); ?><div class="pull-right"><!--<?php if(auth("add")): ?>--><button data-table-id="AiModelTable" data-modal='<?php echo url("add"); ?>' class='layui-btn layui-btn-sm layui-btn-primary'>添加AI模型</button><!--<?php endif; ?>--><!--<?php if(auth("batchHealthCheck")): ?>--><button data-action='<?php echo url("batchHealthCheck"); ?>' data-confirm="确定要批量检测所有模型健康状态吗？" class='layui-btn layui-btn-sm layui-btn-warm'>批量健康检测</button><!--<?php endif; ?>--><!--<?php if(auth("remove")): ?>--><button data-table-id="AiModelTable" data-action='<?php echo url("remove"); ?>' data-rule="id#{id}" data-confirm="确定要批量删除AI模型吗？" class='layui-btn layui-btn-sm layui-btn-primary'>批量删除</button><!--<?php endif; ?>--></div></div><?php endif; ?><div class="layui-card-line"></div><div class="layui-card-body"><div class="layui-card-table"><?php if(!(empty($showErrorMessage) || (($showErrorMessage instanceof \think\Collection || $showErrorMessage instanceof \think\Paginator ) && $showErrorMessage->isEmpty()))): ?><div class="think-box-notify" type="error"><b><?php echo lang('系统提示：'); ?></b><span><?php echo $showErrorMessage; ?></span></div><?php endif; ?><div class="layui-card"><div class="layui-card-body"><form class="layui-form layui-form-pane form-search" action="<?php echo request()->url(); ?>" onsubmit="return false"><div class="layui-form-item layui-inline"><label class="layui-form-label">模型名称</label><div class="layui-input-inline"><input name="name" value="<?php echo htmlentities((string) (isset($get['name']) && ($get['name'] !== '')?$get['name']:'')); ?>" placeholder="请输入模型名称" class="layui-input"></div></div><div class="layui-form-item layui-inline"><label class="layui-form-label">提供商</label><div class="layui-input-inline"><select name="provider" class="layui-select"><option value="">全部提供商</option><option value="openai" <?php if(isset($get['provider']) and $get['provider'] == 'openai'): ?>selected<?php endif; ?>>OpenAI</option><option value="baidu" <?php if(isset($get['provider']) and $get['provider'] == 'baidu'): ?>selected<?php endif; ?>>百度</option><option value="aliyun" <?php if(isset($get['provider']) and $get['provider'] == 'aliyun'): ?>selected<?php endif; ?>>阿里云</option><option value="tencent" <?php if(isset($get['provider']) and $get['provider'] == 'tencent'): ?>selected<?php endif; ?>>腾讯云</option><option value="zhipu" <?php if(isset($get['provider']) and $get['provider'] == 'zhipu'): ?>selected<?php endif; ?>>智谱AI</option><option value="moonshot" <?php if(isset($get['provider']) and $get['provider'] == 'moonshot'): ?>selected<?php endif; ?>>Moonshot</option><option value="other" <?php if(isset($get['provider']) and $get['provider'] == 'other'): ?>selected<?php endif; ?>>其他</option></select></div></div><div class="layui-form-item layui-inline"><label class="layui-form-label">健康状态</label><div class="layui-input-inline"><select name="health_status" class="layui-select"><option value="">全部状态</option><option value="1" <?php if(isset($get['health_status']) and $get['health_status'] == '1'): ?>selected<?php endif; ?>>健康</option><option value="0" <?php if(isset($get['health_status']) and $get['health_status'] == '0'): ?>selected<?php endif; ?>>异常</option></select></div></div><div class="layui-form-item layui-inline"><button class="layui-btn layui-btn-primary" type="submit">搜 索</button></div></form><table id="AiModelTable" data-url="<?php echo request()->url(); ?>" data-target-search="form.form-search"></table></div></div></div></div><script>
    $(function () {
        // 初始化表格组件
        $('#AiModelTable').layTable({
            even: true, height: 'full',
            sort: {field: 'priority desc,id', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'priority', title: '优先级', width: 80, align: 'center', sort: true, templet: '#PriorityInputTpl'},
                {field: 'name', title: '模型名称', width: '15%', align: 'left'},
                {field: 'provider_text', title: '提供商', width: 100, align: 'center'},
                {field: 'model_code', title: '模型代码', width: '15%', align: 'center'},
                {field: 'max_tokens', title: '最大Token', width: 100, align: 'center'},
                {field: 'cost_text', title: '成本', width: 120, align: 'center'},
                {field: 'health_status', title: '健康状态', width: 100, align: 'center', templet: '#HealthStatusTpl'},
                {field: 'status', title: '状态', width: 80, align: 'center', templet: '#StatusSwitchTpl'},
                {field: 'last_health_check', title: '最后检测', width: 170, align: 'center'},
                {toolbar: '#toolbar', align: 'center', width: 180, title: '操作', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("<?php echo url('state'); ?>", data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#AiModelTable').trigger('reload');
                });
                return false;
            }, false);
        });
    });
</script><!-- 优先级输入模板 --><script type="text/html" id="PriorityInputTpl"><input type="number" min="0" data-blur-number="0" data-action-blur="<?php echo sysuri(); ?>" data-value="id#{{d.id}};action#priority;priority#{value}" data-loading="false" value="{{d.priority}}" class="layui-input text-center"></script><!-- 健康状态显示模板 --><script type="text/html" id="HealthStatusTpl">
    {{# if(d.health_status == 1) { }}
        <span class="layui-badge layui-bg-green">健康</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-red">异常</span>
    {{# } }}
</script><!-- 数据状态切换模板 --><script type="text/html" id="StatusSwitchTpl"><!--<?php if(auth("state")): ?>--><input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="StatusSwitch" {{-d.status>0?'checked':''}}><!--<?php else: ?>-->
    {{-d.status ? '<b class="color-green">启用</b>' : '<b class="color-red">禁用</b>'}}
    <!--<?php endif; ?>--></script><!-- 数据操作工具条模板 --><script type="text/html" id="toolbar"><!--<?php if(auth('edit')): ?>--><a class="layui-btn layui-btn-primary layui-btn-sm" data-event-dbclick data-title="编辑模型" data-modal='<?php echo url("edit"); ?>?id={{d.id}}'>编辑</a><!--<?php endif; ?>--><!--<?php if(auth('healthCheck')): ?>--><a class="layui-btn layui-btn-sm" data-confirm="确定要检测该模型健康状态吗？" data-action="<?php echo url('healthCheck'); ?>" data-value="id#{{d.id}}">健康检测</a><!--<?php endif; ?>--><!--<?php if(auth('testConnection')): ?>--><a class="layui-btn layui-btn-warm layui-btn-sm" data-confirm="确定要测试连接吗？" data-action="<?php echo url('testConnection'); ?>" data-value="id#{{d.id}}">测试连接</a><!--<?php endif; ?>--><!--<?php if(auth("remove")): ?>--><a class="layui-btn layui-btn-danger layui-btn-sm" data-confirm="确定要删除该模型吗?" data-action="<?php echo url('remove'); ?>" data-value="id#{{d.id}}">删除</a><!--<?php endif; ?>--></script></div>