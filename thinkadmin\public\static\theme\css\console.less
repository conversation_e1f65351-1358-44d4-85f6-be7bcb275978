@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

::selection {
  color: #fff !important;
  background-color: #ec494e !important
}

::-moz-selection {
  color: #fff !important;
  background-color: #ec494e !important
}

::-webkit-scrollbar-track {
  background: #ccc !important
}

::-webkit-scrollbar-thumb {
  background-color: #666 !important
}

::-webkit-input-placeholder {
  color: #aaa
}

:-webkit-autofill {
  &, &:hover, &:focus, &:active {
    box-shadow: 0 2px 3px 0 rgba(0, 0, 0, .1) inset !important;
    -webkit-transition: color 9999s ease-out, background-color 9999s ease-out !important;
    -webkit-transition-delay: 9999s !important;
    -webkit-text-fill-color: #333 !important
  }
}

body {
  color: #333;
  font-size: 12px;

  a {
    color: #06C;
    cursor: pointer;

    &:hover {
      color: #039;
    }
  }

  input::-ms-clear {
    display: none
  }
}

@import '_custom.less';
@import '_display.less';
@import '_layout.less';

@import '_layout_1_red.less';
@import '_layout_1_blue.less';
@import '_layout_1_black.less';
@import '_layout_1_green.less';

@import '_layout_2_red.less';
@import '_layout_2_blue.less';
@import '_layout_2_black.less';
@import '_layout_2_green.less';

@import '_layout_white.less';