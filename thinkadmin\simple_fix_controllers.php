<?php

/**
 * 简单修复控制器 - 移除所有try-catch，使用原始方法
 */

echo "=== 简单修复控制器 ===\n\n";

$controllersToFix = [
    'PromptTemplate' => [
        'title' => '提示词模板管理',
        'model' => 'PromptTemplateModel'
    ],
    'OutlineTemplate' => [
        'title' => '大纲模板管理', 
        'model' => 'OutlineTemplateModel'
    ],
    'RewriteRecord' => [
        'title' => '降重记录管理',
        'model' => 'RewriteRecordModel'
    ],
    'CheckRecord' => [
        'title' => '查重记录管理',
        'model' => 'CheckRecordModel'
    ],
    'ExportTemplate' => [
        'title' => '导出模板管理',
        'model' => 'ExportTemplateModel'
    ],
    'VipPackage' => [
        'title' => 'VIP套餐管理',
        'model' => 'VipPackageModel'
    ],
    'Order' => [
        'title' => '订单管理',
        'model' => 'OrderModel'
    ],
    'EmailConfig' => [
        'title' => '邮件配置管理',
        'model' => 'EmailConfigModel'
    ],
    'WebhookConfig' => [
        'title' => 'Webhook配置管理',
        'model' => 'WebhookConfigModel'
    ],
    'ContentFilter' => [
        'title' => '内容过滤管理',
        'model' => 'ContentFilterModel'
    ],
    'RewriteModel' => [
        'title' => '降重模型管理',
        'model' => 'RewriteModelModel'
    ]
];

$fixedCount = 0;

foreach ($controllersToFix as $controller => $config) {
    $filePath = "app/admin/controller/{$controller}.php";
    
    echo "修复控制器: {$controller}\n";
    
    if (!file_exists($filePath)) {
        echo "  ⚠️  文件不存在，跳过\n";
        continue;
    }
    
    $content = file_get_contents($filePath);
    
    // 简单的替换策略：使用原始的mQuery方法，不添加try-catch
    $newIndexMethod = "    public function index()
    {
        {$config['model']}::mQuery()->layTable(function () {
            \$this->title = '{$config['title']}';
        }, static function (QueryHelper \$query) {
            \$query->like('name,title,description')->equal('status');
            \$query->dateBetween('create_time');
            \$query->order('id desc');
        });
    }";
    
    // 查找并替换index方法
    $pattern = '/public function index\(\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}/s';
    
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, $newIndexMethod, $content);
        
        // 清理可能残留的try-catch块
        $content = preg_replace('/\s*\/\/ QueryHelper修复[^\n]*\n/', '', $content);
        $content = preg_replace('/\s*try\s*\{\s*\n/', '', $content);
        $content = preg_replace('/\s*\}\s*catch\s*\([^)]*\)\s*\{[^}]*\}\s*/', '', $content);
        
        // 清理多余的空行
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
        
        file_put_contents($filePath, $content);
        
        // 检查语法
        $output = [];
        $returnCode = 0;
        exec("php -l {$filePath} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ 修复成功\n";
            $fixedCount++;
        } else {
            echo "  ❌ 仍有语法错误: " . implode(' ', $output) . "\n";
        }
    } else {
        echo "  ⚠️  未找到index方法\n";
    }
}

echo "\n=== 修复完成 ===\n";
echo "成功修复: {$fixedCount} 个控制器\n";

// 最终检查
echo "\n=== 最终语法检查 ===\n";
foreach (array_keys($controllersToFix) as $controller) {
    $filePath = "app/admin/controller/{$controller}.php";
    
    if (!file_exists($filePath)) {
        continue;
    }
    
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ {$controller}.php - 语法正确\n";
    } else {
        echo "❌ {$controller}.php - 仍有语法错误\n";
    }
}

echo "\n=== 全部完成 ===\n";
