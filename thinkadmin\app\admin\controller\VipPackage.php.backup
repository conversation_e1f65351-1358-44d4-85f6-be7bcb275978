<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\Package;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * VIP套餐管理
 * @class VipPackage
 * @package app\admin\controller
 */
class VipPackage extends Controller
{
    /**
     * VIP套餐管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        VipPackageModel::mQuery()->layTable(function () {
            $this->title = 'VIP套餐管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示VIP相关的套餐
            $query->where('type', 'in', ['vip', 'combo']);
            $query->like('name,description')->equal('type,status,is_hot');
            $query->dateBetween('createtime,updatetime');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加VIP套餐
     * @auth true
     */public function add()
    {
        Package::mForm('form');
    }

    /**
     * 编辑VIP套餐
     * @auth true
     */
    public function edit()
    {
        Package::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = [
                'vip' => 'VIP套餐',
                'combo' => '组合套餐'
            ];
        } else {
            // 限制只能创建VIP相关的套餐类型
            if (!in_array($vo['type'], ['vip', 'combo'])) {
                $this->error('只能创建VIP套餐或组合套餐！');
            }
            
            // 验证套餐数据
            $errors = Package::validatePackageData($vo);
            if (!empty($errors)) {
                $this->error(implode('；', $errors));
            }
            
            // 计算折扣率
            if ($vo['original_price'] > 0) {
                $vo['discount_rate'] = round(($vo['sale_price'] / $vo['original_price']) * 100, 2);
            } else {
                $vo['discount_rate'] = 100.00;
            }
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['createtime'] = date('Y-m-d H:i:s');
            }
            $vo['updatetime'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('套餐保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除VIP套餐
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 检查是否有关联订单
        $orderCount = $package->orders()->count();
        if ($orderCount > 0) {
            $this->error('该套餐已有订单记录，不能删除！');
        }

        if ($package->delete()) {
            $this->success('套餐删除成功！');
        } else {
            $this->error('套餐删除失败！');
        }
    }

    /**
     * 修改VIP套餐状态
     * @auth true
     */
    public function state()
    {
        Package::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 设置热门套餐
     * @auth true
     */
    public function setHot()
    {
        $id = $this->request->post('id', 0);
        $isHot = $this->request->post('is_hot', 0);
        
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        if ($package->save(['is_hot' => $isHot ? 1 : 0])) {
            $this->success($isHot ? '设置热门成功！' : '取消热门成功！');
        } else {
            $this->error('操作失败！');
        }
    }

    /**
     * 复制VIP套餐
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 创建副本
        $copyData = $package->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['is_hot'] = 0;
        $copyData['status'] = 0; // 默认禁用
        $copyData['createtime'] = date('Y-m-d H:i:s');
        $copyData['updatetime'] = date('Y-m-d H:i:s');

        $newPackage = Package::mk()->save($copyData);
        if ($newPackage) {
            $this->success('套餐复制成功！');
        } else {
            $this->error('套餐复制失败！');
        }
    }

    /**
     * 批量设置排序
     * @auth true
     */
    public function sort()
    {
        $ids = $this->request->post('ids', '');
        $sorts = $this->request->post('sorts', '');
        
        if (empty($ids) || empty($sorts)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $sortArray = explode(',', $sorts);
        
        if (count($idArray) !== count($sortArray)) {
            $this->error('参数数量不匹配！');
        }

        $updateData = [];
        foreach ($idArray as $index => $id) {
            $updateData[] = [
                'id' => $id,
                'sort' => intval($sortArray[$index])
            ];
        }

        $result = Package::mk()->saveAll($updateData);
        if ($result) {
            $this->success('排序设置成功！');
        } else {
            $this->error('排序设置失败！');
        }
    }

    /**
     * 套餐详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->with(['orders'])->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 获取销售统计
        $salesCount = $package->getSalesCount();
        $salesAmount = $package->getSalesAmount();
        
        // 获取最近订单
        $recentOrders = $package->orders()
            ->with(['user'])
            ->order('id desc')
            ->limit(10)
            ->select();

        $this->assign('package', $package);
        $this->assign('salesCount', $salesCount);
        $this->assign('salesAmount', $salesAmount);
        $this->assign('recentOrders', $recentOrders);
        $this->assign('title', '套餐详情');
        
        return $this->fetch('vip_package/view');
    }

    /**
     * 获取套餐统计数据
     * @auth true
     */
    public function statistics()
    {
        // 总套餐数（仅VIP相关）
        $totalPackages = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->count();
        
        // 启用的套餐数
        $activePackages = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->where('status', 1)
            ->count();
        
        // 热门套餐数
        $hotPackages = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->where('is_hot', 1)
            ->count();

        // 各类型套餐数
        $typeStats = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();

        // 今日新增套餐数
        $todayPackages = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->whereTime('createtime', 'today')
            ->count();

        // 本月新增套餐数
        $monthPackages = Package::mk()
            ->where('type', 'in', ['vip', 'combo'])
            ->whereTime('createtime', 'month')
            ->count();

        // 销量最好的套餐
        $bestSellingPackage = Package::mk()
            ->with(['orders'])
            ->where('type', 'in', ['vip', 'combo'])
            ->order('id desc')
            ->findOrEmpty();

        $statistics = [
            'total_packages' => $totalPackages,
            'active_packages' => $activePackages,
            'hot_packages' => $hotPackages,
            'today_packages' => $todayPackages,
            'month_packages' => $monthPackages,
            'type_stats' => $typeStats,
            'best_selling_package' => $bestSellingPackage->toArray()
        ];

        return json($statistics);
    }

    /**
     * 导出套餐数据
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的套餐！');
        }

        $idArray = explode(',', $ids);
        $packages = Package::mk()
            ->whereIn('id', $idArray)
            ->where('type', 'in', ['vip', 'combo'])
            ->order('sort asc, id desc')
            ->select();

        if ($packages->isEmpty()) {
            $this->error('没有找到要导出的套餐！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '套餐名称', '套餐类型', '原价', '售价', '折扣率', 'VIP天数', '积分', '写作配额', '降重配额', '查重配额', '是否热门', '状态', '创建时间'];
        
        foreach ($packages as $package) {
            $exportData[] = [
                $package->id,
                $package->name,
                $package->type_text,
                $package->original_price,
                $package->sale_price,
                $package->discount_rate . '%',
                $package->vip_days,
                $package->credits,
                $package->writing_quota ?: '无限制',
                $package->rewrite_quota ?: '无限制',
                $package->check_quota ?: '无限制',
                $package->is_hot_text,
                $package->status_text,
                $package->createtime
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 批量删除套餐
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的套餐！');
        }

        $idArray = explode(',', $ids);
        
        // 检查是否有关联订单
        $orderCount = Package::mk()
            ->with(['orders'])
            ->whereIn('id', $idArray)
            ->select()
            ->sum(function($package) {
                return $package->orders()->count();
            });
            
        if ($orderCount > 0) {
            $this->error('选中的套餐中有已关联订单的记录，不能删除！');
        }
        
        $result = Package::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'VipPackage管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
