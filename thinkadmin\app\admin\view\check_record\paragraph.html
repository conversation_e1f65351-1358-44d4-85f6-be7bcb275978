{extend name="../../admin/view/main/layout.html" /}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list margin-right-5"></span>
        段落查重详情 - {$task.title}
        <div class="pull-right">
            <button class="layui-btn layui-btn-sm" onclick="history.back()">
                <i class="layui-icon layui-icon-return"></i> 返回
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 统计信息 -->
        <div class="layui-row layui-col-space15 margin-bottom-15">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body text-center">
                        <div class="font-s24 color-green">{$stats.total_paragraphs}</div>
                        <div class="color-desc">总段落数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body text-center">
                        <div class="font-s24 color-red">{$stats.high_similarity_count}</div>
                        <div class="color-desc">高相似段落</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body text-center">
                        <div class="font-s24 color-orange">{$stats.avg_similarity}%</div>
                        <div class="color-desc">平均相似度</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body text-center">
                        <div class="font-s24 color-blue">{$stats.total_sources}</div>
                        <div class="color-desc">匹配来源数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选工具 -->
        <div class="layui-card margin-bottom-15">
            <div class="layui-card-body">
                <form class="layui-form layui-form-pane" lay-filter="filter-form">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">相似度筛选</label>
                            <div class="layui-input-inline">
                                <select name="similarity_filter" lay-filter="similarity_filter">
                                    <option value="">全部段落</option>
                                    <option value="high">高相似度(>30%)</option>
                                    <option value="medium">中等相似度(15-30%)</option>
                                    <option value="low">低相似度(<15%)</option>
                                    <option value="need_modify">需要修改</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">排序方式</label>
                            <div class="layui-input-inline">
                                <select name="sort_order" lay-filter="sort_order">
                                    <option value="index_asc">按段落顺序</option>
                                    <option value="similarity_desc">相似度从高到低</option>
                                    <option value="similarity_asc">相似度从低到高</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="button" class="layui-btn" lay-submit lay-filter="filter-submit">筛选</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="resetFilter()">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 段落列表 -->
        <div class="layui-card">
            <div class="layui-card-header">
                段落详情列表
                <span class="layui-badge layui-bg-blue margin-left-10" id="paragraph-count">{$paragraphs|count} 个段落</span>
            </div>
            <div class="layui-card-body">
                <div id="paragraph-list">
                    {volist name="paragraphs" id="paragraph"}
                    <div class="paragraph-item" 
                         data-similarity="{$paragraph.similarity_rate}" 
                         data-index="{$paragraph.paragraph_index}"
                         data-need-modify="{$paragraph.needsModification() ? 1 : 0}">
                        <div class="layui-panel">
                            <div class="layui-panel-header">
                                <div class="layui-row">
                                    <div class="layui-col-md8">
                                        <strong>段落 {$paragraph.paragraph_index}</strong>
                                        <span class="margin-left-10 color-desc">字数: {$paragraph.original_text|mb_strlen}</span>
                                    </div>
                                    <div class="layui-col-md4 text-right">
                                        <span class="layui-badge {if condition='$paragraph.similarity_rate > 30'}layui-bg-red{elseif condition='$paragraph.similarity_rate > 15'}layui-bg-orange{else}layui-bg-green{/if}">
                                            相似度: {$paragraph.similarity_rate}%
                                        </span>
                                        {if condition="$paragraph.needsModification()"}
                                        <span class="layui-badge layui-bg-red margin-left-5">需修改</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                            <div class="layui-panel-body">
                                <!-- 原文内容 -->
                                <div class="margin-bottom-15">
                                    <h4 class="margin-bottom-10">原文内容</h4>
                                    <div class="original-text" style="background: #f8f8f8; padding: 15px; border-radius: 4px; line-height: 1.6;">
                                        {$paragraph.original_text}
                                    </div>
                                </div>

                                <!-- 匹配来源 -->
                                {if condition="!empty($paragraph.matched_sources)"}
                                <div class="matched-sources">
                                    <h4 class="margin-bottom-10">
                                        匹配来源 
                                        <span class="layui-badge layui-bg-blue">{$paragraph.matched_sources|count} 个来源</span>
                                    </h4>
                                    <div class="layui-collapse" lay-accordion="">
                                        {volist name="paragraph.matched_sources" id="source" key="source_index"}
                                        <div class="layui-colla-item">
                                            <h2 class="layui-colla-title">
                                                来源 {$source_index}: {$source.title|default='未知来源'|mb_substr=0,50,'utf-8'}
                                                <span class="layui-badge layui-bg-orange margin-left-10">相似度: {$source.similarity}%</span>
                                            </h2>
                                            <div class="layui-colla-content">
                                                <div class="layui-row layui-col-space15">
                                                    <div class="layui-col-md12">
                                                        <p><strong>来源标题:</strong> {$source.title|default='未知'}</p>
                                                        <p><strong>来源URL:</strong> 
                                                            {if condition="!empty($source.url)"}
                                                                <a href="{$source.url}" target="_blank" class="color-blue">{$source.url}</a>
                                                            {else}
                                                                <span class="color-desc">未知</span>
                                                            {/if}
                                                        </p>
                                                        {if condition="!empty($source.author)"}
                                                        <p><strong>作者:</strong> {$source.author}</p>
                                                        {/if}
                                                        {if condition="!empty($source.publish_date)"}
                                                        <p><strong>发布时间:</strong> {$source.publish_date}</p>
                                                        {/if}
                                                    </div>
                                                    <div class="layui-col-md12 margin-top-10">
                                                        <p><strong>匹配文本:</strong></p>
                                                        <div class="matched-text" style="background: #fff2e8; padding: 12px; border-radius: 4px; border-left: 4px solid #ff9800; line-height: 1.6;">
                                                            {$source.matched_text}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    </div>
                                </div>
                                {else}
                                <div class="text-center color-desc padding-20">
                                    <i class="layui-icon layui-icon-face-smile font-s20"></i>
                                    <br>该段落未发现相似内容
                                </div>
                                {/if}

                                <!-- 操作按钮 -->
                                <div class="text-right margin-top-15">
                                    <button class="layui-btn layui-btn-xs" onclick="copyText('paragraph-{$paragraph.id}')">
                                        <i class="layui-icon layui-icon-file"></i> 复制原文
                                    </button>
                                    {if condition="$paragraph.needsModification()"}
                                    <button class="layui-btn layui-btn-xs layui-btn-warm" onclick="suggestModification({$paragraph.id})">
                                        <i class="layui-icon layui-icon-edit"></i> 修改建议
                                    </button>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>

                <!-- 空状态 -->
                <div id="empty-state" class="text-center color-desc padding-50" style="display: none;">
                    <i class="layui-icon layui-icon-face-cry font-s48"></i>
                    <br><br>
                    没有找到符合条件的段落
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'element', 'layer'], function(){
    var form = layui.form;
    var element = layui.element;
    var layer = layui.layer;
    
    // 筛选功能
    form.on('submit(filter-submit)', function(data){
        filterParagraphs(data.field);
        return false;
    });
    
    // 筛选段落
    function filterParagraphs(filters) {
        var items = document.querySelectorAll('.paragraph-item');
        var visibleCount = 0;
        
        items.forEach(function(item) {
            var similarity = parseFloat(item.dataset.similarity);
            var needModify = parseInt(item.dataset.needModify);
            var show = true;
            
            // 相似度筛选
            if (filters.similarity_filter) {
                switch(filters.similarity_filter) {
                    case 'high':
                        show = similarity > 30;
                        break;
                    case 'medium':
                        show = similarity >= 15 && similarity <= 30;
                        break;
                    case 'low':
                        show = similarity < 15;
                        break;
                    case 'need_modify':
                        show = needModify === 1;
                        break;
                }
            }
            
            if (show) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });
        
        // 更新计数
        document.getElementById('paragraph-count').textContent = visibleCount + ' 个段落';
        
        // 显示/隐藏空状态
        var emptyState = document.getElementById('empty-state');
        var paragraphList = document.getElementById('paragraph-list');
        if (visibleCount === 0) {
            emptyState.style.display = 'block';
            paragraphList.style.display = 'none';
        } else {
            emptyState.style.display = 'none';
            paragraphList.style.display = 'block';
        }
        
        // 排序
        if (filters.sort_order && visibleCount > 0) {
            sortParagraphs(filters.sort_order);
        }
    }
    
    // 排序段落
    function sortParagraphs(order) {
        var container = document.getElementById('paragraph-list');
        var items = Array.from(container.querySelectorAll('.paragraph-item:not([style*="display: none"])'));
        
        items.sort(function(a, b) {
            switch(order) {
                case 'similarity_desc':
                    return parseFloat(b.dataset.similarity) - parseFloat(a.dataset.similarity);
                case 'similarity_asc':
                    return parseFloat(a.dataset.similarity) - parseFloat(b.dataset.similarity);
                case 'index_asc':
                default:
                    return parseInt(a.dataset.index) - parseInt(b.dataset.index);
            }
        });
        
        // 重新排列DOM元素
        items.forEach(function(item) {
            container.appendChild(item);
        });
    }
    
    // 重置筛选
    window.resetFilter = function() {
        form.val('filter-form', {
            similarity_filter: '',
            sort_order: 'index_asc'
        });
        
        var items = document.querySelectorAll('.paragraph-item');
        items.forEach(function(item) {
            item.style.display = 'block';
        });
        
        document.getElementById('paragraph-count').textContent = items.length + ' 个段落';
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('paragraph-list').style.display = 'block';
        
        sortParagraphs('index_asc');
    };
    
    // 复制文本
    window.copyText = function(id) {
        var element = document.querySelector('[data-id="' + id + '"] .original-text');
        if (!element) {
            // 如果没有找到特定元素，尝试通过其他方式获取
            var paragraphElement = document.querySelector('.paragraph-item[data-index="' + id.split('-')[1] + '"] .original-text');
            if (paragraphElement) {
                element = paragraphElement;
            }
        }
        
        if (element) {
            var text = element.textContent || element.innerText;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    layer.msg('文本已复制到剪贴板', {icon: 1});
                });
            } else {
                // 兼容旧浏览器
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                layer.msg('文本已复制到剪贴板', {icon: 1});
            }
        } else {
            layer.msg('复制失败', {icon: 2});
        }
    };
    
    // 修改建议
    window.suggestModification = function(paragraphId) {
        layer.open({
            type: 2,
            title: '修改建议',
            area: ['80%', '70%'],
            content: '{:url("suggest")}?paragraph_id=' + paragraphId
        });
    };
});
</script>

<style>
.paragraph-item {
    margin-bottom: 20px;
}

.paragraph-item:last-child {
    margin-bottom: 0;
}

.original-text {
    user-select: text;
    cursor: text;
}

.matched-text {
    user-select: text;
    cursor: text;
}

.font-s20 {
    font-size: 20px;
}

.font-s24 {
    font-size: 24px;
}

.font-s48 {
    font-size: 48px;
}

.color-green {
    color: #5FB878;
}

.color-red {
    color: #FF5722;
}

.color-orange {
    color: #FF9800;
}

.color-blue {
    color: #1E9FFF;
}

.color-desc {
    color: #999;
}

.padding-50 {
    padding: 50px;
}
</style>
{/block}
