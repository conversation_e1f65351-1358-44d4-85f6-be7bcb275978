/*!
 * Compressor.js v1.2.1
 * https://fengyuanchen.github.io/compressorjs
 *
 * Copyright 2018-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2023-02-28T14:09:41.732Z
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Compressor=t()}(this,function(){"use strict";function t(t,e){var r,i=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)),i}function n(i){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?t(Object(n),!0).forEach(function(e){var t,r;t=i,r=n[e=e],(e=o(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach(function(e){Object.defineProperty(i,e,Object.getOwnPropertyDescriptor(n,e))})}return i}function a(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,o(i.key),i)}}function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r,i=arguments[t];for(r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e}).apply(this,arguments)}function o(e){e=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);if("object"!=typeof(r=r.call(e,t||"default")))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==typeof e?e:String(e)}function O(e){return 0<e&&e<1/0}var e,r,l,c,h,u,f,d,i={exports:{}},j=(e=i,"undefined"!=typeof window&&(l=(r=window).HTMLCanvasElement&&r.HTMLCanvasElement.prototype,c=r.Blob&&function(){try{return Boolean(new Blob)}catch(e){return!1}}(),h=c&&r.Uint8Array&&function(){try{return 100===new Blob([new Uint8Array(100)]).size}catch(e){return!1}}(),u=r.BlobBuilder||r.WebKitBlobBuilder||r.MozBlobBuilder||r.MSBlobBuilder,f=/^data:((.*?)(;charset=.*?)?)(;base64)?,/,d=(c||u)&&r.atob&&r.ArrayBuffer&&r.Uint8Array&&function(e){var t,r,i,n,a,o=e.match(f);if(!o)throw new Error("invalid data URI");for(t=o[2]?o[1]:"text/plain"+(o[3]||";charset=US-ASCII"),a=!!o[4],e=e.slice(o[0].length),r=(a?atob:decodeURIComponent)(e),o=new ArrayBuffer(r.length),i=new Uint8Array(o),n=0;n<r.length;n+=1)i[n]=r.charCodeAt(n);return c?new Blob([h?i:o],{type:t}):((a=new u).append(o),a.getBlob(t))},r.HTMLCanvasElement&&!l.toBlob&&(l.mozGetAsFile?l.toBlob=function(e,t,r){var i=this;setTimeout(function(){r&&l.toDataURL&&d?e(d(i.toDataURL(t,r))):e(i.mozGetAsFile("blob",t))})}:l.toDataURL&&d&&(l.msToBlob?l.toBlob=function(e,t,r){var i=this;setTimeout(function(){(t&&"image/png"!==t||r)&&l.toDataURL&&d?e(d(i.toDataURL(t,r))):e(i.msToBlob(t))})}:l.toBlob=function(e,t,r){var i=this;setTimeout(function(){e(d(i.toDataURL(t,r)))})})),e.exports?e.exports=d:r.dataURLtoBlob=d),i.exports),m={strict:!0,checkOrientation:!0,retainExif:!1,maxWidth:1/0,maxHeight:1/0,minWidth:0,minHeight:0,width:void 0,height:void 0,resize:"none",quality:.8,mimeType:"auto",convertTypes:["image/png"],convertSize:5e6,beforeDraw:null,drew:null,success:null,error:null},b="undefined"!=typeof window&&void 0!==window.document?window:{},p=Array.prototype.slice;function x(e){return Array.from?Array.from(e):p.call(e)}var g=/^image\/.+$/;function T(e){return g.test(e)}var y=String.fromCharCode;var w=b.btoa;function A(e,t){for(var r=[],i=new Uint8Array(e);0<i.length;)r.push(y.apply(null,x(i.subarray(0,8192)))),i=i.subarray(8192);return"data:".concat(t,";base64,").concat(w(r.join("")))}function v(e){var t,r,i,n,a,o,l,s=new DataView(e);try{if(255===s.getUint8(0)&&216===s.getUint8(1))for(var c=s.byteLength,h=2;h+1<c;){if(255===s.getUint8(h)&&225===s.getUint8(h+1)){r=h;break}h+=1}if(i=r&&(n=r+10,"Exif"===function(e,t,r){var i,n="";for(r+=t,i=t;i<r;i+=1)n+=y(e.getUint8(i));return n}(s,r+4,4))&&((l=18761===(a=s.getUint16(n)))||19789===a)&&42===s.getUint16(n+2,l)&&8<=(o=s.getUint32(n+4,l))?n+o:i)for(var u,f=s.getUint16(i,l),d=0;d<f;d+=1)if(u=i+12*d+2,274===s.getUint16(u,l)){u+=8,t=s.getUint16(u,l),s.setUint16(u,1,l);break}}catch(e){t=1}return t}var B=/\.\d*(?:0|9){12}\d*$/;function R(e,t){t=1<arguments.length&&void 0!==t?t:1e11;return B.test(e)?Math.round(e*t)/t:e}function E(e,t){var r,i=e.aspectRatio,n=e.height,e=e.width,t=1<arguments.length&&void 0!==t?t:"none",a=O(e),o=O(n);return a&&o?(r=n*i,("contain"===t||"none"===t)&&e<r||"cover"===t&&r<e?n=e/i:e=n*i):a?n=e/i:o&&(e=n*i),{width:e,height:n}}var U=b.ArrayBuffer,k=b.FileReader,M=b.URL||b.webkitURL,D=/\.\w+$/,L=b.Compressor;return function(){function r(e,t){if(!(this instanceof r))throw new TypeError("Cannot call a class as a function");this.file=e,this.exif=[],this.image=new Image,this.options=n(n({},m),t),this.aborted=!1,this.result=null,this.init()}var e,t,i;return e=r,i=[{key:"noConflict",value:function(){return window.Compressor=L,r}},{key:"setDefaults",value:function(e){s(m,e)}}],(t=[{key:"init",value:function(){var i,n,a,e,o=this,l=this.file,t=this.options;e=l,"undefined"!=typeof Blob&&(e instanceof Blob||"[object Blob]"===Object.prototype.toString.call(e))?T(i=l.type)?M&&k?(U||(t.checkOrientation=!1,t.retainExif=!1),n=(e="image/jpeg"===i)&&t.checkOrientation,a=e&&t.retainExif,!M||n||a?(e=new k,(this.reader=e).onload=function(e){var e=e.target.result,t={},r=1;n&&1<(r=v(e))&&s(t,function(e){var t=0,r=1,i=1;switch(e){case 2:r=-1;break;case 3:t=-180;break;case 4:i=-1;break;case 5:t=90,i=-1;break;case 6:t=90;break;case 7:t=90,r=-1;break;case 8:t=-90}return{rotate:t,scaleX:r,scaleY:i}}(r)),a&&(o.exif=function(e){for(var t=x(new Uint8Array(e)),r=t.length,i=[],n=0;n+3<r;){var a=t[n],o=t[n+1];if(255===a&&218===o)break;255===a&&216===o?n+=2:(a=n+(256*t[n+2]+t[n+3])+2,o=t.slice(n,a),i.push(o),n=a)}return i.reduce(function(e,t){return 255===t[0]&&225===t[1]?e.concat(t):e},[])}(e)),t.url=n||a?!M||1<r?A(e,i):M.createObjectURL(l):e,o.load(t)},e.onabort=function(){o.fail(new Error("Aborted to read the image with FileReader."))},e.onerror=function(){o.fail(new Error("Failed to read the image with FileReader."))},e.onloadend=function(){o.reader=null},n||a?e.readAsArrayBuffer(l):e.readAsDataURL(l)):this.load({url:M.createObjectURL(l)})):this.fail(new Error("The current browser does not support image compression.")):this.fail(new Error("The first argument must be an image File or Blob object.")):this.fail(new Error("The first argument must be a File or Blob object."))}},{key:"load",value:function(e){var t=this,r=this.file,i=this.image;i.onload=function(){t.draw(n(n({},e),{},{naturalWidth:i.naturalWidth,naturalHeight:i.naturalHeight}))},i.onabort=function(){t.fail(new Error("Aborted to load the image."))},i.onerror=function(){t.fail(new Error("Failed to load the image."))},b.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(b.navigator.userAgent)&&(i.crossOrigin="anonymous"),i.alt=r.name,i.src=e.url}},{key:"draw",value:function(e){var n=this,a=e.naturalWidth,o=e.naturalHeight,t=e.rotate,t=void 0===t?0:t,r=e.scaleX,r=void 0===r?1:r,e=e.scaleY,e=void 0===e?1:e,i=this.file,l=this.image,s=this.options,c=document.createElement("canvas"),h=c.getContext("2d"),u=Math.abs(t)%180==90,f=("contain"===s.resize||"cover"===s.resize)&&O(s.width)&&O(s.height),d=Math.max(s.maxWidth,0)||1/0,m=Math.max(s.maxHeight,0)||1/0,b=Math.max(s.minWidth,0)||0,p=Math.max(s.minHeight,0)||0,g=a/o,y=s.width,w=s.height,v=(u&&(d=(v=[m,d])[0],m=v[1],b=(v=[p,b])[0],p=v[1],y=(v=[w,y])[0],w=v[1]),E({aspectRatio:g=f?y/w:g,width:d,height:m},"contain")),v=(d=v.width,m=v.height,E({aspectRatio:g,width:b,height:p},"cover")),v=(b=v.width,p=v.height,w=f?(y=(v=E({aspectRatio:g,width:y,height:w},s.resize)).width,v.height):(y=void 0===(B=(v=E({aspectRatio:g,width:y,height:w})).width)?a:B,void 0===(B=v.height)?o:B),-(y=Math.floor(R(Math.min(Math.max(y,b),d))))/2),B=-(w=Math.floor(R(Math.min(Math.max(w,p),m))))/2,b=y,d=w,p=[],m=(f&&(0,m=(g=E({aspectRatio:g,width:m=a,height:f=o},{contain:"cover",cover:"contain"}[s.resize])).width,f=g.height,p.push((a-m)/2,(o-f)/2,m,f)),p.push(v,B,b,d),u&&(y=(g=[w,y])[0],w=g[1]),c.width=y,c.height=w,T(s.mimeType)||(s.mimeType=i.type),"transparent"),U=(i.size>s.convertSize&&0<=s.convertTypes.indexOf(s.mimeType)&&(s.mimeType="image/jpeg"),"image/jpeg"===s.mimeType);h.fillStyle=m=U?"#fff":m,h.fillRect(0,0,y,w),s.beforeDraw&&s.beforeDraw.call(this,h,c),this.aborted||(h.save(),h.translate(y/2,w/2),h.rotate(t*Math.PI/180),h.scale(r,e),h.drawImage.apply(h,[l].concat(p)),h.restore(),s.drew&&s.drew.call(this,h,c),this.aborted)||(f=function(e){var i,t,r;n.aborted||(i=function(e){return n.done({naturalWidth:a,naturalHeight:o,result:e})},e&&U&&s.retainExif&&n.exif&&0<n.exif.length?(t=function(e){return i(j(A((e=e,t=n.exif,255!==(r=x(new Uint8Array(e)))[2]||224!==r[3]?e:(e=256*r[4]+r[5],t=[255,216].concat(t,r.slice(4+e)),new Uint8Array(t))),s.mimeType)));var t,r},e.arrayBuffer?e.arrayBuffer().then(t).catch(function(){n.fail(new Error("Failed to read the compressed image with Blob.arrayBuffer()."))}):(r=new k,(n.reader=r).onload=function(e){e=e.target;t(e.result)},r.onabort=function(){n.fail(new Error("Aborted to read the compressed image with FileReader."))},r.onerror=function(){n.fail(new Error("Failed to read the compressed image with FileReader."))},r.onloadend=function(){n.reader=null},r.readAsArrayBuffer(e))):i(e))},c.toBlob?c.toBlob(f,s.mimeType,s.quality):f(j(c.toDataURL(s.mimeType,s.quality))))}},{key:"done",value:function(e){var t=e.naturalWidth,r=e.naturalHeight,e=e.result,i=this.file,n=this.image,a=this.options;M&&0===n.src.indexOf("blob:")&&M.revokeObjectURL(n.src),!e||a.strict&&!a.retainExif&&e.size>i.size&&a.mimeType===i.type&&!(a.width>t||a.height>r||a.minWidth>t||a.minHeight>r||a.maxWidth<t||a.maxHeight<r)?e=i:(n=new Date,e.lastModified=n.getTime(),e.lastModifiedDate=n,e.name=i.name,e.name&&e.type!==i.type&&(e.name=e.name.replace(D,(t=T(t=e.type)?t.substr(6):"",".".concat(t="jpeg"===t?"jpg":t))))),this.result=e,a.success&&a.success.call(this,e)}},{key:"fail",value:function(e){var t=this.options;if(!t.error)throw e;t.error.call(this,e)}},{key:"abort",value:function(){this.aborted||(this.aborted=!0,this.reader?this.reader.abort():this.image.complete?this.fail(new Error("The compression process has been aborted.")):(this.image.onload=null,this.image.onabort()))}}])&&a(e.prototype,t),i&&a(e,i),Object.defineProperty(e,"prototype",{writable:!1}),r}()});