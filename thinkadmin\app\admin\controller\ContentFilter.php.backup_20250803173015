<?php

namespace app\admin\controller;

use app\admin\model\ContentFilterModel;
use think\admin\Controller;

/**
 * 内容过滤管理
 * @class ContentFilter
 * @package app\admin\controller
 */
class ContentFilter extends Controller
{
    /**
     * 内容过滤管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        ContentFilterModel::mQuery($this)->layPage(function () {
            $this->title = '内容过滤管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加内容过滤管理
     * @auth true
     */
    public function add()
    {
        ContentFilterModel::mForm('content_filter/form');
    }

    /**
     * 编辑内容过滤管理
     * @auth true
     */
    public function edit()
    {
        ContentFilterModel::mForm('content_filter/form');
    }

    /**
     * 删除内容过滤管理
     * @auth true
     */
    public function remove()
    {
        ContentFilterModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        ContentFilterModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}