<?php

namespace app\admin\controller;

use app\admin\model\RewriteRecord as RewriteRecordModel;
use think\admin\Controller;

/**
 * 降重记录管理
 * @class RewriteRecord
 * @package app\admin\controller
 */
class RewriteRecord extends Controller
{
    /**
     * 降重记录管理首页
     * @auth true
     * @menu true
     */
    public function index()
    {
        try {
            $this->title = '降重记录管理';
            
            // 构建查询条件
            $map = [];
            $params = $this->request->param();
            
            // 处理搜索条件
            if (!empty($params['name'])) {
                $map[] = ['name', 'like', '%' . $params['name'] . '%'];
            }
            
            if (!empty($params['title'])) {
                $map[] = ['title', 'like', '%' . $params['title'] . '%'];
            }
            
            if (!empty($params['description'])) {
                $map[] = ['description', 'like', '%' . $params['description'] . '%'];
            }
            
            if (isset($params['status']) && $params['status'] !== '') {
                $map['status'] = $params['status'];
            }
            
            if (isset($params['user_id']) && $params['user_id'] !== '') {
                $map['user_id'] = $params['user_id'];
            }
            
            // 时间范围查询
            if (!empty($params['create_time_start'])) {
                $map[] = ['create_time', '>=', $params['create_time_start']];
            }
            if (!empty($params['create_time_end'])) {
                $map[] = ['create_time', '<=', $params['create_time_end'] . ' 23:59:59'];
            }
            
            // 执行查询
            $list = RewriteRecordModel::where($map)
                ->order('id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => $params
                ]);
            
            $this->assign([
                'title' => '降重记录管理',
                'list' => $list,
                'pagehtml' => $list->render(),
                'get' => $params
            ]);
            
            return $this->fetch();
            
        } catch (\Exception $e) {
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加降重记录管理
     * @auth true
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            try {
                $this->validate($data, [
                    'name|名称' => 'require|max:100',
                ]);
                
                RewriteRecordModel::create($data);
                $this->success('添加成功！', '');
                
            } catch (\Exception $e) {
                $this->error('添加失败：' . $e->getMessage());
            }
        }
        
        $this->title = '添加降重记录管理';
        return $this->fetch();
    }
    
    /**
     * 编辑降重记录管理
     * @auth true
     */
    public function edit()
    {
        $id = $this->request->param('id');
        $model = RewriteRecordModel::find($id);
        
        if (!$model) {
            $this->error('记录不存在！');
        }
        
        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            try {
                $this->validate($data, [
                    'name|名称' => 'require|max:100',
                ]);
                
                $model->save($data);
                $this->success('编辑成功！', '');
                
            } catch (\Exception $e) {
                $this->error('编辑失败：' . $e->getMessage());
            }
        }
        
        $this->assign('vo', $model);
        $this->title = '编辑降重记录管理';
        return $this->fetch();
    }
    
    /**
     * 删除降重记录管理
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->param('id');
        
        if (empty($id)) {
            $this->error('参数错误！');
        }
        
        try {
            RewriteRecordModel::destroy($id);
            $this->success('删除成功！');
            
        } catch (\Exception $e) {
            $this->error('删除失败：' . $e->getMessage());
        }
    }
    
    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        $id = $this->request->param('id');
        $status = $this->request->param('status');
        
        if (empty($id)) {
            $this->error('参数错误！');
        }
        
        try {
            RewriteRecordModel::where('id', $id)->update(['status' => $status]);
            $this->success('状态修改成功！');
            
        } catch (\Exception $e) {
            $this->error('状态修改失败：' . $e->getMessage());
        }
    }
}