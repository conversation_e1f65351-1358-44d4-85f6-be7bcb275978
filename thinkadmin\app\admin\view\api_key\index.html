{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        接口密钥管理
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">OpenAI API密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="openai_api_key" value="{$configs.openai_api_key}" placeholder="请输入OpenAI API密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">OpenAI API基础URL</label>
                <div class="layui-input-block">
                    <input type="text" name="openai_api_base" value="{$configs.openai_api_base}" placeholder="请输入OpenAI API基础URL" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">Claude API密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="claude_api_key" value="{$configs.claude_api_key}" placeholder="请输入Claude API密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">百度API密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="baidu_api_key" value="{$configs.baidu_api_key}" placeholder="请输入百度API密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">百度Secret密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="baidu_secret_key" value="{$configs.baidu_secret_key}" placeholder="请输入百度Secret密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">智谱API密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="zhipu_api_key" value="{$configs.zhipu_api_key}" placeholder="请输入智谱API密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">千帆API密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="qianfan_api_key" value="{$configs.qianfan_api_key}" placeholder="请输入千帆API密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">千帆Secret密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="qianfan_secret_key" value="{$configs.qianfan_secret_key}" placeholder="请输入千帆Secret密钥" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info);
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}