<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\User;
use app\admin\model\RechargeRecord as RechargeRecordModel;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 充值记录
 * @class RechargeRecord
 * @package app\admin\controller
 */
class RechargeRecord extends Controller
{
    /**
     * 充值记录
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        RechargeRecordModel::mQuery()->layTable(function () {
            $this->title = '充值记录';
            $this->statusOptions = RechargeRecordModel::getStatusOptions();
            $this->methodOptions = RechargeRecordModel::getMethodOptions();
        }, static function (QueryHelper $query) {
            $query->with(['user']);
            $query->like('order_no,transaction_id,user.username,user.nickname')->equal('status,payment_method,recharge_type');
            $query->dateBetween('create_time,payment_time');
            $query->order('id desc');
        });
    }

    /**
     * 充值详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $record = RechargeRecordModel::mk()->with(['user'])->findOrEmpty($id);
        if ($record->isEmpty()) {
            $this->error('记录不存在！');
        }

        // 获取支付详情
        $paymentDetails = $this->getPaymentDetails($record);
        
        // 获取相关操作日志
        $operationLogs = $this->getOperationLogs($record->id);

        $this->assign('record', $record);
        $this->assign('paymentDetails', $paymentDetails);
        $this->assign('operationLogs', $operationLogs);
        $this->assign('title', '充值详情');
        
        return $this->fetch('recharge_record/view');
    }

    /**
     * 手动确认支付
     * @auth true
     */
    public function confirmPayment()
    {
        $id = $this->request->post('id', 0);
        $remark = $this->request->post('remark', '');
        
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $record = RechargeRecordModel::mk()->with(['user'])->findOrEmpty($id);
        if ($record->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($record->status !== 'pending') {
            $this->error('只能确认待支付状态的记录！');
        }

        // 开始事务
        RechargeRecordModel::mk()->startTrans();
        try {
            // 更新充值记录状态
            $record->save([
                'status' => 'completed',
                'payment_time' => date('Y-m-d H:i:s'),
                'remark' => $remark,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 更新用户积分或VIP
            $this->processRechargeReward($record);
            
            // 记录操作日志
            $this->addOperationLog($record->id, 'confirm_payment', '管理员手动确认支付', $remark);

            RechargeRecordModel::mk()->commit();
            $this->success('支付确认成功！');
        } catch (\Exception $e) {
            RechargeRecordModel::mk()->rollback();
            $this->error('支付确认失败：' . $e->getMessage());
        }
    }

    /**
     * 处理充值奖励
     * @param RechargeRecordModel $record
     */
    private function processRechargeReward(RechargeRecordModel $record): void
    {
        $user = $record->user;
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        switch ($record->recharge_type) {
            case 'credits':
                // 增加积分
                $user->increment('credits', $record->credits_amount);
                break;
                
            case 'vip':
                // 延长VIP
                $currentVipExpire = $user->vip_expire_time ?: date('Y-m-d H:i:s');
                $newVipExpire = date('Y-m-d H:i:s', strtotime($currentVipExpire . ' +' . $record->vip_days . ' days'));
                $user->save([
                    'is_vip' => 1,
                    'vip_expire_time' => $newVipExpire
                ]);
                break;
                
            case 'combo':
                // 组合套餐：积分+VIP
                if ($record->credits_amount > 0) {
                    $user->increment('credits', $record->credits_amount);
                }
                if ($record->vip_days > 0) {
                    $currentVipExpire = $user->vip_expire_time ?: date('Y-m-d H:i:s');
                    $newVipExpire = date('Y-m-d H:i:s', strtotime($currentVipExpire . ' +' . $record->vip_days . ' days'));
                    $user->save([
                        'is_vip' => 1,
                        'vip_expire_time' => $newVipExpire
                    ]);
                }
                break;
        }
    }

    /**
     * 退款处理
     * @auth true
     */
    public function refund()
    {
        $id = $this->request->post('id', 0);
        $refundReason = $this->request->post('refund_reason', '');
        
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        if (empty($refundReason)) {
            $this->error('请填写退款原因！');
        }

        $record = RechargeRecordModel::mk()->with(['user'])->findOrEmpty($id);
        if ($record->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($record->status !== 'completed') {
            $this->error('只能退款已完成的记录！');
        }

        // 开始事务
        RechargeRecordModel::mk()->startTrans();
        try {
            // 更新充值记录状态
            $record->save([
                'status' => 'refunded',
                'refund_time' => date('Y-m-d H:i:s'),
                'refund_reason' => $refundReason,
                'update_time' => date('Y-m-d H:i:s')
            ]);

            // 扣除用户奖励
            $this->processRefundDeduction($record);
            
            // 记录操作日志
            $this->addOperationLog($record->id, 'refund', '管理员处理退款', $refundReason);

            RechargeRecordModel::mk()->commit();
            $this->success('退款处理成功！');
        } catch (\Exception $e) {
            RechargeRecordModel::mk()->rollback();
            $this->error('退款处理失败：' . $e->getMessage());
        }
    }

    /**
     * 处理退款扣除
     * @param RechargeRecordModel $record
     */
    private function processRefundDeduction(RechargeRecordModel $record): void
    {
        $user = $record->user;
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        switch ($record->recharge_type) {
            case 'credits':
                // 扣除积分
                if ($user->credits >= $record->credits_amount) {
                    $user->decrement('credits', $record->credits_amount);
                } else {
                    // 积分不足，设为0
                    $user->save(['credits' => 0]);
                }
                break;
                
            case 'vip':
                // 扣除VIP天数
                if ($user->is_vip && $user->vip_expire_time) {
                    $newVipExpire = date('Y-m-d H:i:s', strtotime($user->vip_expire_time . ' -' . $record->vip_days . ' days'));
                    if ($newVipExpire <= date('Y-m-d H:i:s')) {
                        $user->save([
                            'is_vip' => 0,
                            'vip_expire_time' => null
                        ]);
                    } else {
                        $user->save(['vip_expire_time' => $newVipExpire]);
                    }
                }
                break;
                
            case 'combo':
                // 组合套餐：扣除积分和VIP
                if ($record->credits_amount > 0) {
                    if ($user->credits >= $record->credits_amount) {
                        $user->decrement('credits', $record->credits_amount);
                    } else {
                        $user->save(['credits' => 0]);
                    }
                }
                if ($record->vip_days > 0 && $user->is_vip && $user->vip_expire_time) {
                    $newVipExpire = date('Y-m-d H:i:s', strtotime($user->vip_expire_time . ' -' . $record->vip_days . ' days'));
                    if ($newVipExpire <= date('Y-m-d H:i:s')) {
                        $user->save([
                            'is_vip' => 0,
                            'vip_expire_time' => null
                        ]);
                    } else {
                        $user->save(['vip_expire_time' => $newVipExpire]);
                    }
                }
                break;
        }
    }

    /**
     * 删除记录
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $record = RechargeRecordModel::mk()->findOrEmpty($id);
        if ($record->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($record->status === 'completed') {
            $this->error('已完成的充值记录不能删除！');
        }

        if ($record->delete()) {
            $this->success('记录删除成功！');
        } else {
            $this->error('记录删除失败！');
        }
    }

    /**
     * 充值统计
     * @auth true
     */
    public function statistics()
    {
        // 总充值记录数
        $totalRecords = RechargeRecordModel::mk()->count();
        
        // 总充值金额
        $totalAmount = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->sum('amount') ?: 0;
        
        // 今日充值
        $todayAmount = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->whereTime('payment_time', 'today')
            ->sum('amount') ?: 0;
        
        $todayCount = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->whereTime('payment_time', 'today')
            ->count();
        
        // 本月充值
        $monthAmount = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->whereTime('payment_time', 'month')
            ->sum('amount') ?: 0;
        
        $monthCount = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->whereTime('payment_time', 'month')
            ->count();
        
        // 各状态统计
        $statusStats = RechargeRecordModel::mk()
            ->field('status, COUNT(*) as count, SUM(amount) as total_amount')
            ->group('status')
            ->select()
            ->toArray();
        
        // 各支付方式统计
        $methodStats = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->field('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
            ->group('payment_method')
            ->select()
            ->toArray();
        
        // 各充值类型统计
        $typeStats = RechargeRecordModel::mk()
            ->where('status', 'completed')
            ->field('recharge_type, COUNT(*) as count, SUM(amount) as total_amount')
            ->group('recharge_type')
            ->select()
            ->toArray();
        
        // 最近7天充值趋势
        $weekTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $amount = RechargeRecordModel::mk()
                ->where('status', 'completed')
                ->whereTime('payment_time', $date)
                ->sum('amount') ?: 0;
            $count = RechargeRecordModel::mk()
                ->where('status', 'completed')
                ->whereTime('payment_time', $date)
                ->count();
            
            $weekTrend[] = [
                'date' => $date,
                'amount' => $amount,
                'count' => $count
            ];
        }
        
        // 用户充值排行（前10）
        $userRanking = RechargeRecordModel::mk()
            ->with(['user'])
            ->where('status', 'completed')
            ->field('user_id, COUNT(*) as recharge_count, SUM(amount) as total_amount')
            ->group('user_id')
            ->order('total_amount desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 平均充值金额
        $avgAmount = $totalRecords > 0 ? round($totalAmount / $totalRecords, 2) : 0;
        
        // 成功率
        $completedCount = RechargeRecordModel::mk()->where('status', 'completed')->count();
        $successRate = $totalRecords > 0 ? round(($completedCount / $totalRecords) * 100, 2) : 0;

        $statistics = [
            'total_records' => $totalRecords,
            'total_amount' => $totalAmount,
            'today_amount' => $todayAmount,
            'today_count' => $todayCount,
            'month_amount' => $monthAmount,
            'month_count' => $monthCount,
            'avg_amount' => $avgAmount,
            'success_rate' => $successRate,
            'status_stats' => $statusStats,
            'method_stats' => $methodStats,
            'type_stats' => $typeStats,
            'week_trend' => $weekTrend,
            'user_ranking' => $userRanking
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '充值统计');
        return $this->fetch('recharge_record/statistics');
    }

    /**
     * 获取支付详情
     * @param RechargeRecordModel $record
     * @return array
     */
    private function getPaymentDetails(RechargeRecordModel $record): array
    {
        return [
            'order_no' => $record->order_no,
            'transaction_id' => $record->transaction_id,
            'payment_method' => $record->payment_method,
            'amount' => $record->amount,
            'actual_amount' => $record->actual_amount,
            'discount_amount' => $record->discount_amount,
            'payment_time' => $record->payment_time,
            'status' => $record->status
        ];
    }

    /**
     * 获取操作日志
     * @param int $recordId
     * @return array
     */
    private function getOperationLogs(int $recordId): array
    {
        // 这里应该从实际的日志表中获取数据
        // 暂时返回模拟数据
        return [
            [
                'time' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'action' => 'create',
                'description' => '创建充值记录',
                'operator' => '系统',
                'remark' => ''
            ],
            [
                'time' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'action' => 'payment',
                'description' => '用户发起支付',
                'operator' => '用户',
                'remark' => ''
            ]
        ];
    }

    /**
     * 添加操作日志
     * @param int $recordId
     * @param string $action
     * @param string $description
     * @param string $remark
     */
    private function addOperationLog(int $recordId, string $action, string $description, string $remark = ''): void
    {
        // 这里应该写入到实际的日志表
        $logData = [
            'record_id' => $recordId,
            'action' => $action,
            'description' => $description,
            'operator_id' => session('user.id'),
            'operator_name' => session('user.username'),
            'remark' => $remark,
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 实际项目中应该保存到数据库
        // RechargeOperationLog::mk()->save($logData);
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $record = RechargeRecordModel::mk()->findOrEmpty($id);
            if ($record->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'confirm':
                    if ($record->status === 'pending') {
                        try {
                            $record->save([
                                'status' => 'completed',
                                'payment_time' => date('Y-m-d H:i:s'),
                                'update_time' => date('Y-m-d H:i:s')
                            ]);
                            $this->processRechargeReward($record);
                            $successCount++;
                        } catch (\Exception $e) {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'cancel':
                    if ($record->status === 'pending') {
                        $result = $record->save([
                            'status' => 'cancelled',
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                        if ($result) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'delete':
                    if ($record->status !== 'completed') {
                        if ($record->delete()) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                default:
                    $failCount++;
                    break;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出充值记录
     * @auth true
     */
    public function export()
    {
        $startDate = $this->request->post('start_date', '');
        $endDate = $this->request->post('end_date', '');
        $status = $this->request->post('status', '');
        $method = $this->request->post('payment_method', '');

        $query = RechargeRecordModel::mk()->with(['user']);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        if (!empty($method)) {
            $query->where('payment_method', $method);
        }

        $records = $query->order('id desc')->select();

        if ($records->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '订单号', '用户', '充值类型', '金额', '支付方式', '状态', '创建时间', '支付时间'];
        
        foreach ($records as $record) {
            $exportData[] = [
                $record->id,
                $record->order_no,
                $record->user->username ?? '',
                RechargeRecordModel::getTypeOptions()[$record->recharge_type] ?? $record->recharge_type,
                $record->amount,
                RechargeRecordModel::getMethodOptions()[$record->payment_method] ?? $record->payment_method,
                RechargeRecordModel::getStatusOptions()[$record->status] ?? $record->status,
                $record->create_time,
                $record->payment_time ?: ''
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
}
