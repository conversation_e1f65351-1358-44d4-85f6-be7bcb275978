<?php

// 检查当前系统菜单结构
$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 当前系统菜单结构分析 ===\n\n";
    
    // 获取所有菜单数据
    $stmt = $pdo->query("SELECT id, pid, title, node, icon, sort, status FROM system_menu ORDER BY sort, id");
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 构建菜单树
    $menuTree = [];
    $menuMap = [];
    
    foreach ($menus as $menu) {
        $menuMap[$menu['id']] = $menu;
        if ($menu['pid'] == 0) {
            $menuTree[$menu['id']] = $menu;
            $menuTree[$menu['id']]['children'] = [];
        }
    }
    
    foreach ($menus as $menu) {
        if ($menu['pid'] > 0 && isset($menuTree[$menu['pid']])) {
            $menuTree[$menu['pid']]['children'][] = $menu;
        }
    }
    
    // 显示菜单结构
    foreach ($menuTree as $parentMenu) {
        $status = $parentMenu['status'] ? '✅' : '❌';
        echo "{$status} 【一级菜单】{$parentMenu['title']} (ID: {$parentMenu['id']}, Sort: {$parentMenu['sort']})\n";
        echo "   Node: {$parentMenu['node']}\n";
        echo "   Icon: {$parentMenu['icon']}\n";
        
        if (!empty($parentMenu['children'])) {
            foreach ($parentMenu['children'] as $childMenu) {
                $childStatus = $childMenu['status'] ? '✅' : '❌';
                echo "   {$childStatus} └── {$childMenu['title']} (ID: {$childMenu['id']})\n";
                echo "       Node: {$childMenu['node']}\n";
                echo "       Icon: {$childMenu['icon']}\n";
            }
        }
        echo "\n";
    }
    
    // 分析写作中心模块
    echo "=== 写作中心模块分析 ===\n";
    $writingCenterMenu = null;
    foreach ($menuTree as $menu) {
        if (strpos($menu['title'], '写作') !== false) {
            $writingCenterMenu = $menu;
            break;
        }
    }
    
    if ($writingCenterMenu) {
        echo "写作中心菜单ID: {$writingCenterMenu['id']}\n";
        echo "子菜单数量: " . count($writingCenterMenu['children']) . "\n";
        echo "子菜单列表:\n";
        foreach ($writingCenterMenu['children'] as $child) {
            echo "- {$child['title']} -> {$child['node']}\n";
        }
    } else {
        echo "未找到写作中心菜单\n";
    }
    
    // 检查所有控制器文件
    echo "\n=== 所有控制器文件检查 ===\n";
    $controllerDir = __DIR__ . '/app/admin/controller';
    $controllerFiles = glob($controllerDir . '/*.php');

    $existingControllers = [];
    foreach ($controllerFiles as $file) {
        $basename = basename($file, '.php');
        $existingControllers[] = $basename;
        echo "✅ {$basename}.php\n";
    }

    // 检查菜单中引用的控制器
    echo "\n=== 菜单引用的控制器分析 ===\n";
    $menuControllers = [];
    foreach ($menus as $menu) {
        if (!empty($menu['node']) && strpos($menu['node'], 'admin/') === 0) {
            $parts = explode('/', $menu['node']);
            if (count($parts) >= 2) {
                $controller = $parts[1];
                $menuControllers[$controller][] = $menu['title'] . ' (' . $menu['node'] . ')';
            }
        }
    }

    foreach ($menuControllers as $controller => $menuItems) {
        $controllerFile = ucfirst(str_replace('_', '', ucwords($controller, '_')));
        $exists = in_array($controllerFile, $existingControllers) ? '✅' : '❌';
        echo "{$exists} {$controller} -> {$controllerFile}.php\n";
        foreach ($menuItems as $item) {
            echo "    - {$item}\n";
        }
    }

    // 检查未被菜单引用的控制器
    echo "\n=== 未被菜单引用的控制器 ===\n";
    $referencedControllers = [];
    foreach ($menuControllers as $controller => $items) {
        $controllerFile = ucfirst(str_replace('_', '', ucwords($controller, '_')));
        $referencedControllers[] = $controllerFile;
    }

    $unreferencedControllers = array_diff($existingControllers, $referencedControllers);
    foreach ($unreferencedControllers as $controller) {
        echo "⚠️  {$controller}.php (未被菜单引用)\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
