{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        Webhook配置
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">n8n Webhook URL</label>
                <div class="layui-input-block">
                    <input type="text" name="n8n_webhook_url" value="{$configs.n8n_webhook_url}" placeholder="请输入n8n Webhook URL" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">Webhook密钥</label>
                <div class="layui-input-block">
                    <input type="password" name="webhook_secret" value="{$configs.webhook_secret}" placeholder="请输入Webhook密钥" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">Webhook超时时间(秒)</label>
                <div class="layui-input-block">
                    <input type="text" name="webhook_timeout" value="{$configs.webhook_timeout}" placeholder="请输入Webhook超时时间(秒)" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">Webhook重试次数</label>
                <div class="layui-input-block">
                    <input type="text" name="webhook_retry_times" value="{$configs.webhook_retry_times}" placeholder="请输入Webhook重试次数" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">启用Webhook</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="webhook_enable" value="1" lay-skin="switch" lay-text="启用|禁用" {if $configs.webhook_enable}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info);
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}