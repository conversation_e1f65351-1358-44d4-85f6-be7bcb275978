<?php

// +----------------------------------------------------------------------
// | N8N Workflow Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\N8nWorkflow as N8nWorkflowModel;

/**
 * n8n工作流管理
 * @class N8nWorkflow
 * @package app\admin\controller
 */
class N8nWorkflow extends Controller
{
    /**
     * n8n工作流管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        N8nWorkflowModel::mQuery()->layTable(function () {
            $this->title = 'n8n工作流管理';
        }, static function (QueryHelper $query) {
            $query->like('name,workflow_id,type')->equal('is_active');
            $query->dateBetween('create_time,last_execution_time');
            $query->order('is_active desc,execution_count desc,id desc');
        });
    }

    /**
     * 添加工作流
     * @auth true
     */
    public function add()
    {
        N8nWorkflowModel::mForm('n8n_workflow/form');
    }

    /**
     * 编辑工作流
     * @auth true
     */
    public function edit()
    {
        N8nWorkflowModel::mForm('n8n_workflow/form');
    }

    /**
     * 查看工作流详情
     * @auth true
     */
    public function view()
    {
        N8nWorkflowModel::mForm('n8n_workflow/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 工作流类型选项
            $this->typeOptions = [
                'paper_outline' => '论文大纲生成',
                'paper_writing' => '论文写作',
                'content_rewrite' => '内容降重',
                'plagiarism_check' => '查重检测',
                'document_export' => '文档导出',
                'email_notification' => '邮件通知',
                'data_sync' => '数据同步',
                'file_process' => '文件处理',
                'custom' => '自定义流程'
            ];
            
            // 激活状态选项
            $this->activeOptions = [
                0 => '未激活',
                1 => '已激活'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['execution_count'] = 0;
                $data['success_count'] = 0;
            }
            $data['update_time'] = date('Y-m-d H:i:s');
            
            // 处理配置参数JSON
            if (!empty($data['config']) && is_array($data['config'])) {
                $data['config'] = json_encode($data['config'], JSON_UNESCAPED_UNICODE);
            }
            
            // 验证工作流ID唯一性
            if (!empty($data['workflow_id'])) {
                $exists = N8nWorkflowModel::mk()
                    ->where(['workflow_id' => $data['workflow_id']])
                    ->where('id', '<>', $data['id'] ?? 0)
                    ->findOrEmpty();
                if (!$exists->isEmpty()) {
                    $this->error('工作流ID已存在！');
                }
            }
        }
    }

    /**
     * 修改工作流状态
     * @auth true
     */
    public function state()
    {
        N8nWorkflowModel::mSave($this->_vali([
            'is_active.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除工作流
     * @auth true
     */
    public function remove()
    {
        N8nWorkflowModel::mDelete();
    }

    /**
     * 激活工作流
     * @auth true
     */
    public function activate()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('工作流ID不能为空！');
        }
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        // 这里可以调用n8n API激活工作流
        // 暂时直接更新状态
        $workflow->save([
            'is_active' => 1,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('工作流已激活！');
    }

    /**
     * 停用工作流
     * @auth true
     */
    public function deactivate()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('工作流ID不能为空！');
        }
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        // 这里可以调用n8n API停用工作流
        // 暂时直接更新状态
        $workflow->save([
            'is_active' => 0,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('工作流已停用！');
    }

    /**
     * 测试执行工作流
     * @auth true
     */
    public function test()
    {
        $data = $this->_vali([
            'id.require' => '工作流ID不能为空！',
            'test_data' => '测试数据',
        ]);
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($data['id']);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        if ($workflow['is_active'] != 1) {
            $this->error('工作流未激活！');
        }
        
        // 解析测试数据
        $testData = !empty($data['test_data']) ? json_decode($data['test_data'], true) : [];
        
        // 这里可以调用n8n API执行工作流
        // 暂时模拟执行成功
        $executionId = 'test_' . time();
        
        // 更新执行统计
        $workflow->save([
            'execution_count' => $workflow['execution_count'] + 1,
            'success_count' => $workflow['success_count'] + 1,
            'last_execution_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $result = [
            'execution_id' => $executionId,
            'status' => 'success',
            'test_data' => $testData,
            'message' => '工作流测试执行成功'
        ];
        
        $this->success('工作流测试执行成功！', $result);
    }

    /**
     * 同步工作流信息
     * @auth true
     */
    public function sync()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('工作流ID不能为空！');
        }
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        // 这里可以调用n8n API获取最新的工作流信息
        // 暂时模拟同步成功
        $workflow->save([
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('工作流信息同步成功！');
    }

    /**
     * 复制工作流
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('工作流ID不能为空！');
        }
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        $copyData = $workflow->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . ' - 副本';
        $copyData['workflow_id'] = $copyData['workflow_id'] . '_copy_' . time();
        $copyData['is_active'] = 0;
        $copyData['execution_count'] = 0;
        $copyData['success_count'] = 0;
        $copyData['last_execution_time'] = null;
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');
        
        N8nWorkflowModel::mk()->save($copyData);
        $this->success('工作流复制成功！');
    }

    /**
     * 查看执行统计
     * @auth true
     */
    public function statistics()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('工作流ID不能为空！');
        }
        
        $workflow = N8nWorkflowModel::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            $this->error('工作流不存在！');
        }
        
        // 计算成功率
        $successRate = $workflow['execution_count'] > 0 
            ? round($workflow['success_count'] / $workflow['execution_count'] * 100, 2) 
            : 0;
        
        $statistics = [
            'execution_count' => $workflow['execution_count'],
            'success_count' => $workflow['success_count'],
            'failed_count' => $workflow['execution_count'] - $workflow['success_count'],
            'success_rate' => $successRate,
            'last_execution_time' => $workflow['last_execution_time']
        ];
        
        $this->workflow = $workflow;
        $this->statistics = $statistics;
        
        $this->fetch('n8n_workflow/statistics');
    }
}
