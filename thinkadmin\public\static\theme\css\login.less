@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2024 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

body, html {
  height: 100%;
  display: block;
}

body, .login-container {
  vertical-align: middle;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  #defaTransition(1s, background-image);
}

.login-container {
  height: 100%;

  .header {
    top: 0;
    left: 0;
    width: 100%;
    height: 48px;
    position: absolute;
    text-shadow: #000 .1em .1em .1em;

    .title {
      color: #fff;
      float: left;
      font-size: 18px;
      line-height: 48px;
      text-indent: 40px;
      letter-spacing: 1px;

      span {
        font-size: 10px;
        padding-left: 5px;
      }
    }

    a {
      &, &:focus, &:hover {
        color: #fff;
        letter-spacing: 1px;
        text-decoration: none;
      }
    }

    ul {
      float: right;

      li {
        float: left;
        line-height: 47px;
        margin-left: 10px;
      }
    }
  }

  form {
    top: 50%;
    left: 50%;
    width: 300px;
    position: absolute;
    margin-top: -250px;
    margin-left: -150px;

    h2 {
      color: #fff;
      padding: 20px 0;
      font-size: 25px;
      text-align: center;
      font-weight: 700;
      letter-spacing: 3px;
      text-shadow: #000 .05em .05em .05em;
    }

    ul li {
      margin-top: 20px;
      text-shadow: #000 .1em .1em .1em;

      &.verify {
        label {
          width: 200px;
        }

        input.layui-input {
          text-transform: uppercase
        }

        img {
          width: 95px;
          height: 44px;
          cursor: pointer;
          position: absolute;
          margin-left: 5px;
          border-radius: @BoxBorderRadius;
          box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .1);
        }
      }

      i.layui-icon {
        color: #fff;
        font-size: 18px;
        position: absolute;
        padding: 8px 15px 8px 20px;
      }

      input {
        color: #fff;
        height: 45px;
        padding: 0 15px;
        font-size: 14px;
        line-height: 1em;
        text-indent: 35px;
        border: #DDD !important;
        border-radius: @BoxBorderRadius;
        letter-spacing: 2px;
        background: rgba(0, 0, 0, .12);
        text-shadow: #000 .1em .1em .1em;
        -webkit-text-fill-color: #fff !important;
        box-shadow: 0 2px 3px 0 rgba(0, 0, 0, .3) inset !important;

        &:hover, &:focus, &:active,
        &:-webkit-autofill, &:-webkit-autofill:hover,
        &:-webkit-autofill:focus, &:-webkit-autofill:active {
          border: #DDD !important;
          text-shadow: #000 .1em .1em .1em;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .4) inset !important;
          -webkit-text-fill-color: #fff !important;
          -webkit-transition-delay: 9999s !important;
          -webkit-transition: color 9999s ease-out, background-color 9999s ease-out !important;
        }
      }

      span {
        letter-spacing: 1px;
        color: #ff0 !important;
      }

      button {
        color: #333 !important;
        height: 45px !important;
        border: none !important;
        background: #fff !important;
        border-radius: @BoxBorderRadius !important;
        letter-spacing: 1px !important;
        box-shadow: 0 15px 30px 0 hsla(0, 0%, 100%, .25) inset, 0 2px 7px 0 rgba(0, 0, 0, .2);

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .footer {
    left: 0;
    bottom: 0;
    color: #fff;
    width: 100%;
    position: absolute;
    text-align: center;
    line-height: 30px;
    padding-bottom: 10px;
    text-shadow: #000 .1em .1em .1em;

    a, span {
      color: #fff;
    }

    a:hover {
      color: #ccc
    }
  }
}