{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">正文模板管理</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false" method="get" autocomplete="off">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-inline">
                    <input name="name" value="{$get.name ?? ''}" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-inline">
                    <select name="paper_type_id" class="layui-select">
                        <option value="">-- 全部 --</option>
                        <option value="0" {if isset($get.paper_type_id) && $get.paper_type_id eq '0'}selected{/if}>通用模板</option>
                        {foreach $paperTypes as $id=>$name}
                        <option value="{$id}" {if isset($get.paper_type_id) && $get.paper_type_id eq $id}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">-- 全部 --</option>
                        <option value="1" {if isset($get.status) && $get.status eq '1'}selected{/if}>启用</option>
                        <option value="0" {if isset($get.status) && $get.status eq '0'}selected{/if}>禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">默认模板</label>
                <div class="layui-input-inline">
                    <select name="is_default" class="layui-select">
                        <option value="">-- 全部 --</option>
                        <option value="1" {if isset($get.is_default) && $get.is_default eq '1'}selected{/if}>是</option>
                        <option value="0" {if isset($get.is_default) && $get.is_default eq '0'}selected{/if}>否</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line">
                    <thead>
                    <tr>
                        <th class='text-left nowrap'>
                            {if auth("add")}
                            <button data-modal='{:url("add")}' data-title="添加正文模板" class='layui-btn layui-btn-sm layui-btn-primary'>
                                <i class='layui-icon layui-icon-add-1'></i> 添加正文模板
                            </button>
                            {/if}
                        </th>
                    </tr>
                    </thead>
                </table>
                {$pagehtml|raw|default=''}
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>模板名称</th>
                        <th class='text-left nowrap'>论文类型</th>
                        <th class='text-left nowrap'>使用次数</th>
                        <th class='text-left nowrap'>默认模板</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.name ?? ''}">{$vo.name ?? ''}</div>
                        </td>
                        <td class='text-left nowrap'>{$paperTypes[$vo.paper_type_id] ?? '通用'}</td>
                        <td class='text-left nowrap'>{$vo.usage_count ?? 0}</td>
                        <td class='text-left nowrap'>
                            {if $vo.is_default eq 1}
                            <span class="layui-badge layui-bg-green">是</span>
                            {else}
                            <span class="layui-badge">否</span>
                            {/if}
                        </td>
                        <td class='text-left nowrap'>
                            {if $vo.status eq 1}
                            <span class="layui-badge layui-bg-green">启用</span>
                            {else}
                            <span class="layui-badge">禁用</span>
                            {/if}
                        </td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("view")}
                            <a class='layui-btn layui-btn-sm layui-btn-normal' data-modal='{:url("view")}?id={$vo.id}' data-title="查看正文模板">查看</a>
                            {/if}
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-sm' data-modal='{:url("edit")}?id={$vo.id}' data-title="编辑正文模板">编辑</a>
                            {/if}
                            {if auth("copy")}
                            <a class='layui-btn layui-btn-sm layui-btn-primary' data-confirm="确定要复制该模板吗？" data-action='{:url("copy")}' data-value="id#{$vo.id}" data-loading>复制</a>
                            {/if}
                            {if auth("setDefault") && $vo.is_default eq 0}
                            <a class='layui-btn layui-btn-sm layui-btn-warm' data-confirm="确定要设为默认模板吗？" data-action='{:url("setDefault")}' data-value="id#{$vo.id};paper_type_id#{$vo.paper_type_id}" data-loading>设为默认</a>
                            {/if}
                            {if auth("state")}
                            <a class='layui-btn layui-btn-sm layui-btn-warm' data-action='{:url("state")}' data-value="id#{$vo.id};status#{$vo.status?0:1}" data-loading>
                                {$vo.status?'禁用':'启用'}
                            </a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-sm layui-btn-danger' data-confirm="确定要删除该正文模板吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                {if empty($list)}<p class="help-block text-center well">没 有 相 关 数 据 哦！</p>{/if}
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
