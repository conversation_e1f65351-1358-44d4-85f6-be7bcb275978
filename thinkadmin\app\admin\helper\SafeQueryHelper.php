<?php

namespace app\admin\helper;

use think\db\Query;
use think\Request;

/**
 * 安全查询助手类
 * 避免QueryHelper的getInputData方法问题
 */
class SafeQueryHelper
{
    protected $request;
    protected $query;
    
    public function __construct(Query $query, Request $request)
    {
        $this->query = $query;
        $this->request = $request;
    }
    
    /**
     * 安全的like查询
     */
    public function safeLike($fields, $input = null)
    {
        $data = $input ?: $this->request->param();
        $fields = is_array($fields) ? $fields : explode(',', $fields);
        
        foreach ($fields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $this->query->whereLike($field, '%' . $data[$field] . '%');
            }
        }
        
        return $this;
    }
    
    /**
     * 安全的equal查询
     */
    public function safeEqual($fields, $input = null)
    {
        $data = $input ?: $this->request->param();
        $fields = is_array($fields) ? $fields : explode(',', $fields);
        
        foreach ($fields as $field) {
            if (isset($data[$field]) && $data[$field] !== '') {
                $this->query->where($field, $data[$field]);
            }
        }
        
        return $this;
    }
    
    /**
     * 安全的日期范围查询
     */
    public function safeDateBetween($field, $input = null)
    {
        $data = $input ?: $this->request->param();
        
        if (isset($data[$field . '_start']) && !empty($data[$field . '_start'])) {
            $this->query->where($field, '>=', $data[$field . '_start']);
        }
        
        if (isset($data[$field . '_end']) && !empty($data[$field . '_end'])) {
            $this->query->where($field, '<=', $data[$field . '_end']);
        }
        
        return $this;
    }
    
    /**
     * 获取查询对象
     */
    public function getQuery()
    {
        return $this->query;
    }
}