<fieldset>
    <legend>{:lang('条件搜索')}</legend>
    <form class="layui-form layui-form-pane form-search" action="{:sysuri()}" onsubmit="return false" method="get" autocomplete="off">

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('权限名称')}</label>
            <div class="layui-input-inline">
                <input name="title" value="{$get.title|default=''}" placeholder="{:lang('请输入权限名称')}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('权限描述')}</label>
            <div class="layui-input-inline">
                <input name="desc" value="{$get.desc|default=''}" placeholder="{:lang('请输入权限描述')}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('使用状态')}</label>
            <div class="layui-input-inline">
                <select class="layui-select" name="status">
                    <option value=''>-- {:lang('全部')} --</option>
                    {foreach [lang('已禁用记录'),lang('已激活记录')] as $k=>$v}
                    {if isset($get.status) and $get.status eq $k.""}
                    <option selected value="{$k}">{$v}</option>
                    {else}
                    <option value="{$k}">{$v}</option>
                    {/if}{/foreach}
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('创建时间')}</label>
            <div class="layui-input-inline">
                <input data-date-range name="create_at" value="{$get.create_at|default=''}" placeholder="{:lang('请选择创建时间')}" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-inline">
            <button class="layui-btn layui-btn-primary"><i class="layui-icon">&#xe615;</i> {:lang('搜 索')}</button>
        </div>
    </form>
</fieldset>