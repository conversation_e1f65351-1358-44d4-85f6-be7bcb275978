<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card" data-table-id="PaperProjectTable">

    <div class="layui-card-body padding-left-40">

        <label class="layui-form-item relative block">
            <span class="help-label"><b>项目标题</b>Project Title</span>
            <input maxlength="200" class="layui-input" name="title" value='{$vo.title|default=""}' required vali-name="项目标题" placeholder="请输入论文项目标题">
            <span class="help-block">请输入简洁明了的项目标题，建议包含论文主题</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>论文类型</b>Paper Type</span>
            <select name="paper_type_id" class="layui-select" required vali-name="论文类型">
                <option value="">请选择论文类型</option>
                {volist name="paperTypes" id="v" key="k"}
                {if isset($vo.paper_type_id) and $vo.paper_type_id eq $k}
                <option selected value="{$k}">{$v}</option>
                {else}
                <option value="{$k}">{$v}</option>
                {/if}
                {/volist}
            </select>
            <span class="help-block">选择合适的论文类型，系统将使用对应的模板和配置</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>论文主题</b>Paper Topic</span>
            <textarea name="topic" class="layui-textarea" required vali-name="论文主题" placeholder="请详细描述论文主题和研究方向">{$vo.topic|default=''}</textarea>
            <span class="help-block">请详细描述论文的研究主题、方向和要解决的问题</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>研究要求</b>Research Requirements</span>
            <textarea name="requirements" class="layui-textarea" placeholder="请输入具体的研究要求和期望">{$vo.requirements|default=''}</textarea>
            <span class="help-block">请描述论文的具体要求，如研究方法、数据来源、预期结果等</span>
        </label>

        <div class="layui-form-item">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>目标字数</b>Target Word Count</span>
                        <input type="number" min="1000" class="layui-input" name="target_word_count" value='{$vo.target_word_count|default=""}' required vali-name="目标字数" placeholder="请输入目标字数">
                        <span class="help-block">设置论文的目标字数</span>
                    </label>
                </div>
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>截止日期</b>Deadline</span>
                        <input type="date" class="layui-input" name="deadline" value='{$vo.deadline|default=""}' placeholder="请选择截止日期">
                        <span class="help-block">设置项目完成的截止日期</span>
                    </label>
                </div>
            </div>
        </div>

        {if isset($vo.id)}
        <div class="layui-form-item">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <label class="relative block">
                        <span class="help-label"><b>当前状态</b>Current Status</span>
                        <select name="status" class="layui-select">
                            <option value="draft" {if isset($vo.status) and $vo.status eq 'draft'}selected{/if}>草稿</option>
                            <option value="outline" {if isset($vo.status) and $vo.status eq 'outline'}selected{/if}>大纲生成中</option>
                            <option value="writing" {if isset($vo.status) and $vo.status eq 'writing'}selected{/if}>写作中</option>
                            <option value="completed" {if isset($vo.status) and $vo.status eq 'completed'}selected{/if}>已完成</option>
                            <option value="failed" {if isset($vo.status) and $vo.status eq 'failed'}selected{/if}>失败</option>
                        </select>
                    </label>
                </div>
                <div class="layui-col-md4">
                    <label class="relative block">
                        <span class="help-label"><b>当前字数</b>Current Word Count</span>
                        <input type="number" min="0" class="layui-input" name="current_word_count" value='{$vo.current_word_count|default="0"}' readonly>
                    </label>
                </div>
                <div class="layui-col-md4">
                    <label class="relative block">
                        <span class="help-label"><b>完成进度</b>Progress (%)</span>
                        <input type="number" min="0" max="100" class="layui-input" name="progress" value='{$vo.progress|default="0"}' readonly>
                    </label>
                </div>
            </div>
        </div>
        {/if}

        <label class="layui-form-item relative block">
            <span class="help-label"><b>备注信息</b>Notes</span>
            <textarea name="notes" class="layui-textarea" placeholder="请输入项目备注信息">{$vo.notes|default=''}</textarea>
            <span class="help-block">可以记录项目的特殊要求或注意事项</span>
        </label>

    </div>

    <div class="hr-line-dashed"></div>
    {notempty name='vo.id'}<input type='hidden' value='{$vo.id}' name='id'>{/notempty}

    <div class="layui-form-item text-center">
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>

</form>
