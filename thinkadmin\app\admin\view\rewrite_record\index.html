{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">降重记录管理</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false" method="get" autocomplete="off">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">任务标题</label>
                <div class="layui-input-inline">
                    <input name="title" value="{$get.title|default=''}" placeholder="请输入任务标题" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">降重模式</label>
                <div class="layui-input-inline">
                    <select name="rewrite_mode" class="layui-select">
                        <option value="">-- 全部 --</option>
                        {foreach $modeOptions as $key=>$name}
                        <option value="{$key}" {if isset($get.rewrite_mode) && $get.rewrite_mode eq $key}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">任务状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">-- 全部 --</option>
                        {foreach $statusOptions as $key=>$name}
                        <option value="{$key}" {if isset($get.status) && $get.status eq $key}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">AI模型</label>
                <div class="layui-input-inline">
                    <select name="ai_model_id" class="layui-select">
                        <option value="">-- 全部 --</option>
                        {foreach $aiModels as $id=>$name}
                        <option value="{$id}" {if isset($get.ai_model_id) && $get.ai_model_id eq $id}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line">
                    <thead>
                    <tr>
                        <th class='text-left nowrap'>
                            {if auth("export")}
                            <button data-action='{:url("export")}' data-rule="ids#{}" data-confirm="确定要导出选中的记录吗？" class='layui-btn layui-btn-sm layui-btn-primary'>
                                <i class='layui-icon layui-icon-export'></i> 导出记录
                            </button>
                            {/if}
                            {if auth("batchRemove")}
                            <button data-action='{:url("batchRemove")}' data-rule="ids#{}" data-confirm="确定要删除选中的记录吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                                <i class='layui-icon layui-icon-delete'></i> 批量删除
                            </button>
                            {/if}
                        </th>
                    </tr>
                    </thead>
                </table>
                {$pagehtml|raw|default=''}
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>任务标题</th>
                        <th class='text-left nowrap'>用户ID</th>
                        <th class='text-left nowrap'>原文字数</th>
                        <th class='text-left nowrap'>降重模式</th>
                        <th class='text-left nowrap'>目标相似度</th>
                        <th class='text-left nowrap'>AI模型</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>进度</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.title|default=''}">{$vo.title|default=''}</div>
                        </td>
                        <td class='text-left nowrap'>{$vo.user_id|default=''}</td>
                        <td class='text-left nowrap'>{$vo.original_word_count|default=0}</td>
                        <td class='text-left nowrap'>{$modeOptions[$vo.rewrite_mode]|default=$vo.rewrite_mode}</td>
                        <td class='text-left nowrap'>{$vo.target_similarity|default=0}%</td>
                        <td class='text-left nowrap'>{$aiModels[$vo.ai_model_id]|default='未知'}</td>
                        <td class='text-left nowrap'>
                            {switch name="vo.status"}
                            {case value="pending"}<span class="layui-badge">待处理</span>{/case}
                            {case value="processing"}<span class="layui-badge layui-bg-blue">处理中</span>{/case}
                            {case value="completed"}<span class="layui-badge layui-bg-green">已完成</span>{/case}
                            {case value="failed"}<span class="layui-badge layui-bg-red">失败</span>{/case}
                            {case value="cancelled"}<span class="layui-badge layui-bg-gray">已取消</span>{/case}
                            {default /}<span class="layui-badge">{$vo.status}</span>
                            {/switch}
                        </td>
                        <td class='text-left nowrap'>
                            <div class="layui-progress" lay-showpercent="true" style="width: 80px;">
                                <div class="layui-progress-bar" lay-percent="{$vo.progress|default=0}%"></div>
                            </div>
                        </td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("view")}
                            <a class='layui-btn layui-btn-sm layui-btn-normal' data-modal='{:url("view")}?id={$vo.id}' data-title="查看详情">详情</a>
                            {/if}
                            {if auth("reprocess") && in_array($vo.status, ['failed', 'cancelled'])}
                            <a class='layui-btn layui-btn-sm layui-btn-warm' data-confirm="确定要重新处理该任务吗？" data-action='{:url("reprocess")}' data-value="id#{$vo.id}" data-loading>重新处理</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-sm layui-btn-danger' data-confirm="确定要删除该记录吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                {if empty($list)}<p class="help-block text-center well">没 有 相 关 数 据 哦！</p>{/if}
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
