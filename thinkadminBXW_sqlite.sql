-- =====================================================
-- AI论文写作平台数据库设计 (FastAdmin + n8n 架构) - SQLite版本
-- 表前缀: bxw_
-- 创建时间: 2025-01-01
-- 说明: 基于FastAdmin框架的AI论文写作平台数据库结构 (SQLite适配版)
-- =====================================================

PRAGMA foreign_keys = OFF;

-- =====================================================
-- 1. 用户相关表
-- =====================================================

-- 用户基础信息表
DROP TABLE IF EXISTS `bxw_user`;
CREATE TABLE `bxw_user` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `username` VARCHAR(50) NOT NULL,
  `email` VARCHAR(100) NOT NULL,
  `phone` VARCHAR(20) DEFAULT NULL,
  `password_hash` VARCHAR(255) NOT NULL,
  `nickname` VARCHAR(50) DEFAULT NULL,
  `avatar` VARCHAR(255) DEFAULT NULL,
  `status` INTEGER NOT NULL DEFAULT 1,
  `user_type` INTEGER NOT NULL DEFAULT 1,
  `credits` INTEGER NOT NULL DEFAULT 0,
  `vip_expire_time` DATETIME DEFAULT NULL,
  `last_login_time` DATETIME DEFAULT NULL,
  `last_login_ip` VARCHAR(50) DEFAULT NULL,
  `register_ip` VARCHAR(50) DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL,
  CONSTRAINT chk_credits CHECK (credits >= 0),
  CONSTRAINT chk_status_user CHECK (status IN (0, 1)),
  CONSTRAINT chk_user_type CHECK (user_type IN (1, 2, 3))
);

CREATE UNIQUE INDEX `idx_bxw_user_username` ON `bxw_user` (`username`);
CREATE UNIQUE INDEX `idx_bxw_user_email` ON `bxw_user` (`email`);
CREATE INDEX `idx_bxw_user_status` ON `bxw_user` (`status`);
CREATE INDEX `idx_bxw_user_type` ON `bxw_user` (`user_type`);

-- 用户配额限制表
DROP TABLE IF EXISTS `bxw_user_quota`;
CREATE TABLE `bxw_user_quota` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `quota_type` VARCHAR(50) NOT NULL,
  `daily_limit` INTEGER NOT NULL DEFAULT 0,
  `monthly_limit` INTEGER NOT NULL DEFAULT 0,
  `daily_used` INTEGER NOT NULL DEFAULT 0,
  `monthly_used` INTEGER NOT NULL DEFAULT 0,
  `last_reset_date` DATE NOT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_user_quota_user_type` ON `bxw_user_quota` (`user_id`, `quota_type`);
CREATE INDEX `idx_bxw_user_quota_user_id` ON `bxw_user_quota` (`user_id`);

-- =====================================================
-- 2. 论文写作相关表
-- =====================================================

-- 论文类型表
DROP TABLE IF EXISTS `bxw_paper_type`;
CREATE TABLE `bxw_paper_type` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `word_count_min` INTEGER NOT NULL DEFAULT 6000,
  `word_count_max` INTEGER NOT NULL DEFAULT 30000,
  `outline_template_id` INTEGER DEFAULT NULL,
  `prompt_template_id` INTEGER DEFAULT NULL,
  `sort` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_paper_type_status` ON `bxw_paper_type` (`status`);
CREATE INDEX `idx_bxw_paper_type_sort` ON `bxw_paper_type` (`sort`);

-- 大纲模板表
DROP TABLE IF EXISTS `bxw_outline_template`;
CREATE TABLE `bxw_outline_template` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `paper_type_id` INTEGER DEFAULT NULL,
  `template_content` TEXT NOT NULL,
  `description` TEXT,
  `is_default` INTEGER NOT NULL DEFAULT 0,
  `usage_count` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_outline_template_paper_type_id` ON `bxw_outline_template` (`paper_type_id`);
CREATE INDEX `idx_bxw_outline_template_status` ON `bxw_outline_template` (`status`);

-- 提示词模板表
DROP TABLE IF EXISTS `bxw_prompt_template`;
CREATE TABLE `bxw_prompt_template` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `paper_type_id` INTEGER DEFAULT NULL,
  `prompt_content` TEXT NOT NULL,
  `variables` TEXT,
  `ai_model` VARCHAR(50) DEFAULT NULL,
  `description` TEXT,
  `is_default` INTEGER NOT NULL DEFAULT 0,
  `usage_count` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_prompt_template_type` ON `bxw_prompt_template` (`type`);
CREATE INDEX `idx_bxw_prompt_template_paper_type_id` ON `bxw_prompt_template` (`paper_type_id`);
CREATE INDEX `idx_bxw_prompt_template_status` ON `bxw_prompt_template` (`status`);

-- 论文项目表
DROP TABLE IF EXISTS `bxw_paper_project`;
CREATE TABLE `bxw_paper_project` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `paper_type_id` INTEGER NOT NULL,
  `subject` VARCHAR(100) DEFAULT NULL,
  `keywords` VARCHAR(500) DEFAULT NULL,
  `requirements` TEXT,
  `target_word_count` INTEGER NOT NULL DEFAULT 10000,
  `writing_style` VARCHAR(50) NOT NULL DEFAULT 'academic',
  `outline_content` TEXT,
  `outline_version` INTEGER NOT NULL DEFAULT 1,
  `content` TEXT,
  `current_word_count` INTEGER NOT NULL DEFAULT 0,
  `status` VARCHAR(20) NOT NULL DEFAULT 'draft',
  `is_draft` INTEGER NOT NULL DEFAULT 1,
  `draft_version` INTEGER NOT NULL DEFAULT 1,
  `parent_id` INTEGER DEFAULT NULL,
  `n8n_workflow_id` VARCHAR(100) DEFAULT NULL,
  `n8n_execution_id` VARCHAR(100) DEFAULT NULL,
  `progress` INTEGER NOT NULL DEFAULT 0,
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL,
  CONSTRAINT chk_status CHECK (status IN ('draft', 'outline_generating', 'writing', 'completed', 'failed', 'cancelled')),
  CONSTRAINT chk_is_draft CHECK (is_draft IN (0, 1)),
  CONSTRAINT chk_progress CHECK (progress >= 0 AND progress <= 100)
);

CREATE INDEX `idx_bxw_paper_project_user_id` ON `bxw_paper_project` (`user_id`);
CREATE INDEX `idx_bxw_paper_project_paper_type_id` ON `bxw_paper_project` (`paper_type_id`);
CREATE INDEX `idx_bxw_paper_project_status` ON `bxw_paper_project` (`status`);
CREATE INDEX `idx_bxw_paper_project_createtime` ON `bxw_paper_project` (`createtime`);
CREATE INDEX `idx_bxw_paper_project_is_draft` ON `bxw_paper_project` (`is_draft`);
CREATE INDEX `idx_bxw_paper_project_parent_id` ON `bxw_paper_project` (`parent_id`);
CREATE INDEX `idx_user_task_status_time` ON `bxw_paper_project` (`user_id`, `status`, `createtime`);
CREATE INDEX `idx_user_draft_version` ON `bxw_paper_project` (`user_id`, `is_draft`, `draft_version`);

-- 论文章节表
DROP TABLE IF EXISTS `bxw_paper_section`;
CREATE TABLE `bxw_paper_section` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `project_id` INTEGER NOT NULL,
  `section_title` VARCHAR(255) NOT NULL,
  `section_level` INTEGER NOT NULL DEFAULT 1,
  `section_order` INTEGER NOT NULL DEFAULT 0,
  `content` TEXT,
  `word_count` INTEGER NOT NULL DEFAULT 0,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `ai_model_used` VARCHAR(50) DEFAULT NULL,
  `generation_time` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_paper_section_project_id` ON `bxw_paper_section` (`project_id`);
CREATE INDEX `idx_bxw_paper_section_status` ON `bxw_paper_section` (`status`);
CREATE INDEX `idx_bxw_paper_section_order` ON `bxw_paper_section` (`section_order`);

-- =====================================================
-- 3. AI模型配置相关表
-- =====================================================

-- AI模型配置表
DROP TABLE IF EXISTS `bxw_ai_model`;
CREATE TABLE `bxw_ai_model` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `provider` VARCHAR(50) NOT NULL,
  `model_code` VARCHAR(100) NOT NULL,
  `api_endpoint` VARCHAR(255) NOT NULL,
  `api_key` VARCHAR(255) DEFAULT NULL,
  `max_tokens` INTEGER NOT NULL DEFAULT 4000,
  `temperature` REAL NOT NULL DEFAULT 0.70,
  `top_p` REAL NOT NULL DEFAULT 1.00,
  `frequency_penalty` REAL NOT NULL DEFAULT 0.00,
  `presence_penalty` REAL NOT NULL DEFAULT 0.00,
  `cost_per_1k_tokens` REAL NOT NULL DEFAULT 0.000000,
  `priority` INTEGER NOT NULL DEFAULT 0,
  `rate_limit_per_minute` INTEGER NOT NULL DEFAULT 60,
  `status` INTEGER NOT NULL DEFAULT 1,
  `health_status` INTEGER NOT NULL DEFAULT 1,
  `last_health_check` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_ai_model_provider_model` ON `bxw_ai_model` (`provider`, `model_code`);
CREATE INDEX `idx_bxw_ai_model_status` ON `bxw_ai_model` (`status`);
CREATE INDEX `idx_bxw_ai_model_priority` ON `bxw_ai_model` (`priority`);

-- AI模型使用统计表
DROP TABLE IF EXISTS `bxw_ai_usage_log`;
CREATE TABLE `bxw_ai_usage_log` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `ai_model_id` INTEGER NOT NULL,
  `task_type` VARCHAR(50) NOT NULL,
  `task_id` INTEGER DEFAULT NULL,
  `prompt_tokens` INTEGER NOT NULL DEFAULT 0,
  `completion_tokens` INTEGER NOT NULL DEFAULT 0,
  `total_tokens` INTEGER NOT NULL DEFAULT 0,
  `cost` REAL NOT NULL DEFAULT 0.000000,
  `response_time` INTEGER NOT NULL DEFAULT 0,
  `status` VARCHAR(20) NOT NULL DEFAULT 'success',
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_ai_usage_log_user_id` ON `bxw_ai_usage_log` (`user_id`);
CREATE INDEX `idx_bxw_ai_usage_log_ai_model_id` ON `bxw_ai_usage_log` (`ai_model_id`);
CREATE INDEX `idx_bxw_ai_usage_log_task_type` ON `bxw_ai_usage_log` (`task_type`);
CREATE INDEX `idx_bxw_ai_usage_log_createtime` ON `bxw_ai_usage_log` (`createtime`);
CREATE INDEX `idx_ai_usage_model_time` ON `bxw_ai_usage_log` (`ai_model_id`, `createtime`);
CREATE INDEX `idx_ai_usage_user_task` ON `bxw_ai_usage_log` (`user_id`, `task_type`, `createtime`);

-- =====================================================
-- 4. 语义降重相关表
-- =====================================================

-- 降重任务表
DROP TABLE IF EXISTS `bxw_rewrite_task`;
CREATE TABLE `bxw_rewrite_task` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `original_text` TEXT NOT NULL,
  `original_word_count` INTEGER NOT NULL DEFAULT 0,
  `rewrite_mode` VARCHAR(50) NOT NULL DEFAULT 'standard',
  `target_similarity` REAL NOT NULL DEFAULT 30.00,
  `ai_model_id` INTEGER DEFAULT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `n8n_workflow_id` VARCHAR(100) DEFAULT NULL,
  `n8n_execution_id` VARCHAR(100) DEFAULT NULL,
  `progress` INTEGER NOT NULL DEFAULT 0,
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_rewrite_task_user_id` ON `bxw_rewrite_task` (`user_id`);
CREATE INDEX `idx_bxw_rewrite_task_status` ON `bxw_rewrite_task` (`status`);
CREATE INDEX `idx_bxw_rewrite_task_createtime` ON `bxw_rewrite_task` (`createtime`);

-- 降重结果表
DROP TABLE IF EXISTS `bxw_rewrite_result`;
CREATE TABLE `bxw_rewrite_result` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `task_id` INTEGER NOT NULL,
  `ai_model_id` INTEGER NOT NULL,
  `rewritten_text` TEXT NOT NULL,
  `rewritten_word_count` INTEGER NOT NULL DEFAULT 0,
  `similarity_score` REAL DEFAULT NULL,
  `quality_score` REAL DEFAULT NULL,
  `processing_time` INTEGER NOT NULL DEFAULT 0,
  `is_selected` INTEGER NOT NULL DEFAULT 0,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_rewrite_result_task_id` ON `bxw_rewrite_result` (`task_id`);
CREATE INDEX `idx_bxw_rewrite_result_ai_model_id` ON `bxw_rewrite_result` (`ai_model_id`);

-- =====================================================
-- 5. 查重相关表
-- =====================================================

-- 查重接口配置表
DROP TABLE IF EXISTS `bxw_check_api`;
CREATE TABLE `bxw_check_api` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `provider` VARCHAR(50) NOT NULL,
  `api_endpoint` VARCHAR(255) NOT NULL,
  `api_key` VARCHAR(255) DEFAULT NULL,
  `api_secret` VARCHAR(255) DEFAULT NULL,
  `cost_per_check` REAL NOT NULL DEFAULT 0.00,
  `max_word_count` INTEGER NOT NULL DEFAULT 50000,
  `supported_formats` VARCHAR(255) NOT NULL DEFAULT 'txt,doc,docx,pdf',
  `priority` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `health_status` INTEGER NOT NULL DEFAULT 1,
  `last_health_check` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_check_api_provider` ON `bxw_check_api` (`provider`);
CREATE INDEX `idx_bxw_check_api_status` ON `bxw_check_api` (`status`);
CREATE INDEX `idx_bxw_check_api_priority` ON `bxw_check_api` (`priority`);

-- 查重任务表
DROP TABLE IF EXISTS `bxw_check_task`;
CREATE TABLE `bxw_check_task` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `file_path` VARCHAR(255) DEFAULT NULL,
  `file_name` VARCHAR(255) DEFAULT NULL,
  `file_size` INTEGER NOT NULL DEFAULT 0,
  `word_count` INTEGER NOT NULL DEFAULT 0,
  `check_api_id` INTEGER NOT NULL,
  `external_task_id` VARCHAR(100) DEFAULT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `similarity_rate` REAL DEFAULT NULL,
  `report_url` VARCHAR(255) DEFAULT NULL,
  `report_path` VARCHAR(255) DEFAULT NULL,
  `n8n_workflow_id` VARCHAR(100) DEFAULT NULL,
  `n8n_execution_id` VARCHAR(100) DEFAULT NULL,
  `cost` REAL NOT NULL DEFAULT 0.00,
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_check_task_user_id` ON `bxw_check_task` (`user_id`);
CREATE INDEX `idx_bxw_check_task_check_api_id` ON `bxw_check_task` (`check_api_id`);
CREATE INDEX `idx_bxw_check_task_status` ON `bxw_check_task` (`status`);
CREATE INDEX `idx_bxw_check_task_createtime` ON `bxw_check_task` (`createtime`);

-- 查重报告详情表
DROP TABLE IF EXISTS `bxw_check_detail`;
CREATE TABLE `bxw_check_detail` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `task_id` INTEGER NOT NULL,
  `paragraph_index` INTEGER NOT NULL,
  `original_text` TEXT NOT NULL,
  `similarity_rate` REAL NOT NULL,
  `matched_sources` TEXT,
  `suggestions` TEXT,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_check_detail_task_id` ON `bxw_check_detail` (`task_id`);
CREATE INDEX `idx_bxw_check_detail_similarity_rate` ON `bxw_check_detail` (`similarity_rate`);

-- =====================================================
-- 6. 文档导出相关表
-- =====================================================

-- 文档模板表
DROP TABLE IF EXISTS `bxw_document_template`;
CREATE TABLE `bxw_document_template` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `paper_type_id` INTEGER DEFAULT NULL,
  `template_content` TEXT NOT NULL,
  `style_config` TEXT,
  `is_default` INTEGER NOT NULL DEFAULT 0,
  `usage_count` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_document_template_type` ON `bxw_document_template` (`type`);
CREATE INDEX `idx_bxw_document_template_paper_type_id` ON `bxw_document_template` (`paper_type_id`);
CREATE INDEX `idx_bxw_document_template_status` ON `bxw_document_template` (`status`);

-- 文档导出记录表
DROP TABLE IF EXISTS `bxw_export_record`;
CREATE TABLE `bxw_export_record` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `source_type` VARCHAR(50) NOT NULL,
  `source_id` INTEGER NOT NULL,
  `export_format` VARCHAR(20) NOT NULL,
  `template_id` INTEGER DEFAULT NULL,
  `file_name` VARCHAR(255) NOT NULL,
  `file_path` VARCHAR(255) NOT NULL,
  `file_size` INTEGER NOT NULL DEFAULT 0,
  `download_count` INTEGER NOT NULL DEFAULT 0,
  `n8n_workflow_id` VARCHAR(100) DEFAULT NULL,
  `n8n_execution_id` VARCHAR(100) DEFAULT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_export_record_user_id` ON `bxw_export_record` (`user_id`);
CREATE INDEX `idx_bxw_export_record_source_type_id` ON `bxw_export_record` (`source_type`, `source_id`);
CREATE INDEX `idx_bxw_export_record_status` ON `bxw_export_record` (`status`);
CREATE INDEX `idx_bxw_export_record_createtime` ON `bxw_export_record` (`createtime`);

-- 发票管理表
DROP TABLE IF EXISTS `bxw_invoice`;
CREATE TABLE `bxw_invoice` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `order_id` INTEGER NOT NULL,
  `user_id` INTEGER NOT NULL,
  `invoice_type` VARCHAR(20) NOT NULL DEFAULT 'personal',
  `invoice_title` VARCHAR(200) NOT NULL,
  `tax_number` VARCHAR(50) DEFAULT NULL,
  `invoice_content` VARCHAR(500) DEFAULT NULL,
  `invoice_amount` REAL NOT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `invoice_code` VARCHAR(50) DEFAULT NULL,
  `invoice_number` VARCHAR(50) DEFAULT NULL,
  `invoice_url` VARCHAR(255) DEFAULT NULL,
  `invoice_file_path` VARCHAR(255) DEFAULT NULL,
  `apply_time` DATETIME NOT NULL,
  `issue_time` DATETIME DEFAULT NULL,
  `remark` VARCHAR(500) DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL,
  CONSTRAINT chk_invoice_type CHECK (invoice_type IN ('personal', 'company')),
  CONSTRAINT chk_invoice_status CHECK (status IN ('pending', 'processing', 'issued', 'failed', 'cancelled')),
  CONSTRAINT chk_invoice_amount CHECK (invoice_amount > 0)
);

CREATE INDEX `idx_bxw_invoice_order_id` ON `bxw_invoice` (`order_id`);
CREATE INDEX `idx_bxw_invoice_user_id` ON `bxw_invoice` (`user_id`);
CREATE INDEX `idx_bxw_invoice_status` ON `bxw_invoice` (`status`);
CREATE INDEX `idx_bxw_invoice_apply_time` ON `bxw_invoice` (`apply_time`);

-- =====================================================
-- 7. 订单支付相关表
-- =====================================================

-- 套餐配置表
DROP TABLE IF EXISTS `bxw_package`;
CREATE TABLE `bxw_package` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `description` TEXT,
  `credits` INTEGER NOT NULL DEFAULT 0,
  `vip_days` INTEGER NOT NULL DEFAULT 0,
  `writing_quota` INTEGER NOT NULL DEFAULT 0,
  `rewrite_quota` INTEGER NOT NULL DEFAULT 0,
  `check_quota` INTEGER NOT NULL DEFAULT 0,
  `original_price` REAL NOT NULL,
  `sale_price` REAL NOT NULL,
  `discount_rate` REAL NOT NULL DEFAULT 100.00,
  `sort` INTEGER NOT NULL DEFAULT 0,
  `is_hot` INTEGER NOT NULL DEFAULT 0,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL,
  CONSTRAINT chk_package_type CHECK (type IN ('credits', 'vip', 'combo')),
  CONSTRAINT chk_package_prices CHECK (original_price >= 0 AND sale_price >= 0 AND sale_price <= original_price),
  CONSTRAINT chk_package_quotas CHECK (credits >= 0 AND writing_quota >= 0 AND rewrite_quota >= 0 AND check_quota >= 0),
  CONSTRAINT chk_package_status CHECK (status IN (0, 1)),
  CONSTRAINT chk_package_is_hot CHECK (is_hot IN (0, 1))
);

CREATE INDEX `idx_bxw_package_type` ON `bxw_package` (`type`);
CREATE INDEX `idx_bxw_package_status` ON `bxw_package` (`status`);
CREATE INDEX `idx_bxw_package_sort` ON `bxw_package` (`sort`);

-- 订单表
DROP TABLE IF EXISTS `bxw_order`;
CREATE TABLE `bxw_order` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `order_no` VARCHAR(32) NOT NULL,
  `user_id` INTEGER NOT NULL,
  `package_id` INTEGER NOT NULL,
  `package_name` VARCHAR(100) NOT NULL,
  `original_price` REAL NOT NULL,
  `discount_amount` REAL NOT NULL DEFAULT 0.00,
  `final_price` REAL NOT NULL,
  `payment_method` VARCHAR(50) DEFAULT NULL,
  `payment_status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `transaction_id` VARCHAR(100) DEFAULT NULL,
  `paid_time` DATETIME DEFAULT NULL,
  `expired_time` DATETIME NOT NULL,
  `coupon_id` INTEGER DEFAULT NULL,
  `remark` VARCHAR(500) DEFAULT NULL,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL,
  CONSTRAINT chk_order_prices CHECK (original_price >= 0 AND final_price >= 0 AND discount_amount >= 0),
  CONSTRAINT chk_order_payment_status CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded', 'cancelled')),
  CONSTRAINT chk_order_payment_method CHECK (payment_method IS NULL OR payment_method IN ('wechat', 'alipay', 'bank', 'balance'))
);

CREATE UNIQUE INDEX `idx_bxw_order_order_no` ON `bxw_order` (`order_no`);
CREATE INDEX `idx_bxw_order_user_id` ON `bxw_order` (`user_id`);
CREATE INDEX `idx_bxw_order_package_id` ON `bxw_order` (`package_id`);
CREATE INDEX `idx_bxw_order_payment_status` ON `bxw_order` (`payment_status`);
CREATE INDEX `idx_bxw_order_createtime` ON `bxw_order` (`createtime`);

-- 优惠券表
DROP TABLE IF EXISTS `bxw_coupon`;
CREATE TABLE `bxw_coupon` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(50) NOT NULL,
  `type` VARCHAR(20) NOT NULL,
  `value` REAL NOT NULL,
  `min_amount` REAL NOT NULL DEFAULT 0.00,
  `max_discount` REAL NOT NULL DEFAULT 0.00,
  `total_quantity` INTEGER NOT NULL DEFAULT 0,
  `used_quantity` INTEGER NOT NULL DEFAULT 0,
  `per_user_limit` INTEGER NOT NULL DEFAULT 1,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NOT NULL,
  `applicable_packages` VARCHAR(500) DEFAULT NULL,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_coupon_code` ON `bxw_coupon` (`code`);
CREATE INDEX `idx_bxw_coupon_status` ON `bxw_coupon` (`status`);
CREATE INDEX `idx_bxw_coupon_start_end_time` ON `bxw_coupon` (`start_time`, `end_time`);

-- 用户优惠券表
DROP TABLE IF EXISTS `bxw_user_coupon`;
CREATE TABLE `bxw_user_coupon` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `coupon_id` INTEGER NOT NULL,
  `order_id` INTEGER DEFAULT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'unused',
  `used_time` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_user_coupon_user_id` ON `bxw_user_coupon` (`user_id`);
CREATE INDEX `idx_bxw_user_coupon_coupon_id` ON `bxw_user_coupon` (`coupon_id`);
CREATE INDEX `idx_bxw_user_coupon_status` ON `bxw_user_coupon` (`status`);

-- 积分变动记录表
DROP TABLE IF EXISTS `bxw_credits_log`;
CREATE TABLE `bxw_credits_log` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `amount` INTEGER NOT NULL,
  `balance_before` INTEGER NOT NULL,
  `balance_after` INTEGER NOT NULL,
  `related_id` INTEGER DEFAULT NULL,
  `related_type` VARCHAR(50) DEFAULT NULL,
  `description` VARCHAR(255) NOT NULL,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_credits_log_user_id` ON `bxw_credits_log` (`user_id`);
CREATE INDEX `idx_bxw_credits_log_type` ON `bxw_credits_log` (`type`);
CREATE INDEX `idx_bxw_credits_log_createtime` ON `bxw_credits_log` (`createtime`);

-- =====================================================
-- 8. 系统配置相关表
-- =====================================================

-- 系统配置表
DROP TABLE IF EXISTS `bxw_config`;
CREATE TABLE `bxw_config` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `group` VARCHAR(50) NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `title` VARCHAR(100) NOT NULL,
  `value` TEXT,
  `type` VARCHAR(20) NOT NULL DEFAULT 'string',
  `options` TEXT,
  `description` VARCHAR(500) DEFAULT NULL,
  `sort` INTEGER NOT NULL DEFAULT 0,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_config_group_name` ON `bxw_config` (`group`, `name`);
CREATE INDEX `idx_bxw_config_group` ON `bxw_config` (`group`);

-- 内容风控规则表
DROP TABLE IF EXISTS `bxw_content_filter`;
CREATE TABLE `bxw_content_filter` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `pattern` TEXT NOT NULL,
  `action` VARCHAR(20) NOT NULL DEFAULT 'block',
  `replacement` VARCHAR(500) DEFAULT NULL,
  `severity` VARCHAR(20) NOT NULL DEFAULT 'medium',
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_content_filter_type` ON `bxw_content_filter` (`type`);
CREATE INDEX `idx_bxw_content_filter_status` ON `bxw_content_filter` (`status`);

-- =====================================================
-- 9. 通知相关表
-- =====================================================

-- 消息模板表
DROP TABLE IF EXISTS `bxw_message_template`;
CREATE TABLE `bxw_message_template` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `code` VARCHAR(50) NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(20) NOT NULL,
  `subject` VARCHAR(255) DEFAULT NULL,
  `content` TEXT NOT NULL,
  `variables` TEXT,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_message_template_code` ON `bxw_message_template` (`code`);
CREATE INDEX `idx_bxw_message_template_type` ON `bxw_message_template` (`type`);
CREATE INDEX `idx_bxw_message_template_status` ON `bxw_message_template` (`status`);

-- 用户通知表
DROP TABLE IF EXISTS `bxw_user_notification`;
CREATE TABLE `bxw_user_notification` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `related_id` INTEGER DEFAULT NULL,
  `related_type` VARCHAR(50) DEFAULT NULL,
  `is_read` INTEGER NOT NULL DEFAULT 0,
  `read_time` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_user_notification_user_id` ON `bxw_user_notification` (`user_id`);
CREATE INDEX `idx_bxw_user_notification_type` ON `bxw_user_notification` (`type`);
CREATE INDEX `idx_bxw_user_notification_is_read` ON `bxw_user_notification` (`is_read`);
CREATE INDEX `idx_bxw_user_notification_createtime` ON `bxw_user_notification` (`createtime`);

-- 邮件发送记录表
DROP TABLE IF EXISTS `bxw_email_log`;
CREATE TABLE `bxw_email_log` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER DEFAULT NULL,
  `template_id` INTEGER DEFAULT NULL,
  `to_email` VARCHAR(255) NOT NULL,
  `subject` VARCHAR(255) NOT NULL,
  `content` TEXT NOT NULL,
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending',
  `error_message` TEXT,
  `sent_time` DATETIME DEFAULT NULL,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_email_log_user_id` ON `bxw_email_log` (`user_id`);
CREATE INDEX `idx_bxw_email_log_status` ON `bxw_email_log` (`status`);
CREATE INDEX `idx_bxw_email_log_createtime` ON `bxw_email_log` (`createtime`);

-- =====================================================
-- 10. n8n集成相关表
-- =====================================================

-- n8n工作流配置表
DROP TABLE IF EXISTS `bxw_n8n_workflow`;
CREATE TABLE `bxw_n8n_workflow` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `type` VARCHAR(50) NOT NULL,
  `n8n_workflow_id` VARCHAR(100) NOT NULL,
  `webhook_url` VARCHAR(255) DEFAULT NULL,
  `description` TEXT,
  `config` TEXT,
  `is_active` INTEGER NOT NULL DEFAULT 1,
  `status` INTEGER NOT NULL DEFAULT 1,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_n8n_workflow_n8n_id` ON `bxw_n8n_workflow` (`n8n_workflow_id`);
CREATE INDEX `idx_bxw_n8n_workflow_type` ON `bxw_n8n_workflow` (`type`);
CREATE INDEX `idx_bxw_n8n_workflow_status` ON `bxw_n8n_workflow` (`status`);

-- n8n执行记录表
DROP TABLE IF EXISTS `bxw_n8n_execution`;
CREATE TABLE `bxw_n8n_execution` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `workflow_id` INTEGER NOT NULL,
  `n8n_execution_id` VARCHAR(100) NOT NULL,
  `task_type` VARCHAR(50) NOT NULL,
  `task_id` INTEGER NOT NULL,
  `user_id` INTEGER NOT NULL,
  `input_data` TEXT,
  `output_data` TEXT,
  `status` VARCHAR(20) NOT NULL DEFAULT 'running',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME DEFAULT NULL,
  `duration` INTEGER DEFAULT NULL,
  `error_message` TEXT,
  `createtime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_n8n_execution_n8n_id` ON `bxw_n8n_execution` (`n8n_execution_id`);
CREATE INDEX `idx_bxw_n8n_execution_workflow_id` ON `bxw_n8n_execution` (`workflow_id`);
CREATE INDEX `idx_bxw_n8n_execution_task_type_id` ON `bxw_n8n_execution` (`task_type`, `task_id`);
CREATE INDEX `idx_bxw_n8n_execution_user_id` ON `bxw_n8n_execution` (`user_id`);
CREATE INDEX `idx_bxw_n8n_execution_status` ON `bxw_n8n_execution` (`status`);
CREATE INDEX `idx_bxw_n8n_execution_start_time` ON `bxw_n8n_execution` (`start_time`);

-- =====================================================
-- 11. 日志相关表
-- =====================================================

-- 操作日志表
DROP TABLE IF EXISTS `bxw_operation_log`;
CREATE TABLE `bxw_operation_log` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER DEFAULT NULL,
  `admin_id` INTEGER DEFAULT NULL,
  `module` VARCHAR(50) NOT NULL,
  `action` VARCHAR(50) NOT NULL,
  `description` VARCHAR(500) NOT NULL,
  `request_data` TEXT,
  `response_data` TEXT,
  `ip_address` VARCHAR(50) NOT NULL,
  `user_agent` VARCHAR(500) DEFAULT NULL,
  `execution_time` INTEGER NOT NULL DEFAULT 0,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_operation_log_user_id` ON `bxw_operation_log` (`user_id`);
CREATE INDEX `idx_bxw_operation_log_admin_id` ON `bxw_operation_log` (`admin_id`);
CREATE INDEX `idx_bxw_operation_log_module` ON `bxw_operation_log` (`module`);
CREATE INDEX `idx_bxw_operation_log_action` ON `bxw_operation_log` (`action`);
CREATE INDEX `idx_bxw_operation_log_createtime` ON `bxw_operation_log` (`createtime`);

-- 错误日志表
DROP TABLE IF EXISTS `bxw_error_log`;
CREATE TABLE `bxw_error_log` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `level` VARCHAR(20) NOT NULL,
  `category` VARCHAR(50) NOT NULL,
  `message` VARCHAR(1000) NOT NULL,
  `context` TEXT,
  `stack_trace` TEXT,
  `user_id` INTEGER DEFAULT NULL,
  `request_id` VARCHAR(100) DEFAULT NULL,
  `ip_address` VARCHAR(50) DEFAULT NULL,
  `user_agent` VARCHAR(500) DEFAULT NULL,
  `createtime` DATETIME NOT NULL
);

CREATE INDEX `idx_bxw_error_log_level` ON `bxw_error_log` (`level`);
CREATE INDEX `idx_bxw_error_log_category` ON `bxw_error_log` (`category`);
CREATE INDEX `idx_bxw_error_log_user_id` ON `bxw_error_log` (`user_id`);
CREATE INDEX `idx_bxw_error_log_createtime` ON `bxw_error_log` (`createtime`);

-- =====================================================
-- 12. 统计分析相关表
-- =====================================================

-- 用户行为统计表
DROP TABLE IF EXISTS `bxw_user_stats`;
CREATE TABLE `bxw_user_stats` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `user_id` INTEGER NOT NULL,
  `date` DATE NOT NULL,
  `login_count` INTEGER NOT NULL DEFAULT 0,
  `writing_count` INTEGER NOT NULL DEFAULT 0,
  `rewrite_count` INTEGER NOT NULL DEFAULT 0,
  `check_count` INTEGER NOT NULL DEFAULT 0,
  `export_count` INTEGER NOT NULL DEFAULT 0,
  `credits_consumed` INTEGER NOT NULL DEFAULT 0,
  `online_duration` INTEGER NOT NULL DEFAULT 0,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_user_stats_user_date` ON `bxw_user_stats` (`user_id`, `date`);
CREATE INDEX `idx_bxw_user_stats_date` ON `bxw_user_stats` (`date`);
CREATE INDEX `idx_stats_date_user` ON `bxw_user_stats` (`date`, `user_id`);
CREATE INDEX `idx_user_stats_activity` ON `bxw_user_stats` (`user_id`, `date`, `login_count`);

-- 系统统计表
DROP TABLE IF EXISTS `bxw_system_stats`;
CREATE TABLE `bxw_system_stats` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `date` DATE NOT NULL,
  `new_users` INTEGER NOT NULL DEFAULT 0,
  `active_users` INTEGER NOT NULL DEFAULT 0,
  `total_orders` INTEGER NOT NULL DEFAULT 0,
  `total_revenue` REAL NOT NULL DEFAULT 0.00,
  `writing_tasks` INTEGER NOT NULL DEFAULT 0,
  `rewrite_tasks` INTEGER NOT NULL DEFAULT 0,
  `check_tasks` INTEGER NOT NULL DEFAULT 0,
  `ai_api_calls` INTEGER NOT NULL DEFAULT 0,
  `ai_api_cost` REAL NOT NULL DEFAULT 0.000000,
  `error_count` INTEGER NOT NULL DEFAULT 0,
  `createtime` DATETIME NOT NULL,
  `updatetime` DATETIME NOT NULL
);

CREATE UNIQUE INDEX `idx_bxw_system_stats_date` ON `bxw_system_stats` (`date`);

-- =====================================================
-- 13. 初始化数据
-- =====================================================

-- 插入默认论文类型
INSERT INTO `bxw_paper_type` (`name`, `description`, `word_count_min`, `word_count_max`, `sort`, `createtime`, `updatetime`) VALUES
('毕业论文', '本科、硕士、博士毕业论文', 8000, 50000, 1, datetime('now'), datetime('now')),
('学术论文', '期刊论文、会议论文', 3000, 15000, 2, datetime('now'), datetime('now')),
('综述论文', '文献综述、研究综述', 5000, 20000, 3, datetime('now'), datetime('now')),
('开题报告', '研究开题报告', 2000, 8000, 4, datetime('now'), datetime('now')),
('课程论文', '课程作业论文', 1000, 5000, 5, datetime('now'), datetime('now'));

-- 插入默认AI模型配置
INSERT INTO `bxw_ai_model` (`name`, `provider`, `model_code`, `api_endpoint`, `max_tokens`, `temperature`, `cost_per_1k_tokens`, `priority`, `createtime`, `updatetime`) VALUES
('GPT-4', 'openai', 'gpt-4', 'https://api.openai.com/v1/chat/completions', 8000, 0.70, 0.030000, 100, datetime('now'), datetime('now')),
('GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', 'https://api.openai.com/v1/chat/completions', 4000, 0.70, 0.002000, 90, datetime('now'), datetime('now')),
('文心一言', 'baidu', 'ernie-bot', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions', 4000, 0.70, 0.001200, 80, datetime('now'), datetime('now')),
('通义千问', 'aliyun', 'qwen-max', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 6000, 0.70, 0.002000, 85, datetime('now'), datetime('now'));

-- 插入默认套餐配置
INSERT INTO `bxw_package` (`name`, `type`, `description`, `credits`, `writing_quota`, `rewrite_quota`, `check_quota`, `original_price`, `sale_price`, `sort`, `createtime`, `updatetime`) VALUES
('体验套餐', 'credits', '新用户体验套餐，包含基础功能', 100, 2, 5, 1, 19.90, 9.90, 1, datetime('now'), datetime('now')),
('标准套餐', 'credits', '标准用户套餐，适合日常使用', 500, 10, 20, 5, 99.00, 79.00, 2, datetime('now'), datetime('now')),
('专业套餐', 'credits', '专业用户套餐，功能全面', 1000, 20, 50, 10, 199.00, 149.00, 3, datetime('now'), datetime('now')),
('VIP月卡', 'vip', 'VIP会员月卡，享受更多特权', 0, 0, 0, 0, 99.00, 89.00, 4, datetime('now'), datetime('now')),
('VIP年卡', 'vip', 'VIP会员年卡，超值优惠', 0, 0, 0, 0, 999.00, 699.00, 5, datetime('now'), datetime('now'));

-- 插入默认系统配置
INSERT INTO `bxw_config` (`group`, `name`, `title`, `value`, `type`, `description`, `sort`, `createtime`, `updatetime`) VALUES
('system', 'site_name', '网站名称', 'AI论文写作平台', 'string', '网站名称配置', 1, datetime('now'), datetime('now')),
('system', 'site_description', '网站描述', '基于AI的智能论文写作平台', 'string', '网站描述配置', 2, datetime('now'), datetime('now')),
('system', 'default_credits', '新用户默认积分', '50', 'number', '新用户注册默认获得的积分', 3, datetime('now'), datetime('now')),
('ai', 'default_model', '默认AI模型', 'gpt-3.5-turbo', 'string', '系统默认使用的AI模型', 1, datetime('now'), datetime('now')),
('ai', 'max_concurrent_tasks', '最大并发任务数', '10', 'number', '系统最大并发处理任务数', 2, datetime('now'), datetime('now')),
('payment', 'wechat_enabled', '微信支付启用', 'true', 'boolean', '是否启用微信支付', 1, datetime('now'), datetime('now')),
('payment', 'alipay_enabled', '支付宝支付启用', 'true', 'boolean', '是否启用支付宝支付', 2, datetime('now'), datetime('now')),
('notification', 'email_enabled', '邮件通知启用', 'true', 'boolean', '是否启用邮件通知', 1, datetime('now'), datetime('now')),
('notification', 'sms_enabled', '短信通知启用', 'false', 'boolean', '是否启用短信通知', 2, datetime('now'), datetime('now'));

-- 插入默认消息模板
INSERT INTO `bxw_message_template` (`code`, `name`, `type`, `subject`, `content`, `variables`, `createtime`, `updatetime`) VALUES
('task_completed', '任务完成通知', 'system', NULL, '您的{task_type}任务"{task_title}"已完成，请及时查看结果。', '{"task_type":"任务类型","task_title":"任务标题"}', datetime('now'), datetime('now')),
('payment_success', '支付成功通知', 'system', NULL, '您的订单{order_no}支付成功，金额：￥{amount}，感谢您的支持！', '{"order_no":"订单号","amount":"支付金额"}', datetime('now'), datetime('now')),
('welcome_email', '欢迎邮件', 'email', '欢迎使用AI论文写作平台', '亲爱的{username}，欢迎使用我们的AI论文写作平台！您已获得{credits}积分，可以开始体验我们的服务。', '{"username":"用户名","credits":"积分数量"}', datetime('now'), datetime('now')),
('draft_auto_save', '草稿自动保存通知', 'system', NULL, '您的论文"{title}"草稿已自动保存，版本号：{version}', '{"title":"论文标题","version":"版本号"}', datetime('now'), datetime('now')),
('invoice_issued', '发票开具通知', 'system', NULL, '您申请的发票已开具完成，发票号：{invoice_number}，请及时下载。', '{"invoice_number":"发票号码"}', datetime('now'), datetime('now')),
('task_failed', '任务失败通知', 'system', NULL, '您的{task_type}任务"{task_title}"执行失败，错误信息：{error_message}', '{"task_type":"任务类型","task_title":"任务标题","error_message":"错误信息"}', datetime('now'), datetime('now'));

-- 插入默认n8n工作流配置
INSERT INTO `bxw_n8n_workflow` (`name`, `type`, `n8n_workflow_id`, `description`, `createtime`, `updatetime`) VALUES
('论文大纲生成', 'outline', 'outline-generation-workflow', '根据用户输入生成论文大纲的工作流', datetime('now'), datetime('now')),
('论文写作', 'writing', 'paper-writing-workflow', '根据大纲生成论文内容的工作流', datetime('now'), datetime('now')),
('语义降重', 'rewrite', 'text-rewrite-workflow', '对文本进行语义降重的工作流', datetime('now'), datetime('now')),
('论文查重', 'check', 'plagiarism-check-workflow', '提交论文进行查重检测的工作流', datetime('now'), datetime('now')),
('文档导出', 'export', 'document-export-workflow', '将内容导出为DOCX或PDF的工作流', datetime('now'), datetime('now'));

-- =====================================================
-- 外键约束配置（生产环境建议启用）
-- =====================================================

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 重要关联关系的外键约束（可选，根据需要启用）
-- 注意：SQLite的外键约束相对简单，复杂关联建议在应用层处理

-- 用户相关外键
-- ALTER TABLE bxw_user_quota ADD CONSTRAINT fk_user_quota_user
--     FOREIGN KEY (user_id) REFERENCES bxw_user(id) ON DELETE CASCADE;

-- 论文项目相关外键
-- ALTER TABLE bxw_paper_project ADD CONSTRAINT fk_paper_project_user
--     FOREIGN KEY (user_id) REFERENCES bxw_user(id) ON DELETE CASCADE;
-- ALTER TABLE bxw_paper_project ADD CONSTRAINT fk_paper_project_type
--     FOREIGN KEY (paper_type_id) REFERENCES bxw_paper_type(id) ON DELETE RESTRICT;
-- ALTER TABLE bxw_paper_project ADD CONSTRAINT fk_paper_project_parent
--     FOREIGN KEY (parent_id) REFERENCES bxw_paper_project(id) ON DELETE SET NULL;

-- 订单相关外键
-- ALTER TABLE bxw_order ADD CONSTRAINT fk_order_user
--     FOREIGN KEY (user_id) REFERENCES bxw_user(id) ON DELETE CASCADE;
-- ALTER TABLE bxw_order ADD CONSTRAINT fk_order_package
--     FOREIGN KEY (package_id) REFERENCES bxw_package(id) ON DELETE RESTRICT;

-- 发票相关外键
-- ALTER TABLE bxw_invoice ADD CONSTRAINT fk_invoice_order
--     FOREIGN KEY (order_id) REFERENCES bxw_order(id) ON DELETE CASCADE;
-- ALTER TABLE bxw_invoice ADD CONSTRAINT fk_invoice_user
--     FOREIGN KEY (user_id) REFERENCES bxw_user(id) ON DELETE CASCADE;

-- =====================================================
-- SQLite版本数据库设计说明
-- =====================================================
/*
SQLite适配说明：

1. 数据类型转换：
   - int(10) unsigned → INTEGER
   - varchar(n) → VARCHAR(n)
   - decimal(m,n) → REAL
   - tinyint(1) → INTEGER
   - datetime → DATETIME
   - text/longtext → TEXT

2. 主键和自增：
   - AUTO_INCREMENT → AUTOINCREMENT
   - PRIMARY KEY (`id`) → INTEGER PRIMARY KEY AUTOINCREMENT

3. 索引创建：
   - 使用独立的 CREATE INDEX 语句
   - 唯一索引使用 CREATE UNIQUE INDEX
   - 复合索引正常支持

4. 外键约束：
   - SQLite默认不启用外键约束
   - 使用 PRAGMA foreign_keys = ON 启用
   - 注意：SQLite的外键约束相对简单，建议在应用层处理复杂关联

5. 函数替换：
   - NOW() → datetime('now')
   - MySQL特有函数需要替换为SQLite兼容函数

6. 字符集和引擎：
   - 移除了 ENGINE=InnoDB 和 CHARSET=utf8mb4
   - SQLite默认支持UTF-8

7. 注释：
   - SQLite不支持列注释，已移除COMMENT部分
   - 表注释也已移除

主要功能模块保持不变：
- 用户管理：用户信息、配额、VIP等
- 论文写作：项目、章节、模板等（新增草稿箱管理功能）
- AI模型：配置、使用统计等
- 降重功能：任务、结果等
- 查重功能：接口配置、任务、报告等
- 文档导出：模板、记录等
- 订单支付：套餐、订单、优惠券、积分等（新增发票管理功能）
- 系统配置：参数配置、风控规则等
- 通知系统：模板、用户通知、邮件日志等
- n8n集成：工作流配置、执行记录等
- 日志统计：操作日志、错误日志、统计数据等

## 🆕 本次优化更新内容：

### 1. 草稿箱功能增强
- 在 bxw_paper_project 表中新增草稿相关字段：
  * is_draft: 是否草稿标识
  * draft_version: 草稿版本号
  * parent_id: 父项目ID（用于草稿关联）
- 新增草稿相关索引，优化草稿查询性能

### 2. 发票管理功能
- 新增 bxw_invoice 表，支持完整的发票管理流程：
  * 个人/企业发票类型
  * 发票申请、开具、下载全流程
  * 发票状态跟踪和文件管理
- 支持电子发票和纸质发票管理

### 3. 数据完整性增强
- 新增多个CHECK约束，确保数据有效性：
  * 用户积分非负约束
  * 订单金额合理性约束
  * 套餐价格逻辑约束
  * 状态值枚举约束
- 预留外键约束配置（可根据需要启用）

### 4. 索引优化
- 新增复合索引，优化常用查询：
  * 用户任务状态时间复合索引
  * AI使用统计复合索引
  * 用户统计活跃度索引
- 提升查询性能，特别是统计和分析场景

### 5. 消息模板扩展
- 新增草稿自动保存通知模板
- 新增发票开具通知模板
- 新增任务失败通知模板
- 完善通知体系，提升用户体验

使用建议：
1. 导入前确保SQLite版本支持外键约束（3.6.19+）
2. 如果不需要外键约束，可以移除相关PRAGMA语句
3. 生产环境建议使用MySQL/PostgreSQL等更强大的数据库
4. SQLite适合开发测试和小型应用
5. 生产环境建议启用外键约束，取消注释相关ALTER TABLE语句
6. 根据实际业务需求调整CHECK约束条件
7. 定期执行VACUUM命令优化数据库性能
8. 建议定期备份数据库文件

## 🔧 维护建议：
1. 定期执行 PRAGMA integrity_check; 检查数据库完整性
2. 使用 PRAGMA optimize; 优化查询计划
3. 监控数据库文件大小，及时清理过期数据
4. 对于大量数据的表，考虑分区或归档策略
*/
