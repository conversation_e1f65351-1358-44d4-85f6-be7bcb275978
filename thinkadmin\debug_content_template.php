<?php

/**
 * 调试正文模板管理页面
 */

echo "=== 调试正文模板管理页面 ===\n\n";

try {
    // 1. 检查控制器语法
    echo "1. 检查控制器语法:\n";
    $output = [];
    $returnCode = 0;
    exec("php -l app/admin/controller/ContentTemplate.php 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ ContentTemplate.php - 语法正确\n";
    } else {
        echo "  ❌ ContentTemplate.php - 语法错误: " . implode(' ', $output) . "\n";
        exit(1);
    }
    
    // 2. 检查模型文件
    echo "\n2. 检查模型文件:\n";
    $modelFile = 'app/admin/model/DocumentTemplate.php';
    if (file_exists($modelFile)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$modelFile} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ DocumentTemplate.php - 语法正确\n";
        } else {
            echo "  ❌ DocumentTemplate.php - 语法错误\n";
        }
    } else {
        echo "  ❌ DocumentTemplate.php - 文件不存在\n";
    }
    
    // 3. 检查PaperType模型
    echo "\n3. 检查PaperType模型:\n";
    $paperTypeFile = 'app/admin/model/PaperType.php';
    if (file_exists($paperTypeFile)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$paperTypeFile} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ PaperType.php - 语法正确\n";
        } else {
            echo "  ❌ PaperType.php - 语法错误\n";
        }
    } else {
        echo "  ❌ PaperType.php - 文件不存在\n";
    }
    
    // 4. 检查模板文件
    echo "\n4. 检查模板文件:\n";
    $templateFile = 'app/admin/view/content_template/index.html';
    if (file_exists($templateFile)) {
        echo "  ✅ index.html - 模板文件存在\n";
        
        $templateContent = file_get_contents($templateFile);
        
        // 检查模板语法
        if (strpos($templateContent, '{extend name=') !== false) {
            echo "  ✅ 包含模板继承\n";
        }
        
        if (strpos($templateContent, '{foreach $list') !== false) {
            echo "  ✅ 包含数据循环\n";
        }
        
        if (strpos($templateContent, '$paperTypes') !== false) {
            echo "  ✅ 包含论文类型变量\n";
        }
    } else {
        echo "  ❌ index.html - 模板文件不存在\n";
    }
    
    // 5. 数据库连接和数据检查
    echo "\n5. 数据库连接和数据检查:\n";
    
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "  ✅ 数据库连接成功\n";
        
        // 检查document_template表
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_template WHERE type = 'content'");
        $templateCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 正文模板数量: {$templateCount}\n";
        
        // 检查paper_type表
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM paper_type WHERE status = 1");
        $typeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 启用的论文类型: {$typeCount}\n";
        
        // 检查具体数据
        $stmt = $pdo->query("SELECT id, name, paper_type_id, is_default, status FROM document_template WHERE type = 'content' LIMIT 5");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($templates)) {
            echo "  ✅ 正文模板数据示例:\n";
            foreach ($templates as $template) {
                $defaultText = $template['is_default'] ? ' (默认)' : '';
                $statusText = $template['status'] ? '启用' : '禁用';
                echo "    - ID:{$template['id']} {$template['name']} - {$statusText}{$defaultText}\n";
            }
        }
        
    } else {
        echo "  ❌ 数据库文件不存在\n";
    }
    
    // 6. 模拟控制器执行
    echo "\n6. 模拟控制器执行:\n";
    
    // 创建一个简单的测试脚本
    $testScript = '<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set("display_errors", 1);

// 模拟基本环境
define("ROOT_PATH", __DIR__ . "/");

// 检查类是否可以加载
try {
    // 检查DocumentTemplate模型是否存在
    if (file_exists("app/admin/model/DocumentTemplate.php")) {
        echo "✅ DocumentTemplate模型文件存在\\n";
    } else {
        echo "❌ DocumentTemplate模型文件不存在\\n";
    }
    
    // 检查PaperType模型是否存在
    if (file_exists("app/admin/model/PaperType.php")) {
        echo "✅ PaperType模型文件存在\\n";
    } else {
        echo "❌ PaperType模型文件不存在\\n";
    }
    
    // 检查数据库连接
    $dbPath = __DIR__ . "/database/sqlite.db";
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        echo "✅ 数据库连接测试成功\\n";
    } else {
        echo "❌ 数据库文件不存在\\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\\n";
}
';
    
    file_put_contents('temp_controller_test.php', $testScript);
    
    $output = [];
    $returnCode = 0;
    exec("php temp_controller_test.php 2>&1", $output, $returnCode);
    
    echo "  " . implode("\n  ", $output) . "\n";
    
    // 清理临时文件
    if (file_exists('temp_controller_test.php')) {
        unlink('temp_controller_test.php');
    }
    
    echo "\n🔍 可能的500错误原因:\n";
    echo "1. QueryHelper的getInputData方法bug\n";
    echo "2. 模型类加载失败\n";
    echo "3. 数据库连接问题\n";
    echo "4. 模板渲染错误\n";
    echo "5. 权限或路由配置问题\n";
    
    echo "\n💡 建议解决方案:\n";
    echo "1. 检查ThinkAdmin的错误日志文件\n";
    echo "2. 开启调试模式查看详细错误\n";
    echo "3. 使用完全的备用查询方案\n";
    echo "4. 检查服务器PHP错误日志\n";
    
} catch (Exception $e) {
    echo "❌ 调试过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 调试完成 ===\n";
