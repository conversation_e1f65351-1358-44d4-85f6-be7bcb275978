<?php

namespace app\admin\controller;

use app\admin\model\OrderModel;
use think\admin\Controller;

/**
 * 订单管理
 * @class Order
 * @package app\admin\controller
 */
class Order extends Controller
{
    /**
     * 订单管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        OrderModel::mQuery($this)->layPage(function () {
            $this->title = '订单管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加订单管理
     * @auth true
     */
    public function add()
    {
        OrderModel::mForm('order/form');
    }

    /**
     * 编辑订单管理
     * @auth true
     */
    public function edit()
    {
        OrderModel::mForm('order/form');
    }

    /**
     * 删除订单管理
     * @auth true
     */
    public function remove()
    {
        OrderModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        OrderModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}