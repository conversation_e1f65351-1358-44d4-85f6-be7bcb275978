<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

use think\admin\extend\PhinxExtend;
use think\migration\Migrator;

@set_time_limit(0);
@ini_set('memory_limit', -1);

/**
 * 论文项目管理模块数据
 */
class InstallPaperProject extends Migrator
{

    /**
     * 获取脚本名称
     * @return string
     */
    public function getName(): string
    {
        return 'PaperProjectPlugin';
    }

    /**
     * 创建数据库
     */
    public function change()
    {
        // 写作中心模块
        $this->_create_paper_type();
        $this->_create_outline_template();
        $this->_create_prompt_template();
        $this->_create_paper_project();
        $this->_create_paper_section();

        // AI模型管理
        $this->_create_ai_model();
        $this->_create_ai_usage_log();

        // 降重与查重模块
        $this->_create_rewrite_task();
        $this->_create_rewrite_result();
        $this->_create_check_api();
        $this->_create_check_task();
        $this->_create_check_detail();

        // 文档导出模块
        $this->_create_document_template();
        $this->_create_export_record();

        // 发票管理模块
        $this->_create_invoice();

        // 通知消息模块
        $this->_create_message_template();
        $this->_create_user_notification();
        $this->_create_email_log();

        // 系统配置模块
        $this->_create_config();
        $this->_create_content_filter();

        // n8n集成模块
        $this->_create_n8n_workflow();
        $this->_create_n8n_execution();

        // 初始化菜单
        $this->_insert_menu();
    }

    /**
     * 创建论文类型表
     * @return void
     */
    private function _create_paper_type()
    {
        $table = $this->table('paper_type', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '论文类型表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '类型名称']],
            ['description', 'text', ['null' => true, 'comment' => '类型描述']],
            ['word_count_min', 'integer', ['limit' => 10, 'default' => 6000, 'null' => false, 'comment' => '最小字数']],
            ['word_count_max', 'integer', ['limit' => 10, 'default' => 30000, 'null' => false, 'comment' => '最大字数']],
            ['outline_template_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '默认大纲模板ID']],
            ['prompt_template_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '默认提示词模板ID']],
            ['sort', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '排序']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'status', 'sort', 'create_time'
        ], true);
    }

    /**
     * 创建大纲模板表
     * @return void
     */
    private function _create_outline_template()
    {
        $table = $this->table('outline_template', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '大纲模板表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模板名称']],
            ['paper_type_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '论文类型ID']],
            ['template_content', 'text', ['null' => false, 'comment' => '模板内容']],
            ['description', 'text', ['null' => true, 'comment' => '模板描述']],
            ['is_default', 'integer', ['limit' => 1, 'default' => 0, 'null' => false, 'comment' => '是否默认模板']],
            ['usage_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '使用次数']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'paper_type_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建提示词模板表
     * @return void
     */
    private function _create_prompt_template()
    {
        $table = $this->table('prompt_template', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '提示词模板表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模板名称']],
            ['type', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '模板类型']],
            ['paper_type_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '论文类型ID']],
            ['prompt_content', 'text', ['null' => false, 'comment' => '提示词内容']],
            ['variables', 'text', ['null' => true, 'comment' => '变量定义JSON']],
            ['ai_model', 'string', ['limit' => 50, 'null' => true, 'comment' => '推荐AI模型']],
            ['description', 'text', ['null' => true, 'comment' => '模板描述']],
            ['is_default', 'integer', ['limit' => 1, 'default' => 0, 'null' => false, 'comment' => '是否默认模板']],
            ['usage_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '使用次数']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'type', 'paper_type_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建论文项目表
     * @return void
     */
    private function _create_paper_project()
    {
        $table = $this->table('paper_project', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '论文项目表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['title', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '论文标题']],
            ['paper_type_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '论文类型ID']],
            ['subject', 'string', ['limit' => 100, 'null' => true, 'comment' => '学科专业']],
            ['keywords', 'string', ['limit' => 500, 'null' => true, 'comment' => '关键词']],
            ['requirements', 'text', ['null' => true, 'comment' => '具体要求']],
            ['target_word_count', 'integer', ['limit' => 10, 'default' => 10000, 'null' => false, 'comment' => '目标字数']],
            ['writing_style', 'string', ['limit' => 50, 'default' => 'academic', 'null' => false, 'comment' => '写作风格']],
            ['outline_content', 'text', ['null' => true, 'comment' => '大纲内容']],
            ['outline_version', 'integer', ['limit' => 10, 'default' => 1, 'null' => false, 'comment' => '大纲版本']],
            ['content', 'text', ['null' => true, 'comment' => '论文内容']],
            ['current_word_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '当前字数']],
            ['status', 'string', ['limit' => 20, 'default' => 'draft', 'null' => false, 'comment' => '状态']],
            ['is_draft', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '是否草稿(0正式1草稿)']],
            ['draft_version', 'integer', ['limit' => 10, 'default' => 1, 'null' => false, 'comment' => '草稿版本号']],
            ['parent_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '父项目ID(草稿关联)']],
            ['n8n_workflow_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n工作流ID']],
            ['n8n_execution_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n执行ID']],
            ['progress', 'integer', ['limit' => 3, 'default' => 0, 'null' => false, 'comment' => '进度百分比']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'user_id', 'paper_type_id', 'status', 'is_draft', 'parent_id', 'create_time'
        ], true);
    }

    /**
     * 创建论文章节表
     * @return void
     */
    private function _create_paper_section()
    {
        $table = $this->table('paper_section', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '论文章节表',
        ]);
        PhinxExtend::upgrade($table, [
            ['project_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '项目ID']],
            ['section_title', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '章节标题']],
            ['section_level', 'integer', ['limit' => 2, 'default' => 1, 'null' => false, 'comment' => '章节级别']],
            ['section_order', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '章节顺序']],
            ['content', 'text', ['null' => true, 'comment' => '章节内容']],
            ['word_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '字数']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '状态']],
            ['ai_model_used', 'string', ['limit' => 50, 'null' => true, 'comment' => '使用的AI模型']],
            ['generation_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '生成时间']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'project_id', 'status', 'section_order', 'create_time'
        ], true);
    }

    /**
     * 创建AI模型配置表
     * @return void
     */
    private function _create_ai_model()
    {
        $table = $this->table('ai_model', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => 'AI模型配置表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模型名称']],
            ['provider', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '提供商']],
            ['model_code', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模型代码']],
            ['api_endpoint', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => 'API端点']],
            ['api_key', 'string', ['limit' => 255, 'null' => true, 'comment' => 'API密钥']],
            ['max_tokens', 'integer', ['limit' => 10, 'default' => 4000, 'null' => false, 'comment' => '最大token数']],
            ['temperature', 'decimal', ['precision' => 3, 'scale' => 2, 'default' => 0.70, 'null' => false, 'comment' => '温度参数']],
            ['top_p', 'decimal', ['precision' => 3, 'scale' => 2, 'default' => 1.00, 'null' => false, 'comment' => 'top_p参数']],
            ['frequency_penalty', 'decimal', ['precision' => 3, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '频率惩罚']],
            ['presence_penalty', 'decimal', ['precision' => 3, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '存在惩罚']],
            ['cost_per_1k_tokens', 'decimal', ['precision' => 8, 'scale' => 6, 'default' => 0.000000, 'null' => false, 'comment' => '每1k token成本']],
            ['priority', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '优先级']],
            ['rate_limit_per_minute', 'integer', ['limit' => 10, 'default' => 60, 'null' => false, 'comment' => '每分钟限制']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['health_status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '健康状态']],
            ['last_health_check', 'datetime', ['default' => null, 'null' => true, 'comment' => '最后健康检查时间']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'provider', 'model_code', 'status', 'priority', 'create_time'
        ], true);
    }

    /**
     * 创建AI使用统计表
     * @return void
     */
    private function _create_ai_usage_log()
    {
        $table = $this->table('ai_usage_log', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => 'AI使用统计表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['ai_model_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => 'AI模型ID']],
            ['task_type', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '任务类型']],
            ['task_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '任务ID']],
            ['prompt_tokens', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '提示词token数']],
            ['completion_tokens', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '完成token数']],
            ['total_tokens', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '总token数']],
            ['cost', 'decimal', ['precision' => 8, 'scale' => 6, 'default' => 0.000000, 'null' => false, 'comment' => '成本']],
            ['response_time', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '响应时间(毫秒)']],
            ['status', 'string', ['limit' => 20, 'default' => 'success', 'null' => false, 'comment' => '状态']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'user_id', 'ai_model_id', 'task_type', 'create_time'
        ], true);
    }

    /**
     * 创建降重任务表
     * @return void
     */
    private function _create_rewrite_task()
    {
        $table = $this->table('rewrite_task', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '降重任务表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['title', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '任务标题']],
            ['original_text', 'text', ['null' => false, 'comment' => '原始文本']],
            ['original_word_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '原始字数']],
            ['rewrite_mode', 'string', ['limit' => 50, 'default' => 'standard', 'null' => false, 'comment' => '降重模式']],
            ['target_similarity', 'decimal', ['precision' => 5, 'scale' => 2, 'default' => 30.00, 'null' => false, 'comment' => '目标相似度']],
            ['ai_model_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => 'AI模型ID']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '状态']],
            ['n8n_workflow_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n工作流ID']],
            ['n8n_execution_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n执行ID']],
            ['progress', 'integer', ['limit' => 3, 'default' => 0, 'null' => false, 'comment' => '进度百分比']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'user_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建降重结果表
     * @return void
     */
    private function _create_rewrite_result()
    {
        $table = $this->table('rewrite_result', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '降重结果表',
        ]);
        PhinxExtend::upgrade($table, [
            ['task_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '任务ID']],
            ['ai_model_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => 'AI模型ID']],
            ['rewritten_text', 'text', ['null' => false, 'comment' => '降重后文本']],
            ['rewritten_word_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '降重后字数']],
            ['similarity_score', 'decimal', ['precision' => 5, 'scale' => 2, 'null' => true, 'comment' => '相似度分数']],
            ['quality_score', 'decimal', ['precision' => 5, 'scale' => 2, 'null' => true, 'comment' => '质量分数']],
            ['processing_time', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '处理时间(秒)']],
            ['is_selected', 'integer', ['limit' => 1, 'default' => 0, 'null' => false, 'comment' => '是否选中']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'task_id', 'ai_model_id', 'create_time'
        ], true);
    }

    /**
     * 创建查重接口配置表
     * @return void
     */
    private function _create_check_api()
    {
        $table = $this->table('check_api', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '查重接口配置表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '接口名称']],
            ['provider', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '提供商']],
            ['api_endpoint', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => 'API端点']],
            ['api_key', 'string', ['limit' => 255, 'null' => true, 'comment' => 'API密钥']],
            ['api_secret', 'string', ['limit' => 255, 'null' => true, 'comment' => 'API密钥']],
            ['cost_per_check', 'decimal', ['precision' => 8, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '每次查重成本']],
            ['max_word_count', 'integer', ['limit' => 10, 'default' => 50000, 'null' => false, 'comment' => '最大字数限制']],
            ['supported_formats', 'string', ['limit' => 255, 'default' => 'txt,doc,docx,pdf', 'null' => false, 'comment' => '支持格式']],
            ['priority', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '优先级']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['health_status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '健康状态']],
            ['last_health_check', 'datetime', ['default' => null, 'null' => true, 'comment' => '最后健康检查时间']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'provider', 'status', 'priority', 'create_time'
        ], true);
    }

    /**
     * 创建查重任务表
     * @return void
     */
    private function _create_check_task()
    {
        $table = $this->table('check_task', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '查重任务表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['title', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '任务标题']],
            ['file_path', 'string', ['limit' => 255, 'null' => true, 'comment' => '文件路径']],
            ['file_name', 'string', ['limit' => 255, 'null' => true, 'comment' => '文件名称']],
            ['file_size', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '文件大小']],
            ['word_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '字数']],
            ['check_api_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '查重接口ID']],
            ['external_task_id', 'string', ['limit' => 100, 'null' => true, 'comment' => '外部任务ID']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '状态']],
            ['similarity_rate', 'decimal', ['precision' => 5, 'scale' => 2, 'null' => true, 'comment' => '相似度']],
            ['report_url', 'string', ['limit' => 255, 'null' => true, 'comment' => '报告URL']],
            ['report_path', 'string', ['limit' => 255, 'null' => true, 'comment' => '报告路径']],
            ['n8n_workflow_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n工作流ID']],
            ['n8n_execution_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n执行ID']],
            ['cost', 'decimal', ['precision' => 8, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '成本']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'user_id', 'check_api_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建查重报告详情表
     * @return void
     */
    private function _create_check_detail()
    {
        $table = $this->table('check_detail', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '查重报告详情表',
        ]);
        PhinxExtend::upgrade($table, [
            ['task_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '任务ID']],
            ['paragraph_index', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '段落索引']],
            ['original_text', 'text', ['null' => false, 'comment' => '原始文本']],
            ['similarity_rate', 'decimal', ['precision' => 5, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '相似度']],
            ['matched_sources', 'text', ['null' => true, 'comment' => '匹配来源']],
            ['suggestions', 'text', ['null' => true, 'comment' => '修改建议']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'task_id', 'similarity_rate', 'create_time'
        ], true);
    }

    /**
     * 创建文档模板表
     * @return void
     */
    private function _create_document_template()
    {
        $table = $this->table('document_template', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '文档模板表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模板名称']],
            ['type', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '模板类型']],
            ['paper_type_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '论文类型ID']],
            ['template_content', 'text', ['null' => false, 'comment' => '模板内容']],
            ['style_config', 'text', ['null' => true, 'comment' => '样式配置']],
            ['is_default', 'integer', ['limit' => 1, 'default' => 0, 'null' => false, 'comment' => '是否默认模板']],
            ['usage_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '使用次数']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'type', 'paper_type_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建文档导出记录表
     * @return void
     */
    private function _create_export_record()
    {
        $table = $this->table('export_record', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '文档导出记录表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['source_type', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '来源类型']],
            ['source_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '来源ID']],
            ['export_format', 'string', ['limit' => 20, 'default' => '', 'null' => false, 'comment' => '导出格式']],
            ['template_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '模板ID']],
            ['file_name', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '文件名']],
            ['file_path', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '文件路径']],
            ['file_size', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '文件大小']],
            ['download_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '下载次数']],
            ['n8n_workflow_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n工作流ID']],
            ['n8n_execution_id', 'string', ['limit' => 100, 'null' => true, 'comment' => 'n8n执行ID']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '状态']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'user_id', 'source_type', 'source_id', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建发票管理表
     * @return void
     */
    private function _create_invoice()
    {
        $table = $this->table('invoice', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '发票管理表',
        ]);
        PhinxExtend::upgrade($table, [
            ['order_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '订单ID']],
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['invoice_type', 'string', ['limit' => 20, 'default' => 'personal', 'null' => false, 'comment' => '发票类型']],
            ['invoice_title', 'string', ['limit' => 200, 'default' => '', 'null' => false, 'comment' => '发票抬头']],
            ['tax_number', 'string', ['limit' => 50, 'null' => true, 'comment' => '税号']],
            ['invoice_content', 'string', ['limit' => 500, 'null' => true, 'comment' => '发票内容']],
            ['invoice_amount', 'decimal', ['precision' => 8, 'scale' => 2, 'default' => 0.00, 'null' => false, 'comment' => '发票金额']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '状态']],
            ['invoice_code', 'string', ['limit' => 50, 'null' => true, 'comment' => '发票代码']],
            ['invoice_number', 'string', ['limit' => 50, 'null' => true, 'comment' => '发票号码']],
            ['invoice_url', 'string', ['limit' => 255, 'null' => true, 'comment' => '发票URL']],
            ['invoice_file_path', 'string', ['limit' => 255, 'null' => true, 'comment' => '发票文件路径']],
            ['apply_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '申请时间']],
            ['issue_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '开具时间']],
            ['remark', 'string', ['limit' => 500, 'null' => true, 'comment' => '备注']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'order_id', 'user_id', 'status', 'apply_time', 'create_time'
        ], true);
    }

    /**
     * 创建消息模板表
     * @return void
     */
    private function _create_message_template()
    {
        $table = $this->table('message_template', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '消息模板表',
        ]);
        PhinxExtend::upgrade($table, [
            ['code', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '模板代码']],
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '模板名称']],
            ['type', 'string', ['limit' => 20, 'default' => 'system', 'null' => false, 'comment' => '模板类型']],
            ['subject', 'string', ['limit' => 255, 'null' => true, 'comment' => '主题(邮件用)']],
            ['content', 'text', ['null' => false, 'comment' => '模板内容']],
            ['variables', 'text', ['null' => true, 'comment' => '变量定义JSON']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'code', 'type', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建用户通知表
     * @return void
     */
    private function _create_user_notification()
    {
        $table = $this->table('user_notification', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '用户通知表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '用户ID']],
            ['type', 'string', ['limit' => 20, 'default' => 'system', 'null' => false, 'comment' => '通知类型']],
            ['title', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '通知标题']],
            ['content', 'text', ['null' => false, 'comment' => '通知内容']],
            ['related_type', 'string', ['limit' => 50, 'null' => true, 'comment' => '关联类型']],
            ['related_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '关联ID']],
            ['is_read', 'integer', ['limit' => 1, 'default' => 0, 'null' => false, 'comment' => '是否已读']],
            ['read_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '阅读时间']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'user_id', 'type', 'is_read', 'create_time'
        ], true);
    }

    /**
     * 创建邮件日志表
     * @return void
     */
    private function _create_email_log()
    {
        $table = $this->table('email_log', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '邮件日志表',
        ]);
        PhinxExtend::upgrade($table, [
            ['user_id', 'integer', ['limit' => 10, 'null' => true, 'comment' => '用户ID']],
            ['to_email', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '收件人邮箱']],
            ['subject', 'string', ['limit' => 255, 'default' => '', 'null' => false, 'comment' => '邮件主题']],
            ['content', 'text', ['null' => false, 'comment' => '邮件内容']],
            ['template_code', 'string', ['limit' => 50, 'null' => true, 'comment' => '模板代码']],
            ['status', 'string', ['limit' => 20, 'default' => 'pending', 'null' => false, 'comment' => '发送状态']],
            ['send_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '发送时间']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'user_id', 'to_email', 'status', 'send_time', 'create_time'
        ], true);
    }

    /**
     * 创建系统配置表
     * @return void
     */
    private function _create_config()
    {
        $table = $this->table('config', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '系统配置表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '配置名称']],
            ['code', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '配置代码']],
            ['value', 'text', ['null' => true, 'comment' => '配置值']],
            ['type', 'string', ['limit' => 20, 'default' => 'text', 'null' => false, 'comment' => '配置类型']],
            ['options', 'text', ['null' => true, 'comment' => '选项配置']],
            ['description', 'string', ['limit' => 500, 'null' => true, 'comment' => '配置描述']],
            ['group_name', 'string', ['limit' => 50, 'default' => 'system', 'null' => false, 'comment' => '分组名称']],
            ['sort', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '排序']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'code', 'group_name', 'status', 'sort', 'create_time'
        ], true);
    }

    /**
     * 创建内容过滤表
     * @return void
     */
    private function _create_content_filter()
    {
        $table = $this->table('content_filter', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => '内容过滤表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '规则名称']],
            ['type', 'string', ['limit' => 20, 'default' => 'keyword', 'null' => false, 'comment' => '过滤类型']],
            ['pattern', 'string', ['limit' => 500, 'default' => '', 'null' => false, 'comment' => '过滤模式']],
            ['action', 'string', ['limit' => 20, 'default' => 'block', 'null' => false, 'comment' => '处理动作']],
            ['severity', 'integer', ['limit' => 2, 'default' => 1, 'null' => false, 'comment' => '严重程度']],
            ['description', 'string', ['limit' => 500, 'null' => true, 'comment' => '规则描述']],
            ['hit_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '命中次数']],
            ['status', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '状态(0禁用1启用)']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'type', 'action', 'status', 'create_time'
        ], true);
    }

    /**
     * 创建n8n工作流配置表
     * @return void
     */
    private function _create_n8n_workflow()
    {
        $table = $this->table('n8n_workflow', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => 'n8n工作流配置表',
        ]);
        PhinxExtend::upgrade($table, [
            ['name', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => '工作流名称']],
            ['workflow_id', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => 'n8n工作流ID']],
            ['type', 'string', ['limit' => 50, 'default' => '', 'null' => false, 'comment' => '工作流类型']],
            ['description', 'text', ['null' => true, 'comment' => '工作流描述']],
            ['webhook_url', 'string', ['limit' => 255, 'null' => true, 'comment' => 'Webhook URL']],
            ['config', 'text', ['null' => true, 'comment' => '配置参数JSON']],
            ['is_active', 'integer', ['limit' => 1, 'default' => 1, 'null' => false, 'comment' => '是否激活']],
            ['execution_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '执行次数']],
            ['success_count', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '成功次数']],
            ['last_execution_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '最后执行时间']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
            ['update_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '更新时间']],
        ], [
            'workflow_id', 'type', 'is_active', 'create_time'
        ], true);
    }

    /**
     * 创建n8n执行记录表
     * @return void
     */
    private function _create_n8n_execution()
    {
        $table = $this->table('n8n_execution', [
            'engine' => 'InnoDB', 'collation' => 'utf8mb4_general_ci', 'comment' => 'n8n执行记录表',
        ]);
        PhinxExtend::upgrade($table, [
            ['workflow_id', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '工作流ID']],
            ['execution_id', 'string', ['limit' => 100, 'default' => '', 'null' => false, 'comment' => 'n8n执行ID']],
            ['trigger_type', 'string', ['limit' => 50, 'default' => 'manual', 'null' => false, 'comment' => '触发类型']],
            ['trigger_data', 'text', ['null' => true, 'comment' => '触发数据JSON']],
            ['status', 'string', ['limit' => 20, 'default' => 'running', 'null' => false, 'comment' => '执行状态']],
            ['start_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '开始时间']],
            ['end_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '结束时间']],
            ['duration', 'integer', ['limit' => 10, 'default' => 0, 'null' => false, 'comment' => '执行时长(秒)']],
            ['result_data', 'text', ['null' => true, 'comment' => '结果数据JSON']],
            ['error_message', 'text', ['null' => true, 'comment' => '错误信息']],
            ['create_time', 'datetime', ['default' => null, 'null' => true, 'comment' => '创建时间']],
        ], [
            'workflow_id', 'execution_id', 'status', 'start_time', 'create_time'
        ], true);
    }

    /**
     * 初始化AI论文写作平台菜单
     * @return void
     * @throws \Exception
     */
    private function _insert_menu()
    {
        // 清理旧的菜单数据，重新构建完整的7模块菜单结构
        // 根据 readme.md 和 menu.md 文档重新编排
        PhinxExtend::write2menu([
            [
                'name' => '写作中心',
                'title' => '写作中心',
                'icon' => 'layui-icon layui-icon-edit',
                'sort' => '200',
                'subs' => [
                    ['name' => '论文类型管理', 'title' => '论文类型管理', 'icon' => 'layui-icon layui-icon-template-1', 'node' => 'admin/paper_type/index'],
                    ['name' => '大纲模板管理', 'title' => '大纲模板管理', 'icon' => 'layui-icon layui-icon-list', 'node' => 'admin/outline_template/index'],
                    ['name' => '提示词模板管理', 'title' => '提示词模板管理', 'icon' => 'layui-icon layui-icon-dialogue', 'node' => 'admin/prompt_template/index'],
                    ['name' => '写作任务管理', 'title' => '写作任务管理', 'icon' => 'layui-icon layui-icon-file', 'node' => 'admin/paper_project/index'],
                    ['name' => '草稿箱管理', 'title' => '草稿箱管理', 'icon' => 'layui-icon layui-icon-edit', 'node' => 'admin/paper_project/draft'],
                ],
            ],
            [
                'name' => '降重与查重',
                'title' => '降重与查重',
                'icon' => 'layui-icon layui-icon-refresh-3',
                'sort' => '210',
                'subs' => [
                    ['name' => '降重任务管理', 'title' => '降重任务管理', 'icon' => 'layui-icon layui-icon-refresh-3', 'node' => 'admin/rewrite_task/index'],
                    ['name' => '降重结果查看', 'title' => '降重结果查看', 'icon' => 'layui-icon layui-icon-ok-circle', 'node' => 'admin/rewrite_result/index'],
                    ['name' => '查重任务管理', 'title' => '查重任务管理', 'icon' => 'layui-icon layui-icon-search', 'node' => 'admin/check_task/index'],
                    ['name' => '查重接口配置', 'title' => '查重接口配置', 'icon' => 'layui-icon layui-icon-set', 'node' => 'admin/check_api/index'],
                ],
            ],
            [
                'name' => '文档导出',
                'title' => '文档导出',
                'icon' => 'layui-icon layui-icon-download-circle',
                'sort' => '220',
                'subs' => [
                    ['name' => '导出样式模板', 'title' => '导出样式模板', 'icon' => 'layui-icon layui-icon-template', 'node' => 'admin/document_template/index'],
                    ['name' => '下载记录管理', 'title' => '下载记录管理', 'icon' => 'layui-icon layui-icon-download-circle', 'node' => 'admin/export_record/index'],
                ],
            ],
            [
                'name' => '用户中心',
                'title' => '用户中心',
                'icon' => 'layui-icon layui-icon-user',
                'sort' => '230',
                'subs' => [
                    ['name' => '用户列表', 'title' => '用户列表', 'icon' => 'layui-icon layui-icon-username', 'node' => 'admin/user/index'],
                    ['name' => 'VIP套餐管理', 'title' => 'VIP套餐管理', 'icon' => 'layui-icon layui-icon-diamond', 'node' => 'admin/vip_package/index'],
                    ['name' => '用户积分管理', 'title' => '用户积分管理', 'icon' => 'layui-icon layui-icon-star', 'node' => 'admin/user_credit/index'],
                ],
            ],
            [
                'name' => '收费系统',
                'title' => '收费系统',
                'icon' => 'layui-icon layui-icon-diamond',
                'sort' => '240',
                'subs' => [
                    ['name' => '订单管理', 'title' => '订单管理', 'icon' => 'layui-icon layui-icon-form', 'node' => 'admin/order/index'],
                    ['name' => '套餐配置', 'title' => '套餐配置', 'icon' => 'layui-icon layui-icon-set', 'node' => 'admin/package_config/index'],
                    ['name' => '充值记录', 'title' => '充值记录', 'icon' => 'layui-icon layui-icon-rmb', 'node' => 'admin/recharge_record/index'],
                    ['name' => '发票管理', 'title' => '发票管理', 'icon' => 'layui-icon layui-icon-file-b', 'node' => 'admin/invoice/index'],
                ],
            ],
            [
                'name' => '通知与消息',
                'title' => '通知与消息',
                'icon' => 'layui-icon layui-icon-notice',
                'sort' => '250',
                'subs' => [
                    ['name' => '系统通知记录', 'title' => '系统通知记录', 'icon' => 'layui-icon layui-icon-notice', 'node' => 'admin/system_notice/index'],
                    ['name' => '消息模板管理', 'title' => '消息模板管理', 'icon' => 'layui-icon layui-icon-template-1', 'node' => 'admin/message_template/index'],
                    ['name' => '邮件配置', 'title' => '邮件配置', 'icon' => 'layui-icon layui-icon-email', 'node' => 'admin/email_config/index'],
                    ['name' => '通知记录', 'title' => '通知记录', 'icon' => 'layui-icon layui-icon-log', 'node' => 'admin/notification_log/index'],
                ],
            ],
            [
                'name' => '系统设置',
                'title' => '系统设置',
                'icon' => 'layui-icon layui-icon-set',
                'sort' => '260',
                'subs' => [
                    ['name' => 'AI模型配置', 'title' => 'AI模型配置', 'icon' => 'layui-icon layui-icon-engine', 'node' => 'admin/ai_model/index'],
                    ['name' => '接口密钥管理', 'title' => '接口密钥管理', 'icon' => 'layui-icon layui-icon-key', 'node' => 'admin/api_key/index'],
                    ['name' => 'Webhook配置', 'title' => 'Webhook配置', 'icon' => 'layui-icon layui-icon-link', 'node' => 'admin/webhook_config/index'],
                    ['name' => '内容风控规则', 'title' => '内容风控规则', 'icon' => 'layui-icon layui-icon-filter', 'node' => 'admin/content_filter/index'],
                    ['name' => 'n8n工作流管理', 'title' => 'n8n工作流管理', 'icon' => 'layui-icon layui-icon-engine', 'node' => 'admin/n8n_workflow/index'],
                ],
            ],
        ], [
            'url|node' => 'admin/paper_project/index'
        ]);
    }
}
