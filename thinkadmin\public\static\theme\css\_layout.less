@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

.layui-layout {
  > .layui-header {
    left: @LayoutLeftDefaSize;
    right: 0;
    color: @TopHeaderTextColor !important;
    height: @LayoutHeadTopHeight !important;
    z-index: 222;
    position: absolute;
    box-sizing: content-box;
    background: @TopHeaderBackColor !important;
    #notSelect();

    > ul.layui-nav {
      margin: 0;
      padding: 0;
      white-space: nowrap;

      .layui-logo-hide {
        width: 50px !important;
        padding: 0 !important;
        display: none;
        text-align: center;

        .headimg {
          margin: 0;
        }
      }

      &.layui-layout-left {
        left: 0;
      }

      &.layui-layout-right {
        .headimg {
          width: 20px;
          height: 20px;
        }
      }

      .layui-nav-item > a {
        height: @LayoutHeadTopHeight;
        padding: 0 18px;
        overflow: hidden;
        line-height: @LayoutHeadTopHeight;

        &:hover {
          color: @TopHeaderNavHoverTextColor
        }

        .layui-icon {
          font-size: 16px;
        }
      }
    }

    .layui-nav-item {
      height: @LayoutHeadTopHeight;
      line-height: @LayoutHeadTopHeight;

      > a {
        color: @TopHeaderNavNormalTextColor !important;

        &:hover {
          color: @TopHeaderNavHoverTextColor !important;
          background: @TopHeaderNavHoverBackColor !important;
        }
      }

      &.layui-this > a {
        color: @TopHeaderNavActiveTextColor !important;
        background: @TopHeaderNavActiveBackColor !important;
      }

      .layui-nav-child {
        top: @LayoutHeadTopHeight;
        border: 0;
        padding: 0;
        line-height: 48px;
        border-radius: 0;

        &.layui-show + a.layui-elip {
          background: @TopHeaderNavHoverBackColor !important;
        }

        + a.layui-elip {

          img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            border-radius: 50%;
          }
        }

        &.layui-show + a {
          background: #FFF;
        }

        dd {
          margin: 0;

          a {
            padding: 0;
            text-align: center;

            &:hover {
              background: @TopHeaderNavHoverBackColor !important;
            }

            .layui-icon {
              margin-right: 5px;
            }
          }
        }
      }
    }

    .layui-nav-bar,
    .layui-nav-item:after {
      display: none !important
    }
  }

  > .layui-side {
    top: 0;
    bottom: 0;
    width: @LayoutLeftDefaSize;
    z-index: 333;
    position: fixed;
    overflow: hidden;
    box-shadow: @ShadowBodyLeft;
    background-color: @LeftMainBackColor;
    #notSelect();

    .layui-logo {
      color: #FFF;
      width: auto;
      height: @LayoutHeadTopHeight;
      display: block;
      overflow: hidden;
      position: relative;
      text-align: center;
      box-shadow: none !important;
      line-height: @LayoutHeadTopHeight;
      border-bottom: @BoxBottomLine;

      .headimg {
        margin: 0 10px 0 0;
      }

      .headtxt {
        font-size: 18px;
        font-weight: bold;

        sup {
          font-size: 9px;
          line-height: 9px;
          padding-left: 5px;
        }
      }
    }

    .layui-side-target {
      &:before {
        color: #FFF;
        width: 100%;
        height: 100%;
        display: block;
        content: "\e602";
        text-align: center;
        font-weight: 700;
        line-height: 30px;
        transform: rotate(180deg);
        #iconLayout(14px);
      }

      top: 50%;
      color: #fff;
      right: -2px;
      width: 15px;
      height: 30px;
      z-index: 334;
      cursor: pointer;
      display: none;
      position: absolute;
      margin-top: -15px;
      background: rgba(0, 0, 0, 0.4);
      border-top-left-radius: 100%;
      border-bottom-left-radius: 100%;
    }

    .layui-nav-bar {
      display: none !important;
    }

    .layui-side-scroll {
      top: @LayoutHeadTopHeight;
      bottom: 0;
      width: 100% !important;
      height: 100% !important;
      overflow: auto;
      position: absolute;

      .layui-side-icon {
        display: none;
      }

      .layui-side-tree {
        #defaScrollbar();
      }

      .layui-side-icon:after,
      .layui-side-tree:after {
        height: @LayoutHeadTopHeight;
        content: '';
        display: block;
      }

      .layui-nav-tree {
        width: @LayoutLeftDefaSize;
        background: none !important;

        > li.layui-this {
          background: @LeftMainNavActiveBackColor;

          > a {
            background: none !important;
          }
        }
      }

      .layui-nav-item {

        > a:hover {
          background-color: rgba(100, 100, 100, 0.1) !important;
        }

        .layui-nav-child {
          padding: 0;
        }

        dd, dd > a {
          background-color: @LeftMainNavNormalBackColor;
        }

        dd.layui-this {
          background: none !important;

          > a {
            color: @LeftMainNavActiveTextColor;
            background-color: @LeftMainNavActiveBackColor;
          }
        }

        a {
          height: @LayoutLeftNavHeight;
          display: block;
          line-height: @LayoutLeftNavHeight;
          padding-top: 0;
          padding-bottom: 0;
          color: @LeftMainNavNormalTextColor;

          &:hover {
            color: @LeftMainNavActiveTextColor;
          }

          .nav-icon {
            padding-right: 5px;
          }

          .layui-nav-more {
            padding: 0;
          }
        }
      }
    }
  }

  > .layui-body {
    top: @LayoutHeadTopHeight;
    left: @LayoutLeftDefaSize;
    padding: 0;
    z-index: 111;
    background: @BodyMainBackColor;

    > .think-page-loader {
      left: @LayoutLeftDefaSize
    }

    > .think-page-body > .layui-card {
      box-shadow: none !important;
      background: none !important;

      > .layui-card-body {
        top: 0;
        width: 100%;
        bottom: 0;
        padding: 0;
        z-index: 2;
        overflow: auto;
        position: absolute;
        box-sizing: border-box;

        > .layui-card-html {
          padding: 15px;
          min-width: 100%;
          position: absolute;
          box-sizing: border-box;
          #bodyLayout();
        }

        > .layui-card-table {
          padding: 15px;
          box-sizing: border-box;
          #bodyLayout();
        }

        > div > .layui-tab.layui-tab-card {
          border: none;
          box-shadow: @ShadowOuterMax;

          & > .layui-tab-content.think-box-shadow {
            box-shadow: none;
          }
        }
      }

      > .layui-card-line {
        top: 0;
        left: 0;
        right: 0;
        z-index: 3;
        position: absolute;
        box-shadow: @ShadowBodyTop;
      }

      > .layui-card-header {
        top: @LayoutHeadTopHeight;
        left: @LayoutLeftDefaSize;
        box-sizing: border-box;
        border-top: @BoxBottomLine;
        border-bottom: 1px solid #fff !important;
        #notSelect();

        right: 0;
        height: @LayoutBodyHeadHeight;
        z-index: 4;
        padding: 0 15px;
        position: fixed;
        background: #FFF;
        line-height: @LayoutBodyHeadHeight;

        > div {
          margin-top: -1px
        }

        & + .layui-card-line {
          top: @LayoutBodyHeadHeight;

          & + .layui-card-body {
            top: @LayoutBodyHeadHeight;
          }
        }
      }
    }
  }
}

.layui-layout-left-mini {
  .layui-header {
    left: @LayoutLeftMiniSize !important;
  }

  > .layui-body {
    left: @LayoutLeftMiniSize !important;

    > .think-page-loader {
      left: @LayoutLeftMiniSize !important;
    }

    > .think-page-body > .layui-card > .layui-card-header {
      left: @LayoutLeftMiniSize !important;
    }
  }

  > .layui-side {

    .layui-logo {
      .headimg {
        margin: 0;
        display: inline-block;
      }

      .headtxt {
        display: none;
      }
    }

    .layui-nav-more {
      display: none !important
    }

    .layui-side-target:before {
      transform: rotate(0deg);
    }

    .layui-nav-item {
      a {
        padding: 0
      }

      .nav-text {
        display: none
      }

      .nav-icon {
        padding: 0 !important;
        font-size: 16px !important;
        display: inline-block !important;
        #defaTransition();
      }

      .layui-nav-child {
        padding: 0;
        display: block !important;
        background-color: rgba(0, 0, 0, .3) !important
      }
    }

    &,
    .layui-nav-tree,
    .layui-side-scroll,
    .layui-side-scroll .layui-nav-tree {
      width: @LayoutLeftMiniSize;
      text-align: center;
    }
  }

  [data-target-menu-type] i {
    display: inline-block;
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
  }
}

.layui-layout-left-hide {
  > .layui-header {
    left: 0 !important;

    .layui-logo-hide {
      display: inline-block !important;
    }
  }

  > .layui-side {
    display: none !important;
  }

  > .layui-body {
    left: 0 !important;

    > .think-page-loader {
      left: 0 !important;
    }

    > .think-page-body > .layui-card > .layui-card-header {
      left: 0 !important;
    }
  }

  [data-target-menu-type] {
    display: none !important;
  }
}