<?php

// +----------------------------------------------------------------------
// | Rewrite Task Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 降重任务模型
 * @class RewriteTask
 * @package app\admin\model
 */
class RewriteTask extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'rewrite_task';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'ai_model_id' => 'integer',
        'original_word_count' => 'integer',
        'rewritten_word_count' => 'integer',
        'progress' => 'integer',
        'similarity_before' => 'float',
        'similarity_after' => 'float'
    ];

    /**
     * 关联AI模型
     * @return \think\model\relation\BelongsTo
     */
    public function aiModel()
    {
        return $this->belongsTo(AiModel::class, 'ai_model_id', 'id');
    }

    /**
     * 关联降重结果
     * @return \think\model\relation\HasMany
     */
    public function rewriteResults()
    {
        return $this->hasMany(RewriteResult::class, 'task_id', 'id');
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取模式文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getRewriteModeTextAttr($value, $data)
    {
        $modeMap = [
            'standard' => '标准模式',
            'conservative' => '保守模式',
            'aggressive' => '激进模式',
            'academic' => '学术模式'
        ];
        return $modeMap[$data['rewrite_mode']] ?? $data['rewrite_mode'];
    }

    /**
     * 获取进度百分比
     * @param $value
     * @param $data
     * @return string
     */
    public function getProgressPercentAttr($value, $data)
    {
        return $data['progress'] . '%';
    }

    /**
     * 搜索器 - 标题
     * @param $query
     * @param $value
     */
    public function searchTitleAttr($query, $value)
    {
        $query->whereLike('title', "%{$value}%");
    }

    /**
     * 搜索器 - 降重模式
     * @param $query
     * @param $value
     */
    public function searchRewriteModeAttr($query, $value)
    {
        $query->where('rewrite_mode', $value);
    }

    /**
     * 搜索器 - 用户ID
     * @param $query
     * @param $value
     */
    public function searchUserIdAttr($query, $value)
    {
        $query->where('user_id', $value);
    }

    /**
     * 搜索器 - AI模型ID
     * @param $query
     * @param $value
     */
    public function searchAiModelIdAttr($query, $value)
    {
        $query->where('ai_model_id', $value);
    }

    /**
     * 搜索器 - 状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }
}
