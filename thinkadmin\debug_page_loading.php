<?php

/**
 * 调试页面加载失败问题
 */

echo "=== 调试页面加载失败问题 ===\n\n";

// 1. 检查错误日志
echo "1. 检查错误日志:\n";
$logPaths = [
    'runtime/log',
    'runtime/admin/log',
    'runtime/error.log',
    'runtime/admin/error.log'
];

foreach ($logPaths as $logPath) {
    if (is_dir($logPath)) {
        $files = glob($logPath . '/*.log');
        if (!empty($files)) {
            $latestFile = max($files);
            echo "  📄 最新日志文件: {$latestFile}\n";
            
            if (file_exists($latestFile)) {
                $content = file_get_contents($latestFile);
                $lines = explode("\n", $content);
                $recentLines = array_slice($lines, -20); // 最后20行
                
                echo "  📝 最近的日志内容:\n";
                foreach ($recentLines as $line) {
                    if (!empty(trim($line))) {
                        echo "    " . trim($line) . "\n";
                    }
                }
                echo "\n";
            }
        }
    } elseif (file_exists($logPath)) {
        echo "  📄 日志文件: {$logPath}\n";
        $content = file_get_contents($logPath);
        $lines = explode("\n", $content);
        $recentLines = array_slice($lines, -10);
        
        echo "  📝 最近的日志内容:\n";
        foreach ($recentLines as $line) {
            if (!empty(trim($line))) {
                echo "    " . trim($line) . "\n";
            }
        }
        echo "\n";
    }
}

// 2. 检查控制器文件
echo "2. 检查控制器文件状态:\n";
$controllers = [
    'ContentTemplate' => 'app/admin/controller/ContentTemplate.php',
    'PromptTemplate' => 'app/admin/controller/PromptTemplate.php',
    'OutlineTemplate' => 'app/admin/controller/OutlineTemplate.php',
    'DraftBox' => 'app/admin/controller/DraftBox.php',
    'PaperType' => 'app/admin/controller/PaperType.php'
];

foreach ($controllers as $name => $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "  ✅ {$name}: {$size} bytes, 修改时间: {$modified}\n";
        
        // 检查语法
        $output = [];
        $returnCode = 0;
        exec("php -l {$file} 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0) {
            echo "    ❌ 语法错误: " . implode(' ', $output) . "\n";
        }
    } else {
        echo "  ❌ {$name}: 文件不存在\n";
    }
}

// 3. 检查模板文件
echo "\n3. 检查模板文件:\n";
$templates = [
    'content_template/index.html' => 'app/admin/view/content_template/index.html',
    'prompt_template/index.html' => 'app/admin/view/prompt_template/index.html',
    'outline_template/index.html' => 'app/admin/view/outline_template/index.html',
    'draft_box/index.html' => 'app/admin/view/draft_box/index.html',
    'paper_type/index.html' => 'app/admin/view/paper_type/index.html'
];

foreach ($templates as $name => $file) {
    if (file_exists($file)) {
        echo "  ✅ {$name}: 存在\n";
    } else {
        echo "  ❌ {$name}: 不存在\n";
    }
}

// 4. 检查模型文件
echo "\n4. 检查模型文件:\n";
$models = [
    'DocumentTemplate' => 'app/admin/model/DocumentTemplate.php',
    'PromptTemplate' => 'app/admin/model/PromptTemplate.php',
    'OutlineTemplate' => 'app/admin/model/OutlineTemplate.php',
    'PaperProject' => 'app/admin/model/PaperProject.php',
    'PaperType' => 'app/admin/model/PaperType.php'
];

foreach ($models as $name => $file) {
    if (file_exists($file)) {
        echo "  ✅ {$name}: 存在\n";
        
        // 检查语法
        $output = [];
        $returnCode = 0;
        exec("php -l {$file} 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0) {
            echo "    ❌ 语法错误: " . implode(' ', $output) . "\n";
        }
    } else {
        echo "  ❌ {$name}: 不存在\n";
    }
}

// 5. 测试数据库连接
echo "\n5. 测试数据库连接:\n";
try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "  ✅ 数据库连接成功\n";
        
        // 测试查询
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_template WHERE type = 'content'");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 正文模板数据: {$count} 条\n";
        
    } else {
        echo "  ❌ 数据库文件不存在\n";
    }
} catch (Exception $e) {
    echo "  ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 6. 检查权限和路由
echo "\n6. 检查菜单和路由配置:\n";
try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT title, node, url FROM system_menu WHERE node LIKE '%template%' OR node LIKE '%draft%' OR node LIKE '%paper_type%'");
        $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($menus)) {
            echo "  📋 相关菜单项:\n";
            foreach ($menus as $menu) {
                echo "    - {$menu['title']}: {$menu['node']} -> {$menu['url']}\n";
            }
        } else {
            echo "  ⚠️  没有找到相关菜单项\n";
        }
    }
} catch (Exception $e) {
    echo "  ❌ 菜单检查失败: " . $e->getMessage() . "\n";
}

// 7. 模拟HTTP请求测试
echo "\n7. 模拟控制器调用测试:\n";

// 创建简单的测试脚本
$testScript = '<?php
// 设置基本环境
define("ROOT_PATH", __DIR__ . "/");
require __DIR__ . "/vendor/autoload.php";

try {
    // 测试ContentTemplate控制器
    echo "测试ContentTemplate控制器...\n";
    
    // 检查类是否可以加载
    if (class_exists("app\\admin\\controller\\ContentTemplate")) {
        echo "✅ ContentTemplate类加载成功\n";
    } else {
        echo "❌ ContentTemplate类加载失败\n";
    }
    
    // 检查模型类
    if (class_exists("app\\admin\\model\\DocumentTemplate")) {
        echo "✅ DocumentTemplate模型加载成功\n";
    } else {
        echo "❌ DocumentTemplate模型加载失败\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "❌ 致命错误: " . $e->getMessage() . "\n";
}
';

file_put_contents('temp_controller_test.php', $testScript);

$output = [];
$returnCode = 0;
exec("php temp_controller_test.php 2>&1", $output, $returnCode);

echo "  " . implode("\n  ", $output) . "\n";

// 清理临时文件
if (file_exists('temp_controller_test.php')) {
    unlink('temp_controller_test.php');
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "🔍 诊断建议:\n";
echo "1. 检查上述错误日志中的具体错误信息\n";
echo "2. 确认所有控制器和模型文件都存在且语法正确\n";
echo "3. 检查数据库连接是否正常\n";
echo "4. 确认菜单配置是否正确\n";
echo "5. 清除runtime目录下的缓存文件\n";
echo "6. 检查Web服务器的错误日志\n";

echo "\n=== 调试完成 ===\n";
