<?php

/**
 * 检查模板语法错误
 */

echo "=== 模板语法检查 ===\n\n";

// 检查所有视图文件
$viewDir = __DIR__ . '/app/admin/view';
$errorFiles = [];
$checkedFiles = 0;

function checkDirectory($dir, &$errorFiles, &$checkedFiles) {
    $files = glob($dir . '/*.html');
    foreach ($files as $file) {
        $checkedFiles++;
        $content = file_get_contents($file);
        
        // 检查常见的语法错误
        $errors = [];
        
        // 检查 |default== 语法错误
        if (preg_match('/\|default==/', $content)) {
            $errors[] = "包含错误的 |default== 语法";
        }

        // 检查 |default='' 语法错误
        if (preg_match('/\|default=\'\'/', $content)) {
            $errors[] = "包含错误的 |default='' 语法，应使用 ?? '' 替代";
        }
        
        // 检查未闭合的标签
        if (preg_match('/\{[^}]*$/', $content)) {
            $errors[] = "可能包含未闭合的模板标签";
        }
        
        // 检查PHP语法错误（简单检查）
        if (preg_match('/\$[a-zA-Z_][a-zA-Z0-9_]*\|default==/', $content)) {
            $errors[] = "包含PHP变量的错误default语法";
        }
        
        if (!empty($errors)) {
            $errorFiles[str_replace(__DIR__ . '/', '', $file)] = $errors;
        }
    }
    
    // 递归检查子目录
    $dirs = glob($dir . '/*', GLOB_ONLYDIR);
    foreach ($dirs as $subDir) {
        checkDirectory($subDir, $errorFiles, $checkedFiles);
    }
}

checkDirectory($viewDir, $errorFiles, $checkedFiles);

echo "检查了 {$checkedFiles} 个模板文件\n\n";

if (empty($errorFiles)) {
    echo "✅ 所有模板文件语法正确！\n";
} else {
    echo "❌ 发现以下文件有语法错误：\n\n";
    foreach ($errorFiles as $file => $errors) {
        echo "文件: {$file}\n";
        foreach ($errors as $error) {
            echo "  - {$error}\n";
        }
        echo "\n";
    }
}

// 检查是否有编译缓存错误
$tempDir = __DIR__ . '/runtime/temp';
if (is_dir($tempDir)) {
    $tempFiles = glob($tempDir . '/*.php');
    echo "\n模板缓存文件: " . count($tempFiles) . " 个\n";
    
    if (count($tempFiles) > 0) {
        echo "建议清理模板缓存以确保使用最新模板\n";
        echo "执行命令: Remove-Item -Recurse -Force runtime\\temp\n";
    }
} else {
    echo "\n✅ 模板缓存已清理\n";
}

echo "\n=== 检查完成 ===\n";
