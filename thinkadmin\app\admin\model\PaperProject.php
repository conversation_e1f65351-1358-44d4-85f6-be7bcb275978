<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 论文项目模型
 * @class PaperProject
 * @package app\admin\model
 */
class PaperProject extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'paper_project';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'paper_type_id' => 'integer',
        'target_word_count' => 'integer',
        'outline_version' => 'integer',
        'current_word_count' => 'integer',
        'progress' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $statusMap = [
            'draft' => '草稿',
            'outline' => '大纲生成中',
            'writing' => '写作中',
            'completed' => '已完成',
            'failed' => '失败'
        ];
        return $statusMap[$data['status'] ?? 'draft'] ?? '未知';
    }

    /**
     * 获取进度文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getProgressTextAttr($value, array $data): string
    {
        $progress = $data['progress'] ?? 0;
        return $progress . '%';
    }

    /**
     * 获取字数完成度
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getWordCountProgressAttr($value, array $data): string
    {
        $current = $data['current_word_count'] ?? 0;
        $target = $data['target_word_count'] ?? 1;
        $percent = round(($current / $target) * 100, 1);
        return "{$current}/{$target} ({$percent}%)";
    }

    /**
     * 关联论文类型
     * @return \think\model\relation\BelongsTo
     */
    public function paperType()
    {
        return $this->belongsTo(PaperType::class, 'paper_type_id', 'id');
    }

    /**
     * 关联论文章节
     * @return \think\model\relation\HasMany
     */
    public function sections()
    {
        return $this->hasMany(PaperSection::class, 'project_id', 'id');
    }

    /**
     * 关联AI使用记录
     * @return \think\model\relation\HasMany
     */
    public function aiUsageLogs()
    {
        return $this->hasMany(AiUsageLog::class, 'task_id', 'id')->where('task_type', 'paper_writing');
    }

    /**
     * 获取用户的项目列表
     * @param int $userId
     * @param string $status
     * @return \think\Collection
     */
    public static function getUserProjects(int $userId, string $status = ''): \think\Collection
    {
        $query = static::mk()->where(['user_id' => $userId]);
        if (!empty($status)) {
            $query->where(['status' => $status]);
        }
        return $query->order('id desc')->select();
    }

    /**
     * 获取项目统计信息
     * @param int $projectId
     * @return array
     */
    public static function getProjectStats(int $projectId): array
    {
        $project = static::mk()->findOrEmpty($projectId);
        if ($project->isEmpty()) {
            return [];
        }

        $sections = PaperSection::mk()->where(['project_id' => $projectId])->select();
        $totalSections = $sections->count();
        $completedSections = $sections->where('status', 'completed')->count();
        $totalWords = $sections->sum('word_count');

        return [
            'total_sections' => $totalSections,
            'completed_sections' => $completedSections,
            'section_progress' => $totalSections > 0 ? round(($completedSections / $totalSections) * 100, 1) : 0,
            'total_words' => $totalWords,
            'target_words' => $project['target_word_count'],
            'word_progress' => $project['target_word_count'] > 0 ? round(($totalWords / $project['target_word_count']) * 100, 1) : 0,
        ];
    }

    /**
     * 更新项目进度
     * @param int $projectId
     * @return bool
     */
    public static function updateProgress(int $projectId): bool
    {
        $stats = static::getProjectStats($projectId);
        if (empty($stats)) {
            return false;
        }

        $progress = max($stats['section_progress'], $stats['word_progress']);
        $currentWordCount = $stats['total_words'];

        return static::mk()->where(['id' => $projectId])->update([
            'progress' => $progress,
            'current_word_count' => $currentWordCount,
            'update_time' => date('Y-m-d H:i:s')
        ]) > 0;
    }
}
