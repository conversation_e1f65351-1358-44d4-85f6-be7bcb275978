<?php

// +----------------------------------------------------------------------
// | Rewrite Result Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 降重结果模型
 * @class RewriteResult
 * @package app\admin\model
 */
class RewriteResult extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'rewrite_result';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'task_id' => 'integer',
        'version' => 'integer',
        'word_count' => 'integer',
        'similarity_score' => 'float',
        'is_selected' => 'integer'
    ];

    /**
     * 关联降重任务
     * @return \think\model\relation\BelongsTo
     */
    public function rewriteTask()
    {
        return $this->belongsTo(RewriteTask::class, 'task_id', 'id');
    }

    /**
     * 获取是否选中文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsSelectedTextAttr($value, $data)
    {
        return $data['is_selected'] ? '是' : '否';
    }

    /**
     * 获取相似度百分比
     * @param $value
     * @param $data
     * @return string
     */
    public function getSimilarityPercentAttr($value, $data)
    {
        return round($data['similarity_score'], 2) . '%';
    }

    /**
     * 搜索器 - 任务ID
     * @param $query
     * @param $value
     */
    public function searchTaskIdAttr($query, $value)
    {
        $query->where('task_id', $value);
    }

    /**
     * 搜索器 - 是否选中
     * @param $query
     * @param $value
     */
    public function searchIsSelectedAttr($query, $value)
    {
        $query->where('is_selected', $value);
    }
}
