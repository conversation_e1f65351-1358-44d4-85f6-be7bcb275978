<?php

// +----------------------------------------------------------------------
// | Invoice Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\Invoice as InvoiceModel;

/**
 * 发票管理
 * @class Invoice
 * @package app\admin\controller
 */
class Invoice extends Controller
{
    /**
     * 发票管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        InvoiceModel::mQuery()->layTable(function () {
            $this->title = '发票管理';
        }, static function (QueryHelper $query) {
            $query->like('invoice_title,tax_number,invoice_code,invoice_number')
                  ->equal('user_id,order_id,invoice_type,status');
            $query->dateBetween('apply_time,issue_time,create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加发票
     * @auth true
     */
    public function add()
    {
        InvoiceModel::mForm('invoice/form');
    }

    /**
     * 编辑发票
     * @auth true
     */
    public function edit()
    {
        InvoiceModel::mForm('invoice/form');
    }

    /**
     * 查看发票详情
     * @auth true
     */
    public function view()
    {
        InvoiceModel::mForm('invoice/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 发票类型选项
            $this->typeOptions = [
                'personal' => '个人发票',
                'company' => '企业发票'
            ];
            
            // 发票状态选项
            $this->statusOptions = [
                'pending' => '待处理',
                'processing' => '开具中',
                'issued' => '已开具',
                'failed' => '开具失败',
                'cancelled' => '已取消'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['status'] = 'pending';
                $data['apply_time'] = date('Y-m-d H:i:s');
            }
            $data['update_time'] = date('Y-m-d H:i:s');
            
            // 企业发票必须填写税号
            if ($data['invoice_type'] === 'company' && empty($data['tax_number'])) {
                $this->error('企业发票必须填写税号！');
            }
        }
    }

    /**
     * 修改发票状态
     * @auth true
     */
    public function state()
    {
        InvoiceModel::mSave($this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除发票
     * @auth true
     */
    public function remove()
    {
        InvoiceModel::mDelete();
    }

    /**
     * 开具发票
     * @auth true
     */
    public function issue()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('发票ID不能为空！');
        }
        
        $invoice = InvoiceModel::mk()->findOrEmpty($id);
        if ($invoice->isEmpty()) {
            $this->error('发票不存在！');
        }
        
        if ($invoice['status'] !== 'pending') {
            $this->error('发票状态不正确！');
        }
        
        // 这里可以调用第三方发票接口开具发票
        // 暂时模拟开具成功
        $invoice->save([
            'status' => 'processing',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('发票开具任务已启动！');
    }

    /**
     * 完成发票开具
     * @auth true
     */
    public function complete()
    {
        $data = $this->_vali([
            'id.require' => '发票ID不能为空！',
            'invoice_code.require' => '发票代码不能为空！',
            'invoice_number.require' => '发票号码不能为空！',
        ]);
        
        $invoice = InvoiceModel::mk()->findOrEmpty($data['id']);
        if ($invoice->isEmpty()) {
            $this->error('发票不存在！');
        }
        
        if ($invoice['status'] !== 'processing') {
            $this->error('发票状态不正确！');
        }
        
        $updateData = [
            'status' => 'issued',
            'invoice_code' => $data['invoice_code'],
            'invoice_number' => $data['invoice_number'],
            'issue_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        // 如果有发票URL或文件路径
        if (!empty($data['invoice_url'])) {
            $updateData['invoice_url'] = $data['invoice_url'];
        }
        if (!empty($data['invoice_file_path'])) {
            $updateData['invoice_file_path'] = $data['invoice_file_path'];
        }
        
        $invoice->save($updateData);
        $this->success('发票开具完成！');
    }

    /**
     * 发票开具失败
     * @auth true
     */
    public function fail()
    {
        $data = $this->_vali([
            'id.require' => '发票ID不能为空！',
            'remark' => '失败原因',
        ]);
        
        $invoice = InvoiceModel::mk()->findOrEmpty($data['id']);
        if ($invoice->isEmpty()) {
            $this->error('发票不存在！');
        }
        
        if ($invoice['status'] !== 'processing') {
            $this->error('发票状态不正确！');
        }
        
        $invoice->save([
            'status' => 'failed',
            'remark' => $data['remark'] ?? '开具失败',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('发票状态已更新为失败！');
    }

    /**
     * 取消发票
     * @auth true
     */
    public function cancel()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('发票ID不能为空！');
        }
        
        $invoice = InvoiceModel::mk()->findOrEmpty($id);
        if ($invoice->isEmpty()) {
            $this->error('发票不存在！');
        }
        
        if (!in_array($invoice['status'], ['pending', 'processing'])) {
            $this->error('发票状态不允许取消！');
        }
        
        $invoice->save([
            'status' => 'cancelled',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('发票已取消！');
    }

    /**
     * 下载发票
     * @auth true
     */
    public function download()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('发票ID不能为空！');
        }
        
        $invoice = InvoiceModel::mk()->findOrEmpty($id);
        if ($invoice->isEmpty()) {
            $this->error('发票不存在！');
        }
        
        if ($invoice['status'] !== 'issued') {
            $this->error('发票尚未开具！');
        }
        
        if (empty($invoice['invoice_file_path']) && empty($invoice['invoice_url'])) {
            $this->error('发票文件不存在！');
        }
        
        // 如果有本地文件路径，直接下载
        if (!empty($invoice['invoice_file_path']) && file_exists($invoice['invoice_file_path'])) {
            return download($invoice['invoice_file_path'], 
                           "发票_{$invoice['invoice_number']}.pdf");
        }
        
        // 如果有URL，重定向到URL
        if (!empty($invoice['invoice_url'])) {
            $this->redirect($invoice['invoice_url']);
        }
        
        $this->error('发票文件不可用！');
    }
}
