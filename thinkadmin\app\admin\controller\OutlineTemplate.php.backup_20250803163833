<?php

namespace app\admin\controller;

use app\admin\model\OutlineTemplateModel;
use think\admin\Controller;

/**
 * 大纲模板管理
 * @class OutlineTemplate
 * @package app\admin\controller
 */
class OutlineTemplate extends Controller
{
    /**
     * 大纲模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        OutlineTemplateModel::mQuery($this)->layPage(function () {
            $this->title = '大纲模板管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加大纲模板管理
     * @auth true
     */
    public function add()
    {
        OutlineTemplateModel::mForm('outline_template/form');
    }

    /**
     * 编辑大纲模板管理
     * @auth true
     */
    public function edit()
    {
        OutlineTemplateModel::mForm('outline_template/form');
    }

    /**
     * 删除大纲模板管理
     * @auth true
     */
    public function remove()
    {
        OutlineTemplateModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        OutlineTemplateModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}