<?php
// Automatically Generated At: 2025-08-02 08:30:35
declare(strict_types=1);
return array (
  'psr/container' => array (
    'type' => 'library',
    'name' => 'psr/container',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => '2.0.2',
    'homepage' => 'https://github.com/php-fig/container',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Common Container Interface (PHP FIG PSR-11)',
  ),
  'psr/http-message' => array (
    'type' => 'library',
    'name' => 'psr/http-message',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => '1.1',
    'homepage' => 'https://github.com/php-fig/http-message',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Common interface for HTTP messages',
  ),
  'psr/log' => array (
    'type' => 'library',
    'name' => 'psr/log',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => '3.0.2',
    'homepage' => 'https://github.com/php-fig/log',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Common interface for logging libraries',
  ),
  'psr/simple-cache' => array (
    'type' => 'library',
    'name' => 'psr/simple-cache',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => '3.0.0',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Common interfaces for simple caching',
  ),
  'symfony/process' => array (
    'type' => 'library',
    'name' => 'symfony/process',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => 'v7.3.0',
    'homepage' => 'https://symfony.com',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Executes commands in sub-processes',
  ),
  'topthink/framework' => array (
    'type' => 'library',
    'name' => 'topthink/framework',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v8.1.3',
    'homepage' => 'http://thinkphp.cn/',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'The ThinkPHP Framework.',
  ),
  'topthink/think-container' => array (
    'type' => 'library',
    'name' => 'topthink/think-container',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.0.2',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'PHP Container & Facade Manager',
  ),
  'topthink/think-helper' => array (
    'type' => 'library',
    'name' => 'topthink/think-helper',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.1.11',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'The ThinkPHP6 Helper Package',
  ),
  'topthink/think-migration' => array (
    'type' => 'library',
    'name' => 'topthink/think-migration',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.1.1',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => '',
  ),
  'topthink/think-orm' => array (
    'type' => 'library',
    'name' => 'topthink/think-orm',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.0.34',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'the PHP Database&ORM Framework',
  ),
  'topthink/think-template' => array (
    'type' => 'library',
    'name' => 'topthink/think-template',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.0.2',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'the php template engine',
  ),
  'topthink/think-validate' => array (
    'type' => 'library',
    'name' => 'topthink/think-validate',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v3.0.7',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'think validate',
  ),
  'topthink/think-view' => array (
    'type' => 'library',
    'name' => 'topthink/think-view',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v2.0.5',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'thinkphp template driver',
  ),
  'zoujingli/ip2region' => array (
    'type' => 'library',
    'name' => 'zoujingli/ip2region',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v2.0.6',
    'homepage' => 'https://github.com/zoujingli/Ip2Region',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Ip2Region for PHP',
  ),
  'zoujingli/think-install' => array (
    'type' => 'library',
    'name' => 'zoujingli/think-install',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v1.0.49',
    'homepage' => 'https://thinkadmin.top',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Plugin Installer for ThinkAdmin',
  ),
  'zoujingli/think-library' => array (
    'type' => 'library',
    'name' => 'zoujingli/think-library',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => 'v6.1.86',
    'homepage' => 'https://thinkadmin.top',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Library for ThinkAdmin',
  ),
  'zoujingli/think-plugs-account' => array (
    'type' => 'plugin',
    'name' => '用户账号管理',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'VIP',
    ),
    'version' => 'v1.0.22',
    'homepage' => 'https://thinkadmin.top',
    'document' => 'https://thinkadmin.top/plugin/think-plugs-account.html',
    'platforms' => array (
    ),
    'description' => 'Account Plugin for ThinkAdmin',
  ),
  'zoujingli/think-plugs-admin' => array (
    'type' => 'module',
    'name' => '系统后台管理',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => 'v1.0.71',
    'homepage' => 'https://thinkadmin.top',
    'document' => 'https://thinkadmin.top/plugin/think-plugs-admin.html',
    'platforms' => array (
    ),
    'description' => '后台基础管理模块，系统账号及安全配置管理。',
  ),
  'zoujingli/think-plugs-center' => array (
    'type' => 'service',
    'name' => '插件应用管理',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'Apache-2.0',
    ),
    'version' => 'v1.0.37',
    'homepage' => 'https://thinkadmin.top',
    'document' => 'https://thinkadmin.top/plugin/think-plugs-center.html',
    'platforms' => array (
    ),
    'description' => 'Plugin Center for ThinkAdmin',
  ),
  'zoujingli/think-plugs-payment' => array (
    'type' => 'plugin',
    'name' => '系统支付管理',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'VIP',
    ),
    'version' => 'v1.0.15',
    'homepage' => 'https://thinkadmin.top',
    'document' => 'https://thinkadmin.top/plugin/think-plugs-payment.html',
    'platforms' => array (
    ),
    'description' => 'Payment Plugin for ThinkAdmin',
  ),
  'zoujingli/think-plugs-static' => array (
    'type' => 'plugin',
    'name' => 'zoujingli/think-plugs-static',
    'icon' => '',
    'cover' => '',
    'super' => false,
    'license' => array (
      0 => 'MIT',
    ),
    'version' => 'v1.0.132',
    'homepage' => '',
    'document' => '',
    'platforms' => array (
    ),
    'description' => 'Static Files for ThinkAdmin',
  ),
);