<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         colors="true"
         bootstrap="tests/.bootstrap.php"
         stopOnError="false"
         backupGlobals="false"
         stopOnFailure="false"
         processIsolation="false"
         backupStaticProperties="false"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd"
         cacheDirectory=".phpunit.cache"
         beStrictAboutTestsThatDoNotTestAnything="false"
>
    <coverage>
        <include>
            <directory suffix=".php">./src</directory>
        </include>
    </coverage>
    <testsuites>
        <testsuite name="ThinkAdmin Test Suite">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>
</phpunit>
