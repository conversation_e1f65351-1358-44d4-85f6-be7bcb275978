<?php

/**
 * 修复所有控制器，避免QueryHelper问题
 */

echo "=== 修复所有控制器，避免QueryHelper问题 ===\n\n";

// 需要修复的控制器列表
$controllers = [
    'RewriteRecord' => [
        'model' => 'RewriteRecord',
        'title' => '降重记录管理',
        'fields' => ['id', 'user_id', 'paper_project_id', 'original_content', 'rewritten_content', 'similarity_before', 'similarity_after', 'rewrite_model', 'status', 'create_time', 'update_time']
    ],
    'CheckRecord' => [
        'model' => 'CheckRecord', 
        'title' => '查重记录管理',
        'fields' => ['id', 'user_id', 'paper_project_id', 'content', 'similarity_rate', 'check_result', 'check_model', 'status', 'create_time', 'update_time']
    ],
    'VipPackage' => [
        'model' => 'VipPackage',
        'title' => 'VIP套餐管理', 
        'fields' => ['id', 'name', 'description', 'price', 'duration_days', 'features', 'sort', 'status', 'create_time', 'update_time']
    ],
    'UserPoints' => [
        'model' => 'UserPoints',
        'title' => '用户积分管理',
        'fields' => ['id', 'user_id', 'points', 'change_type', 'change_amount', 'description', 'create_time']
    ],
    'RechargeRecord' => [
        'model' => 'RechargeRecord', 
        'title' => '充值记录',
        'fields' => ['id', 'user_id', 'amount', 'payment_method', 'payment_status', 'transaction_id', 'create_time', 'update_time']
    ]
];

foreach ($controllers as $controllerName => $config) {
    echo "修复控制器: {$controllerName}\n";
    
    $controllerFile = "app/admin/controller/{$controllerName}.php";
    
    if (!file_exists($controllerFile)) {
        echo "  ❌ 控制器文件不存在: {$controllerFile}\n";
        continue;
    }
    
    // 读取原文件内容
    $content = file_get_contents($controllerFile);
    
    // 检查是否使用了mQuery
    if (strpos($content, '::mQuery') === false) {
        echo "  ✅ {$controllerName} 已经是标准实现\n";
        continue;
    }
    
    // 生成新的控制器内容
    $newContent = generateStandardController($controllerName, $config);
    
    // 备份原文件
    $backupFile = $controllerFile . '.backup_' . date('YmdHis');
    copy($controllerFile, $backupFile);
    echo "  📄 备份原文件: {$backupFile}\n";
    
    // 写入新内容
    file_put_contents($controllerFile, $newContent);
    echo "  ✅ 已更新: {$controllerFile}\n";
}

echo "\n=== 修复完成 ===\n";

/**
 * 生成标准控制器内容
 */
function generateStandardController($controllerName, $config) {
    $modelName = $config['model'];
    $title = $config['title'];
    $fields = $config['fields'];
    
    return <<<PHP
<?php

namespace app\admin\controller;

use app\admin\model\\{$modelName} as {$modelName}Model;
use think\admin\Controller;

/**
 * {$title}
 * @class {$controllerName}
 * @package app\admin\controller
 */
class {$controllerName} extends Controller
{
    /**
     * {$title}首页
     * @auth true
     * @menu true
     */
    public function index()
    {
        try {
            \$this->title = '{$title}';
            
            // 构建查询条件
            \$map = [];
            \$params = \$this->request->param();
            
            // 处理搜索条件
            if (!empty(\$params['name'])) {
                \$map[] = ['name', 'like', '%' . \$params['name'] . '%'];
            }
            
            if (!empty(\$params['title'])) {
                \$map[] = ['title', 'like', '%' . \$params['title'] . '%'];
            }
            
            if (!empty(\$params['description'])) {
                \$map[] = ['description', 'like', '%' . \$params['description'] . '%'];
            }
            
            if (isset(\$params['status']) && \$params['status'] !== '') {
                \$map['status'] = \$params['status'];
            }
            
            if (isset(\$params['user_id']) && \$params['user_id'] !== '') {
                \$map['user_id'] = \$params['user_id'];
            }
            
            // 时间范围查询
            if (!empty(\$params['create_time_start'])) {
                \$map[] = ['create_time', '>=', \$params['create_time_start']];
            }
            if (!empty(\$params['create_time_end'])) {
                \$map[] = ['create_time', '<=', \$params['create_time_end'] . ' 23:59:59'];
            }
            
            // 执行查询
            \$list = {$modelName}Model::where(\$map)
                ->order('id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => \$params
                ]);
            
            \$this->assign([
                'title' => '{$title}',
                'list' => \$list,
                'pagehtml' => \$list->render(),
                'get' => \$params
            ]);
            
            return \$this->fetch();
            
        } catch (\\Exception \$e) {
            \$this->error('页面加载失败：' . \$e->getMessage());
        }
    }
    
    /**
     * 添加{$title}
     * @auth true
     */
    public function add()
    {
        if (\$this->request->isPost()) {
            \$data = \$this->request->post();
            
            try {
                \$this->validate(\$data, [
                    'name|名称' => 'require|max:100',
                ]);
                
                {$modelName}Model::create(\$data);
                \$this->success('添加成功！', '');
                
            } catch (\\Exception \$e) {
                \$this->error('添加失败：' . \$e->getMessage());
            }
        }
        
        \$this->title = '添加{$title}';
        return \$this->fetch();
    }
    
    /**
     * 编辑{$title}
     * @auth true
     */
    public function edit()
    {
        \$id = \$this->request->param('id');
        \$model = {$modelName}Model::find(\$id);
        
        if (!\$model) {
            \$this->error('记录不存在！');
        }
        
        if (\$this->request->isPost()) {
            \$data = \$this->request->post();
            
            try {
                \$this->validate(\$data, [
                    'name|名称' => 'require|max:100',
                ]);
                
                \$model->save(\$data);
                \$this->success('编辑成功！', '');
                
            } catch (\\Exception \$e) {
                \$this->error('编辑失败：' . \$e->getMessage());
            }
        }
        
        \$this->assign('vo', \$model);
        \$this->title = '编辑{$title}';
        return \$this->fetch();
    }
    
    /**
     * 删除{$title}
     * @auth true
     */
    public function remove()
    {
        \$id = \$this->request->param('id');
        
        if (empty(\$id)) {
            \$this->error('参数错误！');
        }
        
        try {
            {$modelName}Model::destroy(\$id);
            \$this->success('删除成功！');
            
        } catch (\\Exception \$e) {
            \$this->error('删除失败：' . \$e->getMessage());
        }
    }
    
    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        \$id = \$this->request->param('id');
        \$status = \$this->request->param('status');
        
        if (empty(\$id)) {
            \$this->error('参数错误！');
        }
        
        try {
            {$modelName}Model::where('id', \$id)->update(['status' => \$status]);
            \$this->success('状态修改成功！');
            
        } catch (\\Exception \$e) {
            \$this->error('状态修改失败：' . \$e->getMessage());
        }
    }
}
PHP;
}
