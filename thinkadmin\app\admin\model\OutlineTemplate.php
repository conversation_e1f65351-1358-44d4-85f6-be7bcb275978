<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 大纲模板模型
 * @class OutlineTemplate
 * @package app\admin\model
 */
class OutlineTemplate extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'outline_template';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'paper_type_id' => 'integer',
        'is_default' => 'integer',
        'usage_count' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $status = $data['status'] ?? 0;
        return $status ? '启用' : '禁用';
    }

    /**
     * 获取默认模板标识
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getIsDefaultTextAttr($value, array $data): string
    {
        $isDefault = $data['is_default'] ?? 0;
        return $isDefault ? '是' : '否';
    }

    /**
     * 获取论文类型名称
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getPaperTypeNameAttr($value, array $data): string
    {
        if (empty($data['paper_type_id'])) {
            return '通用模板';
        }
        $paperType = PaperType::mk()->findOrEmpty($data['paper_type_id']);
        return $paperType->isEmpty() ? '未知类型' : $paperType['name'];
    }

    /**
     * 关联论文类型
     * @return \think\model\relation\BelongsTo
     */
    public function paperType()
    {
        return $this->belongsTo(PaperType::class, 'paper_type_id', 'id');
    }

    /**
     * 获取指定类型的默认模板
     * @param int $paperTypeId
     * @return array
     */
    public static function getDefaultTemplate(int $paperTypeId = 0): array
    {
        $template = static::mk()->where([
            'paper_type_id' => $paperTypeId,
            'is_default' => 1,
            'status' => 1
        ])->findOrEmpty();

        if ($template->isEmpty() && $paperTypeId > 0) {
            // 如果指定类型没有默认模板，尝试获取通用模板
            $template = static::mk()->where([
                'paper_type_id' => 0,
                'is_default' => 1,
                'status' => 1
            ])->findOrEmpty();
        }

        return $template->toArray();
    }

    /**
     * 获取可用的模板列表
     * @param int $paperTypeId
     * @return array
     */
    public static function getAvailableTemplates(int $paperTypeId = 0): array
    {
        $query = static::mk()->where(['status' => 1]);
        
        if ($paperTypeId > 0) {
            $query->whereIn('paper_type_id', [0, $paperTypeId]);
        } else {
            $query->where(['paper_type_id' => 0]);
        }
        
        return $query->order('is_default desc,usage_count desc,id desc')->column('name', 'id');
    }

    /**
     * 增加使用次数
     * @param int $templateId
     * @return bool
     */
    public static function increaseUsage(int $templateId): bool
    {
        return static::mk()->where(['id' => $templateId])->inc('usage_count')->update() > 0;
    }

    /**
     * 设置默认模板
     * @param int $templateId
     * @param int $paperTypeId
     * @return bool
     */
    public static function setDefault(int $templateId, int $paperTypeId = 0): bool
    {
        // 取消同类型的其他默认模板
        static::mk()->where([
            'paper_type_id' => $paperTypeId,
            'is_default' => 1
        ])->update(['is_default' => 0]);

        // 设置新的默认模板
        return static::mk()->where(['id' => $templateId])->update([
            'is_default' => 1,
            'update_time' => date('Y-m-d H:i:s')
        ]) > 0;
    }
}
