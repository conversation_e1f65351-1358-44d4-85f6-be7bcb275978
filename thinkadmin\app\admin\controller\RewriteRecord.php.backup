<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\RewriteTask;
use app\admin\model\RewriteResult;
use app\admin\model\AiModel;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 降重记录管理
 * @class RewriteRecord
 * @package app\admin\controller
 */
class RewriteRecord extends Controller
{
    /**
     * 降重记录管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        RewriteRecordModel::mQuery()->layTable(function () {
            $this->title = '降重记录管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            $query->like('title')->equal('user_id,ai_model_id,status,rewrite_mode');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 查看降重记录详情
     * @auth true
     */public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $task = RewriteTask::mk()->with(['aiModel'])->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('降重记录不存在！');
        }

        // 获取降重结果
        $results = RewriteResult::mk()
            ->with(['aiModel'])
            ->where('task_id', $id)
            ->order('id desc')
            ->select();

        $this->assign('task', $task);
        $this->assign('results', $results);
        $this->assign('title', '降重记录详情');
        
        return $this->fetch('rewrite_record/view');
    }

    /**
     * 查看降重结果对比
     * @auth true
     */
    public function compare()
    {
        $taskId = $this->request->get('task_id', 0);
        $resultId = $this->request->get('result_id', 0);
        
        if (empty($taskId) || empty($resultId)) {
            $this->error('参数不完整！');
        }

        $task = RewriteTask::mk()->findOrEmpty($taskId);
        if ($task->isEmpty()) {
            $this->error('降重任务不存在！');
        }

        $result = RewriteResult::mk()->with(['aiModel'])->findOrEmpty($resultId);
        if ($result->isEmpty() || $result->task_id != $taskId) {
            $this->error('降重结果不存在！');
        }

        $this->assign('task', $task);
        $this->assign('result', $result);
        $this->assign('title', '降重对比');
        
        return $this->fetch('rewrite_record/compare');
    }

    /**
     * 导出降重记录
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的记录！');
        }

        $idArray = explode(',', $ids);
        $records = RewriteTask::mk()
            ->with(['aiModel'])
            ->whereIn('id', $idArray)
            ->order('id desc')
            ->select();

        if ($records->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '任务标题', '用户ID', '原文字数', '降重模式', '目标相似度', 'AI模型', '状态', '进度', '创建时间'];
        
        foreach ($records as $record) {
            $exportData[] = [
                $record->id,
                $record->title,
                $record->user_id,
                $record->original_word_count,
                $record->rewrite_mode_text,
                $record->target_similarity . '%',
                $record->aiModel->name ?? '未知',
                $record->status_text,
                $record->progress . '%',
                $record->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 删除降重记录
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $task = RewriteTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('降重记录不存在！');
        }

        // 删除相关的降重结果
        RewriteResult::mk()->where('task_id', $id)->delete();
        
        // 删除降重任务
        if ($task->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 批量删除降重记录
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的记录！');
        }

        $idArray = explode(',', $ids);
        
        // 删除相关的降重结果
        RewriteResult::mk()->whereIn('task_id', $idArray)->delete();
        
        // 删除降重任务
        $result = RewriteTask::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }

    /**
     * 重新处理降重任务
     * @auth true
     */
    public function reprocess()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = RewriteTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('降重任务不存在！');
        }

        if ($task->status === 'processing') {
            $this->error('任务正在处理中，请勿重复操作！');
        }

        // 重置任务状态
        $updateData = [
            'status' => 'pending',
            'progress' => 0,
            'error_message' => null,
            'update_time' => date('Y-m-d H:i:s')
        ];

        if ($task->save($updateData)) {
            // 这里应该触发重新处理的逻辑，比如调用n8n工作流
            $this->success('任务已重新提交处理！');
        } else {
            $this->error('重新处理失败！');
        }
    }

    /**
     * 获取降重统计数据
     * @auth true
     */
    public function statistics()
    {
        // 总任务数
        $totalTasks = RewriteTask::mk()->count();
        
        // 各状态任务数
        $statusStats = RewriteTask::mk()
            ->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();

        // 今日任务数
        $todayTasks = RewriteTask::mk()
            ->whereTime('create_time', 'today')
            ->count();

        // 本月任务数
        $monthTasks = RewriteTask::mk()
            ->whereTime('create_time', 'month')
            ->count();

        // 平均处理时间（基于已完成的任务）
        $avgProcessingTime = RewriteResult::mk()
            ->avg('processing_time');

        $statistics = [
            'total_tasks' => $totalTasks,
            'today_tasks' => $todayTasks,
            'month_tasks' => $monthTasks,
            'status_stats' => $statusStats,
            'avg_processing_time' => round($avgProcessingTime, 2)
        ];

        return json($statistics);
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'RewriteRecord管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
