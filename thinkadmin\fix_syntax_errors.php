<?php

/**
 * 修复所有控制器的语法错误
 */

echo "=== 修复所有控制器的语法错误 ===\n\n";

$controllersToCheck = [
    'PaperType',
    'PromptTemplate', 
    'OutlineTemplate',
    'RewriteRecord',
    'CheckRecord',
    'ExportTemplate',
    'VipPackage',
    'Order',
    'EmailConfig',
    'WebhookConfig',
    'ContentFilter',
    'RewriteModel'
];

$fixedCount = 0;
$errorCount = 0;

foreach ($controllersToCheck as $controller) {
    $filePath = "app/admin/controller/{$controller}.php";
    
    echo "检查控制器: {$controller}\n";
    
    if (!file_exists($filePath)) {
        echo "  ⚠️  文件不存在，跳过\n";
        continue;
    }
    
    // 检查语法
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ 语法正确\n";
        continue;
    }
    
    echo "  🔧 发现语法错误，正在修复...\n";
    
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    
    // 查找并修复常见的语法错误
    $fixed = false;
    
    // 1. 修复缺失的catch块
    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];
        
        // 如果找到try块但没有对应的catch块
        if (strpos($line, 'try {') !== false) {
            $tryLine = $i;
            $braceCount = 0;
            $foundCatch = false;
            
            // 查找对应的catch块
            for ($j = $i; $j < count($lines); $j++) {
                $currentLine = $lines[$j];
                $braceCount += substr_count($currentLine, '{') - substr_count($currentLine, '}');
                
                if (strpos($currentLine, 'catch') !== false) {
                    $foundCatch = true;
                    break;
                }
                
                // 如果遇到下一个方法定义，说明catch块缺失
                if ($j > $i && preg_match('/^\s*public function/', $currentLine) && $braceCount <= 0) {
                    // 在这里插入catch块
                    $catchBlock = [
                        "        } catch (\\Exception \$e) {",
                        "            // 如果QueryHelper出现问题，使用简化查询",
                        "            \$this->title = '{$controller}管理';",
                        "            \$this->error('页面加载失败：' . \$e->getMessage());",
                        "        }"
                    ];
                    
                    array_splice($lines, $j, 0, $catchBlock);
                    $fixed = true;
                    echo "    ✅ 添加了缺失的catch块\n";
                    break;
                }
            }
        }
    }
    
    // 2. 修复多余的catch块
    $cleanedLines = [];
    $skipNext = false;
    
    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];
        
        // 检查是否是孤立的catch块（前面没有对应的try）
        if (preg_match('/^\s*} catch \(/', $line)) {
            // 检查前面是否有对应的try
            $hasTry = false;
            for ($j = $i - 1; $j >= 0; $j--) {
                if (strpos($lines[$j], 'try {') !== false) {
                    $hasTry = true;
                    break;
                }
                if (preg_match('/^\s*public function/', $lines[$j])) {
                    break;
                }
            }
            
            if (!$hasTry) {
                // 跳过这个孤立的catch块
                echo "    ✅ 删除了孤立的catch块\n";
                $fixed = true;
                
                // 跳过整个catch块
                $braceCount = 0;
                while ($i < count($lines)) {
                    $currentLine = $lines[$i];
                    $braceCount += substr_count($currentLine, '{') - substr_count($currentLine, '}');
                    $i++;
                    
                    if ($braceCount <= 0) {
                        break;
                    }
                }
                $i--; // 调整索引
                continue;
            }
        }
        
        $cleanedLines[] = $line;
    }
    
    if ($fixed) {
        $content = implode("\n", $cleanedLines);
        file_put_contents($filePath, $content);
        
        // 再次检查语法
        $output = [];
        $returnCode = 0;
        exec("php -l {$filePath} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ 修复成功\n";
            $fixedCount++;
        } else {
            echo "  ❌ 修复失败: " . implode(' ', $output) . "\n";
            $errorCount++;
        }
    } else {
        echo "  ⚠️  无法自动修复，需要手动检查\n";
        $errorCount++;
    }
}

echo "\n=== 修复完成 ===\n";
echo "成功修复: {$fixedCount} 个\n";
echo "需要手动检查: {$errorCount} 个\n";

// 最终语法检查
echo "\n=== 最终语法检查 ===\n";
foreach ($controllersToCheck as $controller) {
    $filePath = "app/admin/controller/{$controller}.php";
    
    if (!file_exists($filePath)) {
        continue;
    }
    
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ {$controller}.php - 语法正确\n";
    } else {
        echo "❌ {$controller}.php - 仍有语法错误\n";
    }
}

echo "\n=== 全部完成 ===\n";
