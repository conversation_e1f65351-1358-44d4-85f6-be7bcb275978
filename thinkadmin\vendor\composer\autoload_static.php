<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1
{
    public static $files = array (
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
        '35fab96057f1bf5e7aba31a8a6d5fdde' => __DIR__ . '/..' . '/topthink/think-orm/stubs/load_stubs.php',
        '15ec93fa4ce4b2d53816a1a5f2c514e2' => __DIR__ . '/..' . '/topthink/think-validate/src/helper.php',
        '8dafcc6956460bc297e00381fed53e11' => __DIR__ . '/..' . '/zoujingli/think-library/src/common.php',
        '205331db7aa2d91641859148c9816601' => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src/helper.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\view\\driver\\' => 18,
            'think\\migration\\' => 16,
            'think\\admin\\install\\' => 20,
            'think\\admin\\' => 12,
            'think\\' => 6,
        ),
        'p' => 
        array (
            'plugin\\payment\\' => 15,
            'plugin\\center\\' => 14,
            'plugin\\account\\' => 15,
        ),
        'a' => 
        array (
            'app\\admin\\' => 10,
            'app\\' => 4,
        ),
        'S' => 
        array (
            'Symfony\\Component\\Process\\' => 26,
        ),
        'P' => 
        array (
            'Psr\\SimpleCache\\' => 16,
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Container\\' => 14,
            'Phinx\\' => 6,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\view\\driver\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-view/src',
        ),
        'think\\migration\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-migration/src',
        ),
        'think\\admin\\install\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-install/src',
        ),
        'think\\admin\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-library/src',
        ),
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-template/src',
            1 => __DIR__ . '/..' . '/topthink/framework/src/think',
            2 => __DIR__ . '/..' . '/topthink/think-validate/src',
            3 => __DIR__ . '/..' . '/topthink/think-orm/src',
            4 => __DIR__ . '/..' . '/topthink/think-container/src',
            5 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'plugin\\payment\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src',
        ),
        'plugin\\center\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src',
        ),
        'plugin\\account\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src',
        ),
        'app\\admin\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-admin/src',
        ),
        'app\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Symfony\\Component\\Process\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/process',
        ),
        'Psr\\SimpleCache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/simple-cache/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Phinx\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-migration/phinx',
        ),
    );

    public static $fallbackDirsPsr0 = array (
        0 => __DIR__ . '/../..' . '/extend',
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Ip2Region' => __DIR__ . '/..' . '/zoujingli/ip2region/Ip2Region.php',
        'Phinx\\Config\\Config' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Config/Config.php',
        'Phinx\\Config\\ConfigInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Config/ConfigInterface.php',
        'Phinx\\Config\\FeatureFlags' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Config/FeatureFlags.php',
        'Phinx\\Config\\NamespaceAwareInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Config/NamespaceAwareInterface.php',
        'Phinx\\Config\\NamespaceAwareTrait' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Config/NamespaceAwareTrait.php',
        'Phinx\\Db\\Action\\Action' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/Action.php',
        'Phinx\\Db\\Action\\AddColumn' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/AddColumn.php',
        'Phinx\\Db\\Action\\AddForeignKey' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/AddForeignKey.php',
        'Phinx\\Db\\Action\\AddIndex' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/AddIndex.php',
        'Phinx\\Db\\Action\\ChangeColumn' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/ChangeColumn.php',
        'Phinx\\Db\\Action\\ChangeComment' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/ChangeComment.php',
        'Phinx\\Db\\Action\\ChangePrimaryKey' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/ChangePrimaryKey.php',
        'Phinx\\Db\\Action\\CreateTable' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/CreateTable.php',
        'Phinx\\Db\\Action\\DropForeignKey' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/DropForeignKey.php',
        'Phinx\\Db\\Action\\DropIndex' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/DropIndex.php',
        'Phinx\\Db\\Action\\DropTable' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/DropTable.php',
        'Phinx\\Db\\Action\\RemoveColumn' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/RemoveColumn.php',
        'Phinx\\Db\\Action\\RenameColumn' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/RenameColumn.php',
        'Phinx\\Db\\Action\\RenameTable' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Action/RenameTable.php',
        'Phinx\\Db\\Adapter\\AbstractAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/AbstractAdapter.php',
        'Phinx\\Db\\Adapter\\AdapterFactory' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/AdapterFactory.php',
        'Phinx\\Db\\Adapter\\AdapterInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/AdapterInterface.php',
        'Phinx\\Db\\Adapter\\AdapterWrapper' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/AdapterWrapper.php',
        'Phinx\\Db\\Adapter\\DirectActionInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/DirectActionInterface.php',
        'Phinx\\Db\\Adapter\\MysqlAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/MysqlAdapter.php',
        'Phinx\\Db\\Adapter\\PdoAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/PdoAdapter.php',
        'Phinx\\Db\\Adapter\\PostgresAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/PostgresAdapter.php',
        'Phinx\\Db\\Adapter\\ProxyAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/ProxyAdapter.php',
        'Phinx\\Db\\Adapter\\SQLiteAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/SQLiteAdapter.php',
        'Phinx\\Db\\Adapter\\SqlServerAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/SqlServerAdapter.php',
        'Phinx\\Db\\Adapter\\TablePrefixAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/TablePrefixAdapter.php',
        'Phinx\\Db\\Adapter\\TimedOutputAdapter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/TimedOutputAdapter.php',
        'Phinx\\Db\\Adapter\\UnsupportedColumnTypeException' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/UnsupportedColumnTypeException.php',
        'Phinx\\Db\\Adapter\\WrapperInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Adapter/WrapperInterface.php',
        'Phinx\\Db\\Plan\\AlterTable' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Plan/AlterTable.php',
        'Phinx\\Db\\Plan\\Intent' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Plan/Intent.php',
        'Phinx\\Db\\Plan\\NewTable' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Plan/NewTable.php',
        'Phinx\\Db\\Plan\\Plan' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Plan/Plan.php',
        'Phinx\\Db\\Plan\\Solver\\ActionSplitter' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Plan/Solver/ActionSplitter.php',
        'Phinx\\Db\\Table' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Table.php',
        'Phinx\\Db\\Table\\Column' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Table/Column.php',
        'Phinx\\Db\\Table\\ForeignKey' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Table/ForeignKey.php',
        'Phinx\\Db\\Table\\Index' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Table/Index.php',
        'Phinx\\Db\\Table\\Table' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Table/Table.php',
        'Phinx\\Db\\Util\\AlterInstructions' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Db/Util/AlterInstructions.php',
        'Phinx\\Migration\\AbstractMigration' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/AbstractMigration.php',
        'Phinx\\Migration\\AbstractTemplateCreation' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/AbstractTemplateCreation.php',
        'Phinx\\Migration\\CreationInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/CreationInterface.php',
        'Phinx\\Migration\\IrreversibleMigrationException' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/IrreversibleMigrationException.php',
        'Phinx\\Migration\\Manager' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/Manager.php',
        'Phinx\\Migration\\Manager\\Environment' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/Manager/Environment.php',
        'Phinx\\Migration\\MigrationInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Migration/MigrationInterface.php',
        'Phinx\\Seed\\AbstractSeed' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Seed/AbstractSeed.php',
        'Phinx\\Seed\\SeedInterface' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Seed/SeedInterface.php',
        'Phinx\\Util\\Expression' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Util/Expression.php',
        'Phinx\\Util\\Literal' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Util/Literal.php',
        'Phinx\\Util\\Util' => __DIR__ . '/..' . '/topthink/think-migration/phinx/Util/Util.php',
        'Psr\\Container\\ContainerExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerExceptionInterface.php',
        'Psr\\Container\\ContainerInterface' => __DIR__ . '/..' . '/psr/container/src/ContainerInterface.php',
        'Psr\\Container\\NotFoundExceptionInterface' => __DIR__ . '/..' . '/psr/container/src/NotFoundExceptionInterface.php',
        'Psr\\Http\\Message\\MessageInterface' => __DIR__ . '/..' . '/psr/http-message/src/MessageInterface.php',
        'Psr\\Http\\Message\\RequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/RequestInterface.php',
        'Psr\\Http\\Message\\ResponseInterface' => __DIR__ . '/..' . '/psr/http-message/src/ResponseInterface.php',
        'Psr\\Http\\Message\\ServerRequestInterface' => __DIR__ . '/..' . '/psr/http-message/src/ServerRequestInterface.php',
        'Psr\\Http\\Message\\StreamInterface' => __DIR__ . '/..' . '/psr/http-message/src/StreamInterface.php',
        'Psr\\Http\\Message\\UploadedFileInterface' => __DIR__ . '/..' . '/psr/http-message/src/UploadedFileInterface.php',
        'Psr\\Http\\Message\\UriInterface' => __DIR__ . '/..' . '/psr/http-message/src/UriInterface.php',
        'Psr\\Log\\AbstractLogger' => __DIR__ . '/..' . '/psr/log/src/AbstractLogger.php',
        'Psr\\Log\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/log/src/InvalidArgumentException.php',
        'Psr\\Log\\LogLevel' => __DIR__ . '/..' . '/psr/log/src/LogLevel.php',
        'Psr\\Log\\LoggerAwareInterface' => __DIR__ . '/..' . '/psr/log/src/LoggerAwareInterface.php',
        'Psr\\Log\\LoggerAwareTrait' => __DIR__ . '/..' . '/psr/log/src/LoggerAwareTrait.php',
        'Psr\\Log\\LoggerInterface' => __DIR__ . '/..' . '/psr/log/src/LoggerInterface.php',
        'Psr\\Log\\LoggerTrait' => __DIR__ . '/..' . '/psr/log/src/LoggerTrait.php',
        'Psr\\Log\\NullLogger' => __DIR__ . '/..' . '/psr/log/src/NullLogger.php',
        'Psr\\SimpleCache\\CacheException' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheException.php',
        'Psr\\SimpleCache\\CacheInterface' => __DIR__ . '/..' . '/psr/simple-cache/src/CacheInterface.php',
        'Psr\\SimpleCache\\InvalidArgumentException' => __DIR__ . '/..' . '/psr/simple-cache/src/InvalidArgumentException.php',
        'Symfony\\Component\\Process\\Exception\\ExceptionInterface' => __DIR__ . '/..' . '/symfony/process/Exception/ExceptionInterface.php',
        'Symfony\\Component\\Process\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/symfony/process/Exception/InvalidArgumentException.php',
        'Symfony\\Component\\Process\\Exception\\LogicException' => __DIR__ . '/..' . '/symfony/process/Exception/LogicException.php',
        'Symfony\\Component\\Process\\Exception\\ProcessFailedException' => __DIR__ . '/..' . '/symfony/process/Exception/ProcessFailedException.php',
        'Symfony\\Component\\Process\\Exception\\ProcessSignaledException' => __DIR__ . '/..' . '/symfony/process/Exception/ProcessSignaledException.php',
        'Symfony\\Component\\Process\\Exception\\ProcessStartFailedException' => __DIR__ . '/..' . '/symfony/process/Exception/ProcessStartFailedException.php',
        'Symfony\\Component\\Process\\Exception\\ProcessTimedOutException' => __DIR__ . '/..' . '/symfony/process/Exception/ProcessTimedOutException.php',
        'Symfony\\Component\\Process\\Exception\\RunProcessFailedException' => __DIR__ . '/..' . '/symfony/process/Exception/RunProcessFailedException.php',
        'Symfony\\Component\\Process\\Exception\\RuntimeException' => __DIR__ . '/..' . '/symfony/process/Exception/RuntimeException.php',
        'Symfony\\Component\\Process\\ExecutableFinder' => __DIR__ . '/..' . '/symfony/process/ExecutableFinder.php',
        'Symfony\\Component\\Process\\InputStream' => __DIR__ . '/..' . '/symfony/process/InputStream.php',
        'Symfony\\Component\\Process\\Messenger\\RunProcessContext' => __DIR__ . '/..' . '/symfony/process/Messenger/RunProcessContext.php',
        'Symfony\\Component\\Process\\Messenger\\RunProcessMessage' => __DIR__ . '/..' . '/symfony/process/Messenger/RunProcessMessage.php',
        'Symfony\\Component\\Process\\Messenger\\RunProcessMessageHandler' => __DIR__ . '/..' . '/symfony/process/Messenger/RunProcessMessageHandler.php',
        'Symfony\\Component\\Process\\PhpExecutableFinder' => __DIR__ . '/..' . '/symfony/process/PhpExecutableFinder.php',
        'Symfony\\Component\\Process\\PhpProcess' => __DIR__ . '/..' . '/symfony/process/PhpProcess.php',
        'Symfony\\Component\\Process\\PhpSubprocess' => __DIR__ . '/..' . '/symfony/process/PhpSubprocess.php',
        'Symfony\\Component\\Process\\Pipes\\AbstractPipes' => __DIR__ . '/..' . '/symfony/process/Pipes/AbstractPipes.php',
        'Symfony\\Component\\Process\\Pipes\\PipesInterface' => __DIR__ . '/..' . '/symfony/process/Pipes/PipesInterface.php',
        'Symfony\\Component\\Process\\Pipes\\UnixPipes' => __DIR__ . '/..' . '/symfony/process/Pipes/UnixPipes.php',
        'Symfony\\Component\\Process\\Pipes\\WindowsPipes' => __DIR__ . '/..' . '/symfony/process/Pipes/WindowsPipes.php',
        'Symfony\\Component\\Process\\Process' => __DIR__ . '/..' . '/symfony/process/Process.php',
        'Symfony\\Component\\Process\\ProcessUtils' => __DIR__ . '/..' . '/symfony/process/ProcessUtils.php',
        'XdbSearcher' => __DIR__ . '/..' . '/zoujingli/ip2region/XdbSearcher.php',
        'app\\admin\\Service' => __DIR__ . '/../..' . '/app/admin/Service.php',
        'app\\admin\\controller\\Auth' => __DIR__ . '/../..' . '/app/admin/controller/Auth.php',
        'app\\admin\\controller\\Base' => __DIR__ . '/../..' . '/app/admin/controller/Base.php',
        'app\\admin\\controller\\Config' => __DIR__ . '/../..' . '/app/admin/controller/Config.php',
        'app\\admin\\controller\\File' => __DIR__ . '/../..' . '/app/admin/controller/File.php',
        'app\\admin\\controller\\Index' => __DIR__ . '/../..' . '/app/admin/controller/Index.php',
        'app\\admin\\controller\\Login' => __DIR__ . '/../..' . '/app/admin/controller/Login.php',
        'app\\admin\\controller\\Menu' => __DIR__ . '/../..' . '/app/admin/controller/Menu.php',
        'app\\admin\\controller\\Oplog' => __DIR__ . '/../..' . '/app/admin/controller/Oplog.php',
        'app\\admin\\controller\\Queue' => __DIR__ . '/../..' . '/app/admin/controller/Queue.php',
        'app\\admin\\controller\\User' => __DIR__ . '/../..' . '/app/admin/controller/User.php',
        'app\\admin\\controller\\api\\Plugs' => __DIR__ . '/../..' . '/app/admin/controller/api/Plugs.php',
        'app\\admin\\controller\\api\\Queue' => __DIR__ . '/../..' . '/app/admin/controller/api/Queue.php',
        'app\\admin\\controller\\api\\System' => __DIR__ . '/../..' . '/app/admin/controller/api/System.php',
        'app\\admin\\controller\\api\\Upload' => __DIR__ . '/../..' . '/app/admin/controller/api/Upload.php',
        'app\\index\\controller\\Index' => __DIR__ . '/../..' . '/app/index/controller/Index.php',
        'plugin\\account\\Service' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/Service.php',
        'plugin\\account\\controller\\Device' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/Device.php',
        'plugin\\account\\controller\\Master' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/Master.php',
        'plugin\\account\\controller\\Message' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/Message.php',
        'plugin\\account\\controller\\api\\Auth' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/api/Auth.php',
        'plugin\\account\\controller\\api\\Login' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/api/Login.php',
        'plugin\\account\\controller\\api\\Wechat' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/api/Wechat.php',
        'plugin\\account\\controller\\api\\Wxapp' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/api/Wxapp.php',
        'plugin\\account\\controller\\api\\auth\\Center' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/controller/api/auth/Center.php',
        'plugin\\account\\model\\Abs' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/model/Abs.php',
        'plugin\\account\\model\\PluginAccountAuth' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/model/PluginAccountAuth.php',
        'plugin\\account\\model\\PluginAccountBind' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/model/PluginAccountBind.php',
        'plugin\\account\\model\\PluginAccountMsms' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/model/PluginAccountMsms.php',
        'plugin\\account\\model\\PluginAccountUser' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/model/PluginAccountUser.php',
        'plugin\\account\\service\\Account' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/Account.php',
        'plugin\\account\\service\\Message' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/Message.php',
        'plugin\\account\\service\\contract\\AccountAccess' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/contract/AccountAccess.php',
        'plugin\\account\\service\\contract\\AccountInterface' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/contract/AccountInterface.php',
        'plugin\\account\\service\\contract\\MessageInterface' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/contract/MessageInterface.php',
        'plugin\\account\\service\\contract\\MessageUsageTrait' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/contract/MessageUsageTrait.php',
        'plugin\\account\\service\\message\\Alisms' => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src/service/message/Alisms.php',
        'plugin\\center\\Service' => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src/Service.php',
        'plugin\\center\\controller\\Index' => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src/controller/Index.php',
        'plugin\\center\\service\\Plugin' => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src/service/Plugin.php',
        'plugin\\payment\\Service' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/Service.php',
        'plugin\\payment\\controller\\Balance' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/Balance.php',
        'plugin\\payment\\controller\\Config' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/Config.php',
        'plugin\\payment\\controller\\Integral' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/Integral.php',
        'plugin\\payment\\controller\\Record' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/Record.php',
        'plugin\\payment\\controller\\Refund' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/Refund.php',
        'plugin\\payment\\controller\\api\\auth\\Address' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/api/auth/Address.php',
        'plugin\\payment\\controller\\api\\auth\\Balance' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/api/auth/Balance.php',
        'plugin\\payment\\controller\\api\\auth\\Integral' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/controller/api/auth/Integral.php',
        'plugin\\payment\\model\\PluginPaymentAddress' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentAddress.php',
        'plugin\\payment\\model\\PluginPaymentBalance' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentBalance.php',
        'plugin\\payment\\model\\PluginPaymentConfig' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentConfig.php',
        'plugin\\payment\\model\\PluginPaymentIntegral' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentIntegral.php',
        'plugin\\payment\\model\\PluginPaymentRecord' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentRecord.php',
        'plugin\\payment\\model\\PluginPaymentRefund' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/model/PluginPaymentRefund.php',
        'plugin\\payment\\queue\\Recount' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/queue/Recount.php',
        'plugin\\payment\\service\\Balance' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/Balance.php',
        'plugin\\payment\\service\\Integral' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/Integral.php',
        'plugin\\payment\\service\\Payment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/Payment.php',
        'plugin\\payment\\service\\contract\\PaymentInterface' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/contract/PaymentInterface.php',
        'plugin\\payment\\service\\contract\\PaymentResponse' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/contract/PaymentResponse.php',
        'plugin\\payment\\service\\contract\\PaymentUsageTrait' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/contract/PaymentUsageTrait.php',
        'plugin\\payment\\service\\payment\\AliPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/AliPayment.php',
        'plugin\\payment\\service\\payment\\BalancePayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/BalancePayment.php',
        'plugin\\payment\\service\\payment\\CouponPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/CouponPayment.php',
        'plugin\\payment\\service\\payment\\EmptyPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/EmptyPayment.php',
        'plugin\\payment\\service\\payment\\IntegralPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/IntegralPayment.php',
        'plugin\\payment\\service\\payment\\JoinPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/JoinPayment.php',
        'plugin\\payment\\service\\payment\\VoucherPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/VoucherPayment.php',
        'plugin\\payment\\service\\payment\\WechatPayment' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/WechatPayment.php',
        'plugin\\payment\\service\\payment\\wechat\\WechatPaymentV2' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/wechat/WechatPaymentV2.php',
        'plugin\\payment\\service\\payment\\wechat\\WechatPaymentV3' => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src/service/payment/wechat/WechatPaymentV3.php',
        'think\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/App.php',
        'think\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/Cache.php',
        'think\\Collection' => __DIR__ . '/..' . '/topthink/think-helper/src/Collection.php',
        'think\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/Config.php',
        'think\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/Console.php',
        'think\\Container' => __DIR__ . '/..' . '/topthink/think-container/src/Container.php',
        'think\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/Cookie.php',
        'think\\Db' => __DIR__ . '/..' . '/topthink/framework/src/think/Db.php',
        'think\\DbManager' => __DIR__ . '/..' . '/topthink/think-orm/src/DbManager.php',
        'think\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/Env.php',
        'think\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/Event.php',
        'think\\Exception' => __DIR__ . '/..' . '/topthink/framework/src/think/Exception.php',
        'think\\Facade' => __DIR__ . '/..' . '/topthink/think-container/src/Facade.php',
        'think\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/File.php',
        'think\\Http' => __DIR__ . '/..' . '/topthink/framework/src/think/Http.php',
        'think\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/Lang.php',
        'think\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/Log.php',
        'think\\Manager' => __DIR__ . '/..' . '/topthink/framework/src/think/Manager.php',
        'think\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/Middleware.php',
        'think\\Model' => __DIR__ . '/..' . '/topthink/think-orm/src/Model.php',
        'think\\Paginator' => __DIR__ . '/..' . '/topthink/think-orm/src/Paginator.php',
        'think\\Pipeline' => __DIR__ . '/..' . '/topthink/framework/src/think/Pipeline.php',
        'think\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/Request.php',
        'think\\Response' => __DIR__ . '/..' . '/topthink/framework/src/think/Response.php',
        'think\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/Route.php',
        'think\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/Service.php',
        'think\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/Session.php',
        'think\\Template' => __DIR__ . '/..' . '/topthink/think-template/src/Template.php',
        'think\\Validate' => __DIR__ . '/..' . '/topthink/think-validate/src/Validate.php',
        'think\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/View.php',
        'think\\admin\\Builder' => __DIR__ . '/..' . '/zoujingli/think-library/src/Builder.php',
        'think\\admin\\Command' => __DIR__ . '/..' . '/zoujingli/think-library/src/Command.php',
        'think\\admin\\Controller' => __DIR__ . '/..' . '/zoujingli/think-library/src/Controller.php',
        'think\\admin\\Exception' => __DIR__ . '/..' . '/zoujingli/think-library/src/Exception.php',
        'think\\admin\\Helper' => __DIR__ . '/..' . '/zoujingli/think-library/src/Helper.php',
        'think\\admin\\Library' => __DIR__ . '/..' . '/zoujingli/think-library/src/Library.php',
        'think\\admin\\Model' => __DIR__ . '/..' . '/zoujingli/think-library/src/Model.php',
        'think\\admin\\Plugin' => __DIR__ . '/..' . '/zoujingli/think-library/src/Plugin.php',
        'think\\admin\\Queue' => __DIR__ . '/..' . '/zoujingli/think-library/src/Queue.php',
        'think\\admin\\Service' => __DIR__ . '/..' . '/zoujingli/think-library/src/Service.php',
        'think\\admin\\Storage' => __DIR__ . '/..' . '/zoujingli/think-library/src/Storage.php',
        'think\\admin\\contract\\StorageInterface' => __DIR__ . '/..' . '/zoujingli/think-library/src/contract/StorageInterface.php',
        'think\\admin\\contract\\StorageUsageTrait' => __DIR__ . '/..' . '/zoujingli/think-library/src/contract/StorageUsageTrait.php',
        'think\\admin\\contract\\StreamInterface' => __DIR__ . '/..' . '/zoujingli/think-library/src/contract/StreamInterface.php',
        'think\\admin\\extend\\CodeExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/CodeExtend.php',
        'think\\admin\\extend\\DataExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/DataExtend.php',
        'think\\admin\\extend\\ExcelExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/ExcelExtend.php',
        'think\\admin\\extend\\FaviconExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/FaviconExtend.php',
        'think\\admin\\extend\\HttpExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/HttpExtend.php',
        'think\\admin\\extend\\ImageVerify' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/ImageVerify.php',
        'think\\admin\\extend\\JsonRpcClient' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/JsonRpcClient.php',
        'think\\admin\\extend\\JsonRpcServer' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/JsonRpcServer.php',
        'think\\admin\\extend\\JwtExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/JwtExtend.php',
        'think\\admin\\extend\\PhinxExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/PhinxExtend.php',
        'think\\admin\\extend\\ToolsExtend' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/ToolsExtend.php',
        'think\\admin\\extend\\VirtualModel' => __DIR__ . '/..' . '/zoujingli/think-library/src/extend/VirtualModel.php',
        'think\\admin\\helper\\DeleteHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/DeleteHelper.php',
        'think\\admin\\helper\\FormHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/FormHelper.php',
        'think\\admin\\helper\\PageHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/PageHelper.php',
        'think\\admin\\helper\\QueryHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/QueryHelper.php',
        'think\\admin\\helper\\SaveHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/SaveHelper.php',
        'think\\admin\\helper\\TokenHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/TokenHelper.php',
        'think\\admin\\helper\\ValidateHelper' => __DIR__ . '/..' . '/zoujingli/think-library/src/helper/ValidateHelper.php',
        'think\\admin\\install\\Installer' => __DIR__ . '/..' . '/zoujingli/think-install/src/Installer.php',
        'think\\admin\\install\\Service' => __DIR__ . '/..' . '/zoujingli/think-install/src/Service.php',
        'think\\admin\\install\\Support' => __DIR__ . '/..' . '/zoujingli/think-install/src/Support.php',
        'think\\admin\\model\\SystemAuth' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemAuth.php',
        'think\\admin\\model\\SystemBase' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemBase.php',
        'think\\admin\\model\\SystemConfig' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemConfig.php',
        'think\\admin\\model\\SystemData' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemData.php',
        'think\\admin\\model\\SystemFile' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemFile.php',
        'think\\admin\\model\\SystemMenu' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemMenu.php',
        'think\\admin\\model\\SystemNode' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemNode.php',
        'think\\admin\\model\\SystemOplog' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemOplog.php',
        'think\\admin\\model\\SystemQueue' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemQueue.php',
        'think\\admin\\model\\SystemUser' => __DIR__ . '/..' . '/zoujingli/think-library/src/model/SystemUser.php',
        'think\\admin\\service\\AdminService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/AdminService.php',
        'think\\admin\\service\\CaptchaService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/CaptchaService.php',
        'think\\admin\\service\\ExpressService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/ExpressService.php',
        'think\\admin\\service\\InterfaceService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/InterfaceService.php',
        'think\\admin\\service\\MenuService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/MenuService.php',
        'think\\admin\\service\\MessageService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/MessageService.php',
        'think\\admin\\service\\ModuleService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/ModuleService.php',
        'think\\admin\\service\\NodeService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/NodeService.php',
        'think\\admin\\service\\ProcessService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/ProcessService.php',
        'think\\admin\\service\\QueueService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/QueueService.php',
        'think\\admin\\service\\RuntimeService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/RuntimeService.php',
        'think\\admin\\service\\SystemService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/SystemService.php',
        'think\\admin\\service\\ZtSmsService' => __DIR__ . '/..' . '/zoujingli/think-library/src/service/ZtSmsService.php',
        'think\\admin\\storage\\AliossStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/AliossStorage.php',
        'think\\admin\\storage\\AlistStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/AlistStorage.php',
        'think\\admin\\storage\\LocalStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/LocalStorage.php',
        'think\\admin\\storage\\QiniuStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/QiniuStorage.php',
        'think\\admin\\storage\\TxcosStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/TxcosStorage.php',
        'think\\admin\\storage\\UpyunStorage' => __DIR__ . '/..' . '/zoujingli/think-library/src/storage/UpyunStorage.php',
        'think\\admin\\support\\Route' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/Route.php',
        'think\\admin\\support\\Url' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/Url.php',
        'think\\admin\\support\\command\\Database' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Database.php',
        'think\\admin\\support\\command\\Package' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Package.php',
        'think\\admin\\support\\command\\Publish' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Publish.php',
        'think\\admin\\support\\command\\Queue' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Queue.php',
        'think\\admin\\support\\command\\Replace' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Replace.php',
        'think\\admin\\support\\command\\Sysmenu' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/command/Sysmenu.php',
        'think\\admin\\support\\middleware\\JwtSession' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/middleware/JwtSession.php',
        'think\\admin\\support\\middleware\\MultAccess' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/middleware/MultAccess.php',
        'think\\admin\\support\\middleware\\RbacAccess' => __DIR__ . '/..' . '/zoujingli/think-library/src/support/middleware/RbacAccess.php',
        'think\\cache\\Driver' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/Driver.php',
        'think\\cache\\TagSet' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/TagSet.php',
        'think\\cache\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/File.php',
        'think\\cache\\driver\\Memcache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcache.php',
        'think\\cache\\driver\\Memcached' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Memcached.php',
        'think\\cache\\driver\\Redis' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Redis.php',
        'think\\cache\\driver\\Wincache' => __DIR__ . '/..' . '/topthink/framework/src/think/cache/driver/Wincache.php',
        'think\\console\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Command.php',
        'think\\console\\Input' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Input.php',
        'think\\console\\Output' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Output.php',
        'think\\console\\Table' => __DIR__ . '/..' . '/topthink/framework/src/think/console/Table.php',
        'think\\console\\command\\Clear' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Clear.php',
        'think\\console\\command\\Help' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Help.php',
        'think\\console\\command\\Lists' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Lists.php',
        'think\\console\\command\\Make' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Make.php',
        'think\\console\\command\\RouteList' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RouteList.php',
        'think\\console\\command\\RunServer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/RunServer.php',
        'think\\console\\command\\ServiceDiscover' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/ServiceDiscover.php',
        'think\\console\\command\\VendorPublish' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/VendorPublish.php',
        'think\\console\\command\\Version' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/Version.php',
        'think\\console\\command\\make\\Command' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Command.php',
        'think\\console\\command\\make\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Controller.php',
        'think\\console\\command\\make\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Event.php',
        'think\\console\\command\\make\\Listener' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Listener.php',
        'think\\console\\command\\make\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Middleware.php',
        'think\\console\\command\\make\\Model' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Model.php',
        'think\\console\\command\\make\\Service' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Service.php',
        'think\\console\\command\\make\\Subscribe' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Subscribe.php',
        'think\\console\\command\\make\\Validate' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/make/Validate.php',
        'think\\console\\command\\optimize\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Config.php',
        'think\\console\\command\\optimize\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Route.php',
        'think\\console\\command\\optimize\\Schema' => __DIR__ . '/..' . '/topthink/framework/src/think/console/command/optimize/Schema.php',
        'think\\console\\input\\Argument' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Argument.php',
        'think\\console\\input\\Definition' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Definition.php',
        'think\\console\\input\\Option' => __DIR__ . '/..' . '/topthink/framework/src/think/console/input/Option.php',
        'think\\console\\output\\Ask' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Ask.php',
        'think\\console\\output\\Descriptor' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Descriptor.php',
        'think\\console\\output\\Formatter' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Formatter.php',
        'think\\console\\output\\Question' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/Question.php',
        'think\\console\\output\\descriptor\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/descriptor/Console.php',
        'think\\console\\output\\driver\\Buffer' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Buffer.php',
        'think\\console\\output\\driver\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Console.php',
        'think\\console\\output\\driver\\Nothing' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/driver/Nothing.php',
        'think\\console\\output\\formatter\\Stack' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Stack.php',
        'think\\console\\output\\formatter\\Style' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/formatter/Style.php',
        'think\\console\\output\\question\\Choice' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Choice.php',
        'think\\console\\output\\question\\Confirmation' => __DIR__ . '/..' . '/topthink/framework/src/think/console/output/question/Confirmation.php',
        'think\\contract\\Arrayable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Arrayable.php',
        'think\\contract\\CacheHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/CacheHandlerInterface.php',
        'think\\contract\\Enumable' => __DIR__ . '/..' . '/topthink/think-validate/src/contract/Enumable.php',
        'think\\contract\\Jsonable' => __DIR__ . '/..' . '/topthink/think-helper/src/contract/Jsonable.php',
        'think\\contract\\LogHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/LogHandlerInterface.php',
        'think\\contract\\ModelRelationInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/ModelRelationInterface.php',
        'think\\contract\\SessionHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/SessionHandlerInterface.php',
        'think\\contract\\TemplateHandlerInterface' => __DIR__ . '/..' . '/topthink/framework/src/think/contract/TemplateHandlerInterface.php',
        'think\\db\\BaseBuilder' => __DIR__ . '/..' . '/topthink/think-orm/src/db/BaseBuilder.php',
        'think\\db\\BaseQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/BaseQuery.php',
        'think\\db\\Builder' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Builder.php',
        'think\\db\\CacheItem' => __DIR__ . '/..' . '/topthink/think-orm/src/db/CacheItem.php',
        'think\\db\\Connection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Connection.php',
        'think\\db\\ConnectionInterface' => __DIR__ . '/..' . '/topthink/think-orm/src/db/ConnectionInterface.php',
        'think\\db\\Fetch' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Fetch.php',
        'think\\db\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Mongo.php',
        'think\\db\\PDOConnection' => __DIR__ . '/..' . '/topthink/think-orm/src/db/PDOConnection.php',
        'think\\db\\Query' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Query.php',
        'think\\db\\Raw' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Raw.php',
        'think\\db\\Where' => __DIR__ . '/..' . '/topthink/think-orm/src/db/Where.php',
        'think\\db\\builder\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mongo.php',
        'think\\db\\builder\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Mysql.php',
        'think\\db\\builder\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Oracle.php',
        'think\\db\\builder\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Pgsql.php',
        'think\\db\\builder\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlite.php',
        'think\\db\\builder\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/builder/Sqlsrv.php',
        'think\\db\\concern\\AggregateQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/AggregateQuery.php',
        'think\\db\\concern\\JoinAndViewQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/JoinAndViewQuery.php',
        'think\\db\\concern\\ModelRelationQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ModelRelationQuery.php',
        'think\\db\\concern\\ParamsBind' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ParamsBind.php',
        'think\\db\\concern\\ResultOperation' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/ResultOperation.php',
        'think\\db\\concern\\TableFieldInfo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TableFieldInfo.php',
        'think\\db\\concern\\TimeFieldQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/TimeFieldQuery.php',
        'think\\db\\concern\\Transaction' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/Transaction.php',
        'think\\db\\concern\\WhereQuery' => __DIR__ . '/..' . '/topthink/think-orm/src/db/concern/WhereQuery.php',
        'think\\db\\connector\\Mongo' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mongo.php',
        'think\\db\\connector\\Mysql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Mysql.php',
        'think\\db\\connector\\Oracle' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Oracle.php',
        'think\\db\\connector\\Pgsql' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Pgsql.php',
        'think\\db\\connector\\Sqlite' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlite.php',
        'think\\db\\connector\\Sqlsrv' => __DIR__ . '/..' . '/topthink/think-orm/src/db/connector/Sqlsrv.php',
        'think\\db\\exception\\BindParamException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/BindParamException.php',
        'think\\db\\exception\\DataNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DataNotFoundException.php',
        'think\\db\\exception\\DbEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbEventException.php',
        'think\\db\\exception\\DbException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/DbException.php',
        'think\\db\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/InvalidArgumentException.php',
        'think\\db\\exception\\ModelEventException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelEventException.php',
        'think\\db\\exception\\ModelNotFoundException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/ModelNotFoundException.php',
        'think\\db\\exception\\PDOException' => __DIR__ . '/..' . '/topthink/think-orm/src/db/exception/PDOException.php',
        'think\\event\\AppInit' => __DIR__ . '/..' . '/topthink/framework/src/think/event/AppInit.php',
        'think\\event\\HttpEnd' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpEnd.php',
        'think\\event\\HttpRun' => __DIR__ . '/..' . '/topthink/framework/src/think/event/HttpRun.php',
        'think\\event\\LogRecord' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogRecord.php',
        'think\\event\\LogWrite' => __DIR__ . '/..' . '/topthink/framework/src/think/event/LogWrite.php',
        'think\\event\\RouteLoaded' => __DIR__ . '/..' . '/topthink/framework/src/think/event/RouteLoaded.php',
        'think\\exception\\ClassNotFoundException' => __DIR__ . '/..' . '/topthink/think-container/src/exception/ClassNotFoundException.php',
        'think\\exception\\ErrorException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/ErrorException.php',
        'think\\exception\\FileException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/FileException.php',
        'think\\exception\\FuncNotFoundException' => __DIR__ . '/..' . '/topthink/think-container/src/exception/FuncNotFoundException.php',
        'think\\exception\\Handle' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/Handle.php',
        'think\\exception\\HttpException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpException.php',
        'think\\exception\\HttpResponseException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/HttpResponseException.php',
        'think\\exception\\InvalidArgumentException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/InvalidArgumentException.php',
        'think\\exception\\InvalidCacheException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/InvalidCacheException.php',
        'think\\exception\\RouteNotFoundException' => __DIR__ . '/..' . '/topthink/framework/src/think/exception/RouteNotFoundException.php',
        'think\\exception\\ValidateException' => __DIR__ . '/..' . '/topthink/think-validate/src/exception/ValidateException.php',
        'think\\facade\\App' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/App.php',
        'think\\facade\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cache.php',
        'think\\facade\\Config' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Config.php',
        'think\\facade\\Console' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Console.php',
        'think\\facade\\Cookie' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Cookie.php',
        'think\\facade\\Db' => __DIR__ . '/..' . '/topthink/think-orm/src/facade/Db.php',
        'think\\facade\\Env' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Env.php',
        'think\\facade\\Event' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Event.php',
        'think\\facade\\Lang' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Lang.php',
        'think\\facade\\Log' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Log.php',
        'think\\facade\\Middleware' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Middleware.php',
        'think\\facade\\Request' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Request.php',
        'think\\facade\\Route' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Route.php',
        'think\\facade\\Session' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/Session.php',
        'think\\facade\\Template' => __DIR__ . '/..' . '/topthink/think-template/src/facade/Template.php',
        'think\\facade\\Validate' => __DIR__ . '/..' . '/topthink/think-validate/src/facade/Validate.php',
        'think\\facade\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/facade/View.php',
        'think\\file\\UploadedFile' => __DIR__ . '/..' . '/topthink/framework/src/think/file/UploadedFile.php',
        'think\\helper\\Arr' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Arr.php',
        'think\\helper\\Macroable' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Macroable.php',
        'think\\helper\\Str' => __DIR__ . '/..' . '/topthink/think-helper/src/helper/Str.php',
        'think\\initializer\\BootService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/BootService.php',
        'think\\initializer\\Error' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/Error.php',
        'think\\initializer\\RegisterService' => __DIR__ . '/..' . '/topthink/framework/src/think/initializer/RegisterService.php',
        'think\\log\\Channel' => __DIR__ . '/..' . '/topthink/framework/src/think/log/Channel.php',
        'think\\log\\ChannelSet' => __DIR__ . '/..' . '/topthink/framework/src/think/log/ChannelSet.php',
        'think\\log\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/log/driver/File.php',
        'think\\middleware\\AllowCrossDomain' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/AllowCrossDomain.php',
        'think\\middleware\\CheckRequestCache' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/CheckRequestCache.php',
        'think\\middleware\\FormTokenCheck' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/FormTokenCheck.php',
        'think\\middleware\\LoadLangPack' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/LoadLangPack.php',
        'think\\middleware\\SessionInit' => __DIR__ . '/..' . '/topthink/framework/src/think/middleware/SessionInit.php',
        'think\\migration\\Command' => __DIR__ . '/..' . '/topthink/think-migration/src/Command.php',
        'think\\migration\\Creator' => __DIR__ . '/..' . '/topthink/think-migration/src/Creator.php',
        'think\\migration\\Factory' => __DIR__ . '/..' . '/topthink/think-migration/src/Factory.php',
        'think\\migration\\FactoryBuilder' => __DIR__ . '/..' . '/topthink/think-migration/src/FactoryBuilder.php',
        'think\\migration\\Migrator' => __DIR__ . '/..' . '/topthink/think-migration/src/Migrator.php',
        'think\\migration\\NullOutput' => __DIR__ . '/..' . '/topthink/think-migration/src/NullOutput.php',
        'think\\migration\\Seeder' => __DIR__ . '/..' . '/topthink/think-migration/src/Seeder.php',
        'think\\migration\\Service' => __DIR__ . '/..' . '/topthink/think-migration/src/Service.php',
        'think\\migration\\UsePhinx' => __DIR__ . '/..' . '/topthink/think-migration/src/UsePhinx.php',
        'think\\migration\\command\\Migrate' => __DIR__ . '/..' . '/topthink/think-migration/src/command/Migrate.php',
        'think\\migration\\command\\Seed' => __DIR__ . '/..' . '/topthink/think-migration/src/command/Seed.php',
        'think\\migration\\command\\factory\\Create' => __DIR__ . '/..' . '/topthink/think-migration/src/command/factory/Create.php',
        'think\\migration\\command\\migrate\\Breakpoint' => __DIR__ . '/..' . '/topthink/think-migration/src/command/migrate/Breakpoint.php',
        'think\\migration\\command\\migrate\\Create' => __DIR__ . '/..' . '/topthink/think-migration/src/command/migrate/Create.php',
        'think\\migration\\command\\migrate\\Rollback' => __DIR__ . '/..' . '/topthink/think-migration/src/command/migrate/Rollback.php',
        'think\\migration\\command\\migrate\\Run' => __DIR__ . '/..' . '/topthink/think-migration/src/command/migrate/Run.php',
        'think\\migration\\command\\migrate\\Status' => __DIR__ . '/..' . '/topthink/think-migration/src/command/migrate/Status.php',
        'think\\migration\\command\\seed\\Create' => __DIR__ . '/..' . '/topthink/think-migration/src/command/seed/Create.php',
        'think\\migration\\command\\seed\\Run' => __DIR__ . '/..' . '/topthink/think-migration/src/command/seed/Run.php',
        'think\\migration\\db\\Column' => __DIR__ . '/..' . '/topthink/think-migration/src/db/Column.php',
        'think\\migration\\db\\Table' => __DIR__ . '/..' . '/topthink/think-migration/src/db/Table.php',
        'think\\model\\Collection' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Collection.php',
        'think\\model\\Pivot' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Pivot.php',
        'think\\model\\Relation' => __DIR__ . '/..' . '/topthink/think-orm/src/model/Relation.php',
        'think\\model\\concern\\Attribute' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Attribute.php',
        'think\\model\\concern\\AutoWriteId' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/AutoWriteId.php',
        'think\\model\\concern\\Conversion' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Conversion.php',
        'think\\model\\concern\\ModelEvent' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/ModelEvent.php',
        'think\\model\\concern\\OptimLock' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/OptimLock.php',
        'think\\model\\concern\\RelationShip' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/RelationShip.php',
        'think\\model\\concern\\SoftDelete' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/SoftDelete.php',
        'think\\model\\concern\\TimeStamp' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/TimeStamp.php',
        'think\\model\\concern\\Virtual' => __DIR__ . '/..' . '/topthink/think-orm/src/model/concern/Virtual.php',
        'think\\model\\contract\\EnumTransform' => __DIR__ . '/..' . '/topthink/think-orm/src/model/contract/EnumTransform.php',
        'think\\model\\contract\\FieldTypeTransform' => __DIR__ . '/..' . '/topthink/think-orm/src/model/contract/FieldTypeTransform.php',
        'think\\model\\relation\\BelongsTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsTo.php',
        'think\\model\\relation\\BelongsToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/BelongsToMany.php',
        'think\\model\\relation\\HasMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasMany.php',
        'think\\model\\relation\\HasManyThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasManyThrough.php',
        'think\\model\\relation\\HasOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOne.php',
        'think\\model\\relation\\HasOneThrough' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/HasOneThrough.php',
        'think\\model\\relation\\MorphMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphMany.php',
        'think\\model\\relation\\MorphOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphOne.php',
        'think\\model\\relation\\MorphTo' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphTo.php',
        'think\\model\\relation\\MorphToMany' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/MorphToMany.php',
        'think\\model\\relation\\OneToOne' => __DIR__ . '/..' . '/topthink/think-orm/src/model/relation/OneToOne.php',
        'think\\paginator\\driver\\Bootstrap' => __DIR__ . '/..' . '/topthink/think-orm/src/paginator/driver/Bootstrap.php',
        'think\\response\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/response/File.php',
        'think\\response\\Html' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Html.php',
        'think\\response\\Json' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Json.php',
        'think\\response\\Jsonp' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Jsonp.php',
        'think\\response\\Redirect' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Redirect.php',
        'think\\response\\View' => __DIR__ . '/..' . '/topthink/framework/src/think/response/View.php',
        'think\\response\\Xml' => __DIR__ . '/..' . '/topthink/framework/src/think/response/Xml.php',
        'think\\route\\Dispatch' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Dispatch.php',
        'think\\route\\Domain' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Domain.php',
        'think\\route\\Resource' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Resource.php',
        'think\\route\\ResourceRegister' => __DIR__ . '/..' . '/topthink/framework/src/think/route/ResourceRegister.php',
        'think\\route\\Rule' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Rule.php',
        'think\\route\\RuleGroup' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleGroup.php',
        'think\\route\\RuleItem' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleItem.php',
        'think\\route\\RuleName' => __DIR__ . '/..' . '/topthink/framework/src/think/route/RuleName.php',
        'think\\route\\Url' => __DIR__ . '/..' . '/topthink/framework/src/think/route/Url.php',
        'think\\route\\dispatch\\Callback' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Callback.php',
        'think\\route\\dispatch\\Controller' => __DIR__ . '/..' . '/topthink/framework/src/think/route/dispatch/Controller.php',
        'think\\service\\ModelService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ModelService.php',
        'think\\service\\PaginatorService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/PaginatorService.php',
        'think\\service\\ValidateService' => __DIR__ . '/..' . '/topthink/framework/src/think/service/ValidateService.php',
        'think\\session\\Store' => __DIR__ . '/..' . '/topthink/framework/src/think/session/Store.php',
        'think\\session\\driver\\Cache' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/Cache.php',
        'think\\session\\driver\\File' => __DIR__ . '/..' . '/topthink/framework/src/think/session/driver/File.php',
        'think\\template\\TagLib' => __DIR__ . '/..' . '/topthink/think-template/src/template/TagLib.php',
        'think\\template\\contract\\DriverInterface' => __DIR__ . '/..' . '/topthink/think-template/src/template/contract/DriverInterface.php',
        'think\\template\\driver\\File' => __DIR__ . '/..' . '/topthink/think-template/src/template/driver/File.php',
        'think\\template\\exception\\TemplateNotFoundException' => __DIR__ . '/..' . '/topthink/think-template/src/template/exception/TemplateNotFoundException.php',
        'think\\template\\taglib\\Cx' => __DIR__ . '/..' . '/topthink/think-template/src/template/taglib/Cx.php',
        'think\\validate\\ValidateRule' => __DIR__ . '/..' . '/topthink/think-validate/src/validate/ValidateRule.php',
        'think\\validate\\ValidateRuleSet' => __DIR__ . '/..' . '/topthink/think-validate/src/validate/ValidateRuleSet.php',
        'think\\view\\driver\\Php' => __DIR__ . '/..' . '/topthink/framework/src/think/view/driver/Php.php',
        'think\\view\\driver\\Think' => __DIR__ . '/..' . '/topthink/think-view/src/Think.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$prefixDirsPsr4;
            $loader->fallbackDirsPsr0 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$fallbackDirsPsr0;
            $loader->classMap = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$classMap;

        }, null, ClassLoader::class);
    }
}
