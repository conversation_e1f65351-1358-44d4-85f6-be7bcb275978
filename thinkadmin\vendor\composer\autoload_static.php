<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1
{
    public static $files = array (
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
        '35fab96057f1bf5e7aba31a8a6d5fdde' => __DIR__ . '/..' . '/topthink/think-orm/stubs/load_stubs.php',
        '15ec93fa4ce4b2d53816a1a5f2c514e2' => __DIR__ . '/..' . '/topthink/think-validate/src/helper.php',
        '8dafcc6956460bc297e00381fed53e11' => __DIR__ . '/..' . '/zoujingli/think-library/src/common.php',
        '205331db7aa2d91641859148c9816601' => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src/helper.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\view\\driver\\' => 18,
            'think\\migration\\' => 16,
            'think\\admin\\install\\' => 20,
            'think\\admin\\' => 12,
            'think\\' => 6,
        ),
        'p' => 
        array (
            'plugin\\payment\\' => 15,
            'plugin\\center\\' => 14,
            'plugin\\account\\' => 15,
        ),
        'a' => 
        array (
            'app\\admin\\' => 10,
            'app\\' => 4,
        ),
        'S' => 
        array (
            'Symfony\\Component\\Process\\' => 26,
        ),
        'P' => 
        array (
            'Psr\\SimpleCache\\' => 16,
            'Psr\\Log\\' => 8,
            'Psr\\Http\\Message\\' => 17,
            'Psr\\Container\\' => 14,
            'Phinx\\' => 6,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\view\\driver\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-view/src',
        ),
        'think\\migration\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-migration/src',
        ),
        'think\\admin\\install\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-install/src',
        ),
        'think\\admin\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-library/src',
        ),
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-template/src',
            1 => __DIR__ . '/..' . '/topthink/framework/src/think',
            2 => __DIR__ . '/..' . '/topthink/think-validate/src',
            3 => __DIR__ . '/..' . '/topthink/think-orm/src',
            4 => __DIR__ . '/..' . '/topthink/think-container/src',
            5 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'plugin\\payment\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-payment/src',
        ),
        'plugin\\center\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-center/src',
        ),
        'plugin\\account\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-account/src',
        ),
        'app\\admin\\' => 
        array (
            0 => __DIR__ . '/..' . '/zoujingli/think-plugs-admin/src',
        ),
        'app\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
        'Symfony\\Component\\Process\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/process',
        ),
        'Psr\\SimpleCache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/simple-cache/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Phinx\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-migration/phinx',
        ),
    );

    public static $fallbackDirsPsr0 = array (
        0 => __DIR__ . '/../..' . '/extend',
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Ip2Region' => __DIR__ . '/..' . '/zoujingli/ip2region/Ip2Region.php',
        'XdbSearcher' => __DIR__ . '/..' . '/zoujingli/ip2region/XdbSearcher.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$prefixDirsPsr4;
            $loader->fallbackDirsPsr0 = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$fallbackDirsPsr0;
            $loader->classMap = ComposerStaticInit29d8861b10bb7820a2dd469a547bbbb1::$classMap;

        }, null, ClassLoader::class);
    }
}
