{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">降重记录详情</div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            
            <div class="layui-form-item">
                <label class="layui-form-label">任务标题</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.title|default=''}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.user_id|default=''}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">降重模式</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.rewrite_mode_text|default=$task.rewrite_mode}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">目标相似度</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.target_similarity|default=0}%</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">AI模型</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.aiModel.name|default='未知'}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">任务状态</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {switch name="task.status"}
                        {case value="pending"}<span class="layui-badge">待处理</span>{/case}
                        {case value="processing"}<span class="layui-badge layui-bg-blue">处理中</span>{/case}
                        {case value="completed"}<span class="layui-badge layui-bg-green">已完成</span>{/case}
                        {case value="failed"}<span class="layui-badge layui-bg-red">失败</span>{/case}
                        {case value="cancelled"}<span class="layui-badge layui-bg-gray">已取消</span>{/case}
                        {default /}<span class="layui-badge">{$task.status}</span>
                        {/switch}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">处理进度</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        <div class="layui-progress" lay-showpercent="true" style="width: 200px;">
                            <div class="layui-progress-bar" lay-percent="{$task.progress|default=0}%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">原文字数</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.original_word_count|default=0} 字</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">原始文本</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="white-space: pre-wrap; background: #f8f8f8; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
{$task.original_text|default=''}
                    </div>
                </div>
            </div>
            
            {if !empty($task.error_message)}
            <div class="layui-form-item">
                <label class="layui-form-label">错误信息</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="color: #ff5722;">{$task.error_message}</div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.create_time|format_datetime}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">更新时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$task.update_time|format_datetime}</div>
                </div>
            </div>
            
        </div>
        
        {if !empty($results)}
        <div class="hr-line-dashed"></div>
        <h4>降重结果列表</h4>
        <table class="layui-table" lay-skin="line">
            <thead>
            <tr>
                <th>AI模型</th>
                <th>降重后字数</th>
                <th>相似度分数</th>
                <th>质量分数</th>
                <th>处理时间</th>
                <th>是否选中</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            {foreach $results as $result}
            <tr>
                <td>{$result.aiModel.name|default='未知'}</td>
                <td>{$result.rewritten_word_count|default=0} 字</td>
                <td>{$result.similarity_score|default='-'}%</td>
                <td>{$result.quality_score|default='-'}%</td>
                <td>{$result.processing_time|default=0} 秒</td>
                <td>
                    {if $result.is_selected eq 1}
                    <span class="layui-badge layui-bg-green">是</span>
                    {else}
                    <span class="layui-badge">否</span>
                    {/if}
                </td>
                <td>{$result.create_time|format_datetime}</td>
                <td>
                    {if auth("compare")}
                    <a class='layui-btn layui-btn-sm layui-btn-normal' data-modal='{:url("compare")}?task_id={$task.id}&result_id={$result.id}' data-title="对比查看">对比</a>
                    {/if}
                </td>
            </tr>
            {/foreach}
            </tbody>
        </table>
        {/if}
        
        <div class="hr-line-dashed"></div>
        
        <div class="layui-form-item text-center">
            <button class="layui-btn layui-btn-danger" type='button' data-close>关闭窗口</button>
        </div>
        
    </div>
</div>
{/block}
