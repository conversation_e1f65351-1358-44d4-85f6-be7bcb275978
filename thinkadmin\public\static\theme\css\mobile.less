@charset "UTF-8";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

::-webkit-scrollbar {
  width: 3px;
  height: 3px
}

::-webkit-scrollbar-track {
  background: #ccc !important
}

::-webkit-scrollbar-thumb {
  background-color: #666 !important
}

* {
  margin: 0;
  padding: 0;
  outline: 0;
  box-sizing: content-box
}

body, html {
  font-size: 20px
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%
}

body {
  margin: 0
}

main {
  display: block
}

h1 {
  margin: .67em 0;
  font-size: 2em
}

hr {
  overflow: visible;
  box-sizing: content-box;
  height: 0
}

pre {
  font-size: 1em;
  font-family: monospace
}

a {
  background-color: transparent
}

abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted
}

b, strong {
  font-weight: bolder
}

code, kbd, samp {
  font-size: 1em;
  font-family: monospace
}

small {
  font-size: 80%
}

sub, sup {
  position: relative;
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0
}

sub {
  bottom: -.25em
}

sup {
  top: -.5em
}

img {
  border-style: none
}

button, input, optgroup, select, textarea {
  margin: 0;
  font-size: 100%;
  font-family: inherit;
  line-height: 1.15
}

button, input {
  overflow: visible
}

button, select {
  text-transform: none
}

[type=button], [type=reset], [type=submit], button {
  -webkit-appearance: button
}

[type=button]::-moz-focus-inner, [type=reset]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {
  padding: 0;
  border-style: none
}

[type=button]:-moz-focusring, [type=reset]:-moz-focusring, [type=submit]:-moz-focusring, button:-moz-focusring {
  outline: 1px dotted ButtonText
}

fieldset {
  padding: .35em .75em .625em
}

legend {
  display: table;
  box-sizing: border-box;
  padding: 0;
  max-width: 100%;
  color: inherit;
  white-space: normal
}

progress {
  vertical-align: baseline
}

textarea {
  overflow: auto
}

[type=checkbox], [type=radio] {
  box-sizing: border-box;
  padding: 0
}

[type=number]::-webkit-inner-spin-button, [type=number]::-webkit-outer-spin-button {
  height: auto
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield
}

[type=search]::-webkit-search-decoration {
  -webkit-appearance: none
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button
}

details {
  display: block
}

summary {
  display: list-item
}

[hidden], template {
  display: none
}

a {
  text-decoration: none
}

@media only screen and (min-width: 320px) and (min-height: 480px) {
  html {
    font-size: 20px !important
  }
}

@media only screen and (min-width: 320px) and (min-height: 568px) {
  html {
    font-size: 20px !important
  }
}

@media only screen and (min-width: 360px) and (min-height: 640px) {
  html {
    font-size: 22.5px !important
  }
}

@media only screen and (min-width: 375px) and (min-height: 667px) {
  html {
    font-size: 23.5px !important
  }
}

@media only screen and (min-width: 414px) and (min-height: 736px) {
  html {
    font-size: 25.9px !important
  }
}

@media only screen and (min-width: 736px) {
  html {
    font-size: 46px !important
  }
}

.header {
  margin-top: .4rem;
  text-align: center;

  span {
    padding: .1rem .2rem;
    border-radius: .5rem;
    background: #999;
    color: #fff;
    font-size: .6rem
  }
}


.container {
  position: relative;
  display: block;
  margin: 5vw auto;
  width: 90%;
  color: #333;
  text-overflow: ellipsis;

  .logo {
    position: absolute;
    top: 0;
    display: inline-block;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: #098;
    color: #fff;
    content: 'A';
    text-align: center;
    font-size: .8rem;
    line-height: 2.5rem
  }

  .content {
    position: relative;
    left: 3.4rem;
    display: inline-block;
    padding: .8rem .8rem .8rem;
    max-width: 54vw;
    border: 1px solid #ccc;
    border-radius: .25rem;
    background: #fff;
    font-size: .7rem;
    line-height: 1.5em
  }

  .arrow:after, .arrow:before {
    position: absolute;
    top: .8rem;
    left: -.4rem;
    width: 0;
    height: 0;
    border-color: transparent #ccc transparent transparent;
    border-style: solid;
    border-width: .4rem .4rem .4rem 0;
    content: ' '
  }

  .arrow:after {
    margin-left: 1px;
    border-color: transparent #fff transparent transparent
  }
}

@font-face {
  font-family: "iconfont";
  src: url('./icon/iconfont.eot?t=1543820027953'); /* IE9 */
  src: url('./icon/iconfont.eot?t=1543820027953#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff'),
  url('./icon/iconfont.ttf?t=1543820027953') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/ url('./icon/iconfont.svg?t=1543820027953#iconfont') format('svg'); /* iOS 4.1- */
}

.icon {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &-play:before {
    content: "\e650"
  }

  &-pause:before {
    content: "\e629"
  }

  &-volume:before {
    content: "\e87a"
  }
}
