{extend name='table'}

{block name="button"}
<!--{if auth("add")}-->
<button data-table-id="AiModelTable" data-modal='{:url("add")}' class='layui-btn layui-btn-sm layui-btn-primary'>添加AI模型</button>
<!--{/if}-->

<!--{if auth("batchHealthCheck")}-->
<button data-action='{:url("batchHealthCheck")}' data-confirm="确定要批量检测所有模型健康状态吗？" class='layui-btn layui-btn-sm layui-btn-warm'>批量健康检测</button>
<!--{/if}-->

<!--{if auth("remove")}-->
<button data-table-id="AiModelTable" data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除AI模型吗？" class='layui-btn layui-btn-sm layui-btn-primary'>批量删除</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">模型名称</label>
                <div class="layui-input-inline">
                    <input name="name" value="{$get.name|default=''}" placeholder="请输入模型名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">提供商</label>
                <div class="layui-input-inline">
                    <select name="provider" class="layui-select">
                        <option value="">全部提供商</option>
                        <option value="openai" {if isset($get.provider) and $get.provider eq 'openai'}selected{/if}>OpenAI</option>
                        <option value="baidu" {if isset($get.provider) and $get.provider eq 'baidu'}selected{/if}>百度</option>
                        <option value="aliyun" {if isset($get.provider) and $get.provider eq 'aliyun'}selected{/if}>阿里云</option>
                        <option value="tencent" {if isset($get.provider) and $get.provider eq 'tencent'}selected{/if}>腾讯云</option>
                        <option value="zhipu" {if isset($get.provider) and $get.provider eq 'zhipu'}selected{/if}>智谱AI</option>
                        <option value="moonshot" {if isset($get.provider) and $get.provider eq 'moonshot'}selected{/if}>Moonshot</option>
                        <option value="other" {if isset($get.provider) and $get.provider eq 'other'}selected{/if}>其他</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">健康状态</label>
                <div class="layui-input-inline">
                    <select name="health_status" class="layui-select">
                        <option value="">全部状态</option>
                        <option value="1" {if isset($get.health_status) and $get.health_status eq '1'}selected{/if}>健康</option>
                        <option value="0" {if isset($get.health_status) and $get.health_status eq '0'}selected{/if}>异常</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit">搜 索</button>
            </div>
        </form>
        <table id="AiModelTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
    </div>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 初始化表格组件
        $('#AiModelTable').layTable({
            even: true, height: 'full',
            sort: {field: 'priority desc,id', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'priority', title: '优先级', width: 80, align: 'center', sort: true, templet: '#PriorityInputTpl'},
                {field: 'name', title: '模型名称', width: '15%', align: 'left'},
                {field: 'provider_text', title: '提供商', width: 100, align: 'center'},
                {field: 'model_code', title: '模型代码', width: '15%', align: 'center'},
                {field: 'max_tokens', title: '最大Token', width: 100, align: 'center'},
                {field: 'cost_text', title: '成本', width: 120, align: 'center'},
                {field: 'health_status', title: '健康状态', width: 100, align: 'center', templet: '#HealthStatusTpl'},
                {field: 'status', title: '状态', width: 80, align: 'center', templet: '#StatusSwitchTpl'},
                {field: 'last_health_check', title: '最后检测', width: 170, align: 'center'},
                {toolbar: '#toolbar', align: 'center', width: 180, title: '操作', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#AiModelTable').trigger('reload');
                });
                return false;
            }, false);
        });
    });
</script>

<!-- 优先级输入模板 -->
<script type="text/html" id="PriorityInputTpl">
    <input type="number" min="0" data-blur-number="0" data-action-blur="{:sysuri()}" data-value="id#{{d.id}};action#priority;priority#{value}" data-loading="false" value="{{d.priority}}" class="layui-input text-center">
</script>

<!-- 健康状态显示模板 -->
<script type="text/html" id="HealthStatusTpl">
    {{# if(d.health_status == 1) { }}
        <span class="layui-badge layui-bg-green">健康</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-red">异常</span>
    {{# } }}
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchTpl">
    <!--{if auth("state")}-->
    <input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="StatusSwitch" {{-d.status>0?'checked':''}}>
    <!--{else}-->
    {{-d.status ? '<b class="color-green">启用</b>' : '<b class="color-red">禁用</b>'}}
    <!--{/if}-->
</script>

<!-- 数据操作工具条模板 -->
<script type="text/html" id="toolbar">
    <!--{if auth('edit')}-->
    <a class="layui-btn layui-btn-primary layui-btn-sm" data-event-dbclick data-title="编辑模型" data-modal='{:url("edit")}?id={{d.id}}'>编辑</a>
    <!--{/if}-->
    
    <!--{if auth('healthCheck')}-->
    <a class="layui-btn layui-btn-sm" data-confirm="确定要检测该模型健康状态吗？" data-action="{:url('healthCheck')}" data-value="id#{{d.id}}">健康检测</a>
    <!--{/if}-->
    
    <!--{if auth('testConnection')}-->
    <a class="layui-btn layui-btn-warm layui-btn-sm" data-confirm="确定要测试连接吗？" data-action="{:url('testConnection')}" data-value="id#{{d.id}}">测试连接</a>
    <!--{/if}-->

    <!--{if auth("remove")}-->
    <a class="layui-btn layui-btn-danger layui-btn-sm" data-confirm="确定要删除该模型吗?" data-action="{:url('remove')}" data-value="id#{{d.id}}">删除</a>
    <!--{/if}-->
</script>
{/block}
