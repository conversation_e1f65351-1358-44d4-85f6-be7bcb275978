<?php

/**
 * 添加论文类型数据
 */

echo "=== 添加论文类型数据 ===\n\n";

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 检查现有数据
    $stmt = $pdo->query("SELECT id, name FROM paper_type WHERE status = 1");
    $existing = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "现有论文类型:\n";
    foreach ($existing as $type) {
        echo "  - ID: {$type['id']}, 名称: {$type['name']}\n";
    }
    
    // 添加新的论文类型
    $newTypes = [
        ['name' => '学术论文', 'description' => '学术研究论文', 'sort' => 100],
        ['name' => '毕业论文', 'description' => '本科/研究生毕业论文', 'sort' => 200],
        ['name' => '期刊论文', 'description' => '期刊发表论文', 'sort' => 300],
        ['name' => '会议论文', 'description' => '学术会议论文', 'sort' => 400],
        ['name' => '综述论文', 'description' => '文献综述论文', 'sort' => 500]
    ];
    
    echo "\n添加新的论文类型...\n";
    
    $stmt = $pdo->prepare("
        INSERT OR IGNORE INTO paper_type (name, description, word_count_min, word_count_max, sort, status, create_time)
        VALUES (:name, :description, 3000, 10000, :sort, 1, datetime('now'))
    ");
    
    $added = 0;
    foreach ($newTypes as $type) {
        // 检查是否已存在
        $checkStmt = $pdo->prepare("SELECT COUNT(*) as count FROM paper_type WHERE name = :name");
        $checkStmt->execute(['name' => $type['name']]);
        $exists = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;
        
        if (!$exists) {
            $stmt->execute($type);
            echo "  ✅ 添加: {$type['name']}\n";
            $added++;
        } else {
            echo "  ⚠️  已存在: {$type['name']}\n";
        }
    }
    
    echo "\n✅ 成功添加 {$added} 个论文类型\n";
    
    // 验证结果
    echo "\n最终论文类型列表:\n";
    $stmt = $pdo->query("SELECT id, name, description, sort FROM paper_type WHERE status = 1 ORDER BY sort");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "  - ID: {$row['id']}, 名称: {$row['name']}, 描述: {$row['description']}, 排序: {$row['sort']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 添加完成 ===\n";
