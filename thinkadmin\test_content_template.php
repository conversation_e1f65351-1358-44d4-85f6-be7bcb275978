<?php

/**
 * 测试正文模板控制器
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use app\admin\model\DocumentTemplate;
use app\admin\model\PaperType;

try {
    echo "=== 测试正文模板数据 ===\n\n";
    
    // 初始化应用
    $app = new App();
    $app->initialize();
    
    // 测试模型查询
    echo "1. 测试DocumentTemplate模型:\n";
    $templates = DocumentTemplate::where(['type' => 'content'])->limit(5)->select();
    echo "找到 " . count($templates) . " 个正文模板\n";
    
    if (count($templates) > 0) {
        foreach ($templates as $template) {
            echo "  - ID: {$template->id}, 名称: {$template->name}\n";
        }
    } else {
        echo "  没有找到正文模板数据\n";
    }
    
    echo "\n2. 测试PaperType模型:\n";
    $paperTypes = PaperType::where(['status' => 1])->column('name', 'id');
    echo "找到 " . count($paperTypes) . " 个论文类型\n";
    foreach ($paperTypes as $id => $name) {
        echo "  - ID: {$id}, 名称: {$name}\n";
    }
    
    echo "\n3. 测试模板类型选项:\n";
    $typeOptions = DocumentTemplate::getTypeOptions();
    foreach ($typeOptions as $key => $value) {
        echo "  - {$key}: {$value}\n";
    }
    
    echo "\n4. 测试查询构建:\n";
    $query = DocumentTemplate::where(['type' => 'content']);
    echo "基础查询: " . $query->buildSql() . "\n";
    
    echo "\n✅ 所有测试通过！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
