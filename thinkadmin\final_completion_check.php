<?php

/**
 * 最终完成度检查脚本
 * 检查所有menu.md中定义的模块是否都已实现
 */

// 定义menu.md中的所有模块及其对应的控制器
$menuModules = [
    // 写作中心
    '写作中心' => [
        '论文类型管理' => 'PaperType.php',
        '大纲模板管理' => 'OutlineTemplate.php', 
        '提示词模板管理' => 'PromptTemplate.php',
        '正文模板管理' => 'ContentTemplate.php',
        '写作任务管理' => 'PaperProject.php',
        '草稿箱管理' => 'PaperProject.php'
    ],
    
    // 降重与查重
    '降重与查重' => [
        '降重记录管理' => 'RewriteRecord.php',
        '降重模型配置' => 'RewriteModel.php',
        '查重记录管理' => 'CheckRecord.php',
        '降重任务管理' => 'RewriteTask.php'
    ],
    
    // 文档导出
    '文档导出' => [
        '导出样式模板' => 'ExportTemplate.php',
        '导出任务监控' => 'ExportMonitor.php',
        '发票管理' => 'Invoice.php'
    ],
    
    // 用户中心
    '用户中心' => [
        '用户列表' => 'User.php',
        'VIP套餐管理' => 'VipPackage.php',
        '用户积分管理' => 'UserPoints.php'
    ],
    
    // 收费系统
    '收费系统' => [
        '订单管理' => 'Order.php',
        '套餐配置' => 'PackageConfig.php',
        '充值记录' => 'RechargeRecord.php'
    ],
    
    // 通知与消息
    '通知与消息' => [
        '系统通知记录' => 'SystemNotice.php',
        '消息模板管理' => 'MessageTemplate.php',
        '邮件配置' => 'EmailConfig.php',
        '通知记录' => 'NotificationLog.php'
    ],
    
    // 系统设置
    '系统设置' => [
        'AI模型配置' => 'AiModel.php',
        'n8n工作流管理' => 'N8nWorkflow.php',
        '接口密钥管理' => 'ApiKey.php',
        'Webhook配置' => 'WebhookConfig.php',
        '内容风控规则' => 'ContentFilter.php',
        '基础参数设置' => 'BasicConfig.php'
    ]
];

// 控制器目录
$controllerDir = __DIR__ . '/app/admin/controller/';

echo "=== 最终完成度检查 ===\n\n";

$totalModules = 0;
$completedModules = 0;
$missingModules = [];

foreach ($menuModules as $section => $modules) {
    echo "## {$section}\n";
    
    foreach ($modules as $moduleName => $controllerFile) {
        $totalModules++;
        $filePath = $controllerDir . $controllerFile;
        
        if (file_exists($filePath)) {
            echo "✅ {$moduleName} -> {$controllerFile} (已实现)\n";
            $completedModules++;
        } else {
            echo "❌ {$moduleName} -> {$controllerFile} (缺失)\n";
            $missingModules[] = [
                'section' => $section,
                'module' => $moduleName,
                'controller' => $controllerFile
            ];
        }
    }
    echo "\n";
}

// 统计结果
$completionRate = round(($completedModules / $totalModules) * 100, 2);

echo "📊 完成度统计：\n";
echo "- 总模块数：{$totalModules}\n";
echo "- 已完成：{$completedModules}\n";
echo "- 缺失：" . count($missingModules) . "\n";
echo "- 完成率：{$completionRate}%\n\n";

if (empty($missingModules)) {
    echo "🎉 恭喜！所有模块都已实现完成！\n";
} else {
    echo "⚠️ 仍有以下模块需要实现：\n";
    foreach ($missingModules as $missing) {
        echo "- [{$missing['section']}] {$missing['module']} -> {$missing['controller']}\n";
    }
}

echo "\n=== 检查完成 ===\n";
