<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\User;
use app\admin\model\NotificationLog as NotificationLogModel;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 通知记录
 * @class NotificationLog
 * @package app\admin\controller
 */
class NotificationLog extends Controller
{
    /**
     * 通知记录
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        NotificationLogModel::mQuery()->layTable(function () {
            $this->title = '通知记录';
            $this->typeOptions = NotificationLogModel::getTypeOptions();
            $this->statusOptions = NotificationLogModel::getStatusOptions();
            $this->channelOptions = NotificationLogModel::getChannelOptions();
        }, static function (QueryHelper $query) {
            $query->with(['user']);
            $query->like('title,content,user.username,user.nickname')->equal('type,status,channel');
            $query->dateBetween('create_time,send_time,read_time');
            $query->order('id desc');
        });
    }

    /**
     * 通知详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notification = NotificationLogModel::mk()->with(['user'])->findOrEmpty($id);
        if ($notification->isEmpty()) {
            $this->error('通知不存在！');
        }

        // 获取发送详情
        $sendDetails = $this->getSendDetails($notification);
        
        // 获取相关操作日志
        $operationLogs = $this->getOperationLogs($notification->id);

        $this->assign('notification', $notification);
        $this->assign('sendDetails', $sendDetails);
        $this->assign('operationLogs', $operationLogs);
        $this->assign('title', '通知详情');
        
        return $this->fetch('notification_log/view');
    }

    /**
     * 重新发送通知
     * @auth true
     */
    public function resend()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notification = NotificationLogModel::mk()->with(['user'])->findOrEmpty($id);
        if ($notification->isEmpty()) {
            $this->error('通知不存在！');
        }

        if ($notification->status === 'success') {
            $this->error('已成功发送的通知不需要重新发送！');
        }

        try {
            // 模拟重新发送
            $sendResult = $this->performNotificationSend($notification);
            
            if ($sendResult['success']) {
                $notification->save([
                    'status' => 'success',
                    'send_time' => date('Y-m-d H:i:s'),
                    'error_message' => '',
                    'retry_count' => ($notification->retry_count ?: 0) + 1,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
                $this->addOperationLog($notification->id, 'resend', '管理员重新发送通知');
                $this->success('通知重新发送成功！');
            } else {
                $notification->save([
                    'status' => 'failed',
                    'error_message' => $sendResult['error'],
                    'retry_count' => ($notification->retry_count ?: 0) + 1,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                
                $this->error('通知重新发送失败：' . $sendResult['error']);
            }
        } catch (\Exception $e) {
            $this->error('重新发送异常：' . $e->getMessage());
        }
    }

    /**
     * 执行通知发送
     * @param NotificationLogModel $notification
     * @return array
     */
    private function performNotificationSend(NotificationLogModel $notification): array
    {
        try {
            // 模拟发送过程
            usleep(rand(500000, 2000000)); // 500ms-2s
            
            // 根据不同渠道模拟发送结果
            switch ($notification->channel) {
                case 'email':
                    $success = rand(1, 10) > 2; // 80%成功率
                    $error = $success ? '' : '邮件服务器连接失败';
                    break;
                    
                case 'sms':
                    $success = rand(1, 10) > 3; // 70%成功率
                    $error = $success ? '' : '短信发送失败，余额不足';
                    break;
                    
                case 'push':
                    $success = rand(1, 10) > 1; // 90%成功率
                    $error = $success ? '' : '推送服务异常';
                    break;
                    
                case 'wechat':
                    $success = rand(1, 10) > 2; // 80%成功率
                    $error = $success ? '' : '微信接口调用失败';
                    break;
                    
                default:
                    $success = rand(1, 10) > 3; // 70%成功率
                    $error = $success ? '' : '未知渠道发送失败';
                    break;
            }
            
            return [
                'success' => $success,
                'error' => $error
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 删除通知记录
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notification = NotificationLogModel::mk()->findOrEmpty($id);
        if ($notification->isEmpty()) {
            $this->error('通知不存在！');
        }

        if ($notification->delete()) {
            $this->success('通知记录删除成功！');
        } else {
            $this->error('通知记录删除失败！');
        }
    }

    /**
     * 标记为已读
     * @auth true
     */
    public function markRead()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notification = NotificationLogModel::mk()->findOrEmpty($id);
        if ($notification->isEmpty()) {
            $this->error('通知不存在！');
        }

        if ($notification->is_read) {
            $this->error('通知已经是已读状态！');
        }

        $result = $notification->save([
            'is_read' => 1,
            'read_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $this->addOperationLog($notification->id, 'mark_read', '管理员标记为已读');
            $this->success('标记已读成功！');
        } else {
            $this->error('标记已读失败！');
        }
    }

    /**
     * 通知统计
     * @auth true
     */
    public function statistics()
    {
        // 总通知数
        $totalNotifications = NotificationLogModel::mk()->count();
        
        // 今日通知数
        $todayNotifications = NotificationLogModel::mk()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月通知数
        $monthNotifications = NotificationLogModel::mk()
            ->whereTime('create_time', 'month')
            ->count();
        
        // 各状态统计
        $statusStats = NotificationLogModel::mk()
            ->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();
        
        // 各渠道统计
        $channelStats = NotificationLogModel::mk()
            ->field('channel, COUNT(*) as count')
            ->group('channel')
            ->select()
            ->toArray();
        
        // 各类型统计
        $typeStats = NotificationLogModel::mk()
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 成功率统计
        $successCount = NotificationLogModel::mk()->where('status', 'success')->count();
        $successRate = $totalNotifications > 0 ? round(($successCount / $totalNotifications) * 100, 2) : 0;
        
        // 阅读率统计
        $readCount = NotificationLogModel::mk()->where('is_read', 1)->count();
        $readRate = $totalNotifications > 0 ? round(($readCount / $totalNotifications) * 100, 2) : 0;
        
        // 最近7天通知趋势
        $weekTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $total = NotificationLogModel::mk()
                ->whereTime('create_time', $date)
                ->count();
            $success = NotificationLogModel::mk()
                ->whereTime('create_time', $date)
                ->where('status', 'success')
                ->count();
            
            $weekTrend[] = [
                'date' => $date,
                'total' => $total,
                'success' => $success
            ];
        }
        
        // 用户通知排行（前10）
        $userStats = NotificationLogModel::mk()
            ->with(['user'])
            ->field('user_id, COUNT(*) as notification_count')
            ->where('user_id', '>', 0)
            ->group('user_id')
            ->order('notification_count desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 失败原因统计
        $errorStats = NotificationLogModel::mk()
            ->where('status', 'failed')
            ->whereNotNull('error_message')
            ->field('error_message, COUNT(*) as count')
            ->group('error_message')
            ->order('count desc')
            ->limit(10)
            ->select()
            ->toArray();

        $statistics = [
            'total_notifications' => $totalNotifications,
            'today_notifications' => $todayNotifications,
            'month_notifications' => $monthNotifications,
            'success_rate' => $successRate,
            'read_rate' => $readRate,
            'status_stats' => $statusStats,
            'channel_stats' => $channelStats,
            'type_stats' => $typeStats,
            'week_trend' => $weekTrend,
            'user_stats' => $userStats,
            'error_stats' => $errorStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '通知统计');
        return $this->fetch('notification_log/statistics');
    }

    /**
     * 获取发送详情
     * @param NotificationLogModel $notification
     * @return array
     */
    private function getSendDetails(NotificationLogModel $notification): array
    {
        return [
            'channel' => $notification->channel,
            'type' => $notification->type,
            'status' => $notification->status,
            'send_time' => $notification->send_time,
            'read_time' => $notification->read_time,
            'retry_count' => $notification->retry_count ?: 0,
            'error_message' => $notification->error_message,
            'extra_data' => $notification->extra_data ? json_decode($notification->extra_data, true) : []
        ];
    }

    /**
     * 获取操作日志
     * @param int $notificationId
     * @return array
     */
    private function getOperationLogs(int $notificationId): array
    {
        // 这里应该从实际的日志表中获取数据
        // 暂时返回模拟数据
        return [
            [
                'time' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'action' => 'create',
                'description' => '创建通知记录',
                'operator' => '系统',
                'remark' => ''
            ],
            [
                'time' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'action' => 'send',
                'description' => '发送通知',
                'operator' => '系统',
                'remark' => ''
            ]
        ];
    }

    /**
     * 添加操作日志
     * @param int $notificationId
     * @param string $action
     * @param string $description
     * @param string $remark
     */
    private function addOperationLog(int $notificationId, string $action, string $description, string $remark = ''): void
    {
        // 这里应该写入到实际的日志表
        $logData = [
            'notification_id' => $notificationId,
            'action' => $action,
            'description' => $description,
            'operator_id' => session('user.id'),
            'operator_name' => session('user.username'),
            'remark' => $remark,
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 实际项目中应该保存到数据库
        // NotificationOperationLog::mk()->save($logData);
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $notification = NotificationLogModel::mk()->findOrEmpty($id);
            if ($notification->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'resend':
                    if ($notification->status !== 'success') {
                        try {
                            $sendResult = $this->performNotificationSend($notification);
                            if ($sendResult['success']) {
                                $notification->save([
                                    'status' => 'success',
                                    'send_time' => date('Y-m-d H:i:s'),
                                    'error_message' => '',
                                    'retry_count' => ($notification->retry_count ?: 0) + 1
                                ]);
                                $successCount++;
                            } else {
                                $notification->save([
                                    'status' => 'failed',
                                    'error_message' => $sendResult['error'],
                                    'retry_count' => ($notification->retry_count ?: 0) + 1
                                ]);
                                $failCount++;
                            }
                        } catch (\Exception $e) {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'mark_read':
                    if (!$notification->is_read) {
                        $result = $notification->save([
                            'is_read' => 1,
                            'read_time' => date('Y-m-d H:i:s')
                        ]);
                        if ($result) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'delete':
                    if ($notification->delete()) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                    break;

                default:
                    $failCount++;
                    break;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 清理过期通知
     * @auth true
     */
    public function cleanup()
    {
        $days = $this->request->post('days', 30);
        
        if ($days < 1) {
            $this->error('清理天数必须大于0！');
        }

        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // 只清理已读的通知
        $count = NotificationLogModel::mk()
            ->where('is_read', 1)
            ->where('create_time', '<', $expireDate)
            ->delete();

        $this->success("清理完成！共清理了 {$count} 条过期通知。");
    }

    /**
     * 导出通知记录
     * @auth true
     */
    public function export()
    {
        $startDate = $this->request->post('start_date', '');
        $endDate = $this->request->post('end_date', '');
        $channel = $this->request->post('channel', '');
        $status = $this->request->post('status', '');

        $query = NotificationLogModel::mk()->with(['user']);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }

        if (!empty($channel)) {
            $query->where('channel', $channel);
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        $notifications = $query->order('id desc')->select();

        if ($notifications->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '接收用户', '通知类型', '发送渠道', '标题', '状态', '是否已读', '发送时间', '阅读时间'];
        
        foreach ($notifications as $notification) {
            $exportData[] = [
                $notification->id,
                $notification->user->username ?? '',
                NotificationLogModel::getTypeOptions()[$notification->type] ?? $notification->type,
                NotificationLogModel::getChannelOptions()[$notification->channel] ?? $notification->channel,
                $notification->title,
                NotificationLogModel::getStatusOptions()[$notification->status] ?? $notification->status,
                $notification->is_read ? '是' : '否',
                $notification->send_time ?: '',
                $notification->read_time ?: ''
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 实时监控
     * @auth true
     */
    public function monitor()
    {
        // 获取最近的通知记录
        $recentNotifications = NotificationLogModel::mk()
            ->with(['user'])
            ->order('id desc')
            ->limit(20)
            ->select();
        
        // 获取发送中的通知
        $sendingNotifications = NotificationLogModel::mk()
            ->with(['user'])
            ->where('status', 'sending')
            ->order('create_time asc')
            ->select();
        
        // 系统状态（模拟数据）
        $systemStatus = [
            'email_queue' => rand(0, 50),
            'sms_queue' => rand(0, 20),
            'push_queue' => rand(0, 100),
            'wechat_queue' => rand(0, 30),
            'total_today' => NotificationLogModel::mk()->whereTime('create_time', 'today')->count(),
            'success_today' => NotificationLogModel::mk()->whereTime('create_time', 'today')->where('status', 'success')->count()
        ];

        if ($this->request->isAjax()) {
            return json([
                'recent_notifications' => $recentNotifications->toArray(),
                'sending_notifications' => $sendingNotifications->toArray(),
                'system_status' => $systemStatus
            ]);
        }

        $this->assign('recentNotifications', $recentNotifications);
        $this->assign('sendingNotifications', $sendingNotifications);
        $this->assign('systemStatus', $systemStatus);
        $this->assign('title', '通知监控');
        
        return $this->fetch('notification_log/monitor');
    }
}
