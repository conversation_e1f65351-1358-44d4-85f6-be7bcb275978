<?php

namespace app\admin\controller;

use app\admin\model\RewriteModelModel;
use think\admin\Controller;

/**
 * 降重模型管理
 * @class RewriteModel
 * @package app\admin\controller
 */
class RewriteModel extends Controller
{
    /**
     * 降重模型管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        RewriteModelModel::mQuery($this)->layPage(function () {
            $this->title = '降重模型管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加降重模型管理
     * @auth true
     */
    public function add()
    {
        RewriteModelModel::mForm('rewrite_model/form');
    }

    /**
     * 编辑降重模型管理
     * @auth true
     */
    public function edit()
    {
        RewriteModelModel::mForm('rewrite_model/form');
    }

    /**
     * 删除降重模型管理
     * @auth true
     */
    public function remove()
    {
        RewriteModelModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        RewriteModelModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}