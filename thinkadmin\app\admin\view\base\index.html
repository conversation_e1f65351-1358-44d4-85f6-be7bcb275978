{extend name='table'}

{block name="button"}
<!--{if auth("add")}-->
<button data-table-id="BaseTable" data-modal='{:url("add")}?type={$type|default=""}' class='layui-btn layui-btn-sm layui-btn-primary'>添加数据</button>
<!--{/if}-->

<!--{if auth("remove")}-->
<button data-table-id="BaseTable" data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除数据吗？" class='layui-btn layui-btn-sm layui-btn-primary'>批量删除</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="layui-tab layui-tab-card">
    <ul class="layui-tab-title">
        {foreach $types as $t}{if isset($type) and $type eq $t}
        <li class="layui-this" data-open="{:sysuri()}?type={$t}">{$t}</li>
        {else}
        <li data-open="{:sysuri()}?type={$t}">{$t}</li>
        {/if}{/foreach}
    </ul>
    <div class="layui-tab-content">
        {include file='base/index_search'}
        <table id="BaseTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
    </div>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 初始化表格组件
        $('#BaseTable').layTable({
            even: true, height: 'full',
            sort: {field: 'sort desc,id', type: 'asc'},
            where: {type: '{$type|default=""}'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'sort', title: '{:lang("排序权重")}', width: 100, align: 'center', sort: true, templet: '#SortInputTpl'},
                // {field: 'type', title: '数据类型', minWidth: 140, align: 'center'},
                {field: 'code', title: '数据编码', width: '20%', align: 'left'},
                {field: 'name', title: '数据名称', width: '30%', align: 'left'},
                {field: 'status', title: '数据状态', minWidth: 110, align: 'center', templet: '#StatusSwitchTpl'},
                {field: 'create_at', title: '创建时间', minWidth: 170, align: 'center', sort: true},
                {toolbar: '#toolbar', align: 'center', minWidth: 150, title: '数据操作', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#BaseTable').trigger('reload');
                });
                return false;
            }, false);
        });
    });
</script>

<!-- 列表排序权重模板 -->
<script type="text/html" id="SortInputTpl">
    <input type="number" min="0" data-blur-number="0" data-action-blur="{:sysuri()}" data-value="id#{{d.id}};action#sort;sort#{value}" data-loading="false" value="{{d.sort}}" class="layui-input text-center">
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchTpl">
    <!--{if auth("state")}-->
    <input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="已激活|已禁用" lay-filter="StatusSwitch" {{-d.status>0?'checked':''}}>
    <!--{else}-->
    {{-d.status ? '<b class="color-green">已启用</b>' : '<b class="color-red">已禁用</b>'}}
    <!--{/if}-->
</script>

<!-- 数据操作工具条模板 -->
<script type="text/html" id="toolbar">
    <!--{if auth('edit')}-->
    <a class="layui-btn layui-btn-primary layui-btn-sm" data-event-dbclick data-title="编辑数据" data-modal='{:url("edit")}?id={{d.id}}'>编 辑</a>
    <!--{/if}-->

    <!--{if auth("remove")}-->
    <a class="layui-btn layui-btn-danger layui-btn-sm" data-confirm="确定要删除数据吗?" data-action="{:url('remove')}" data-value="id#{{d.id}}">删 除</a>
    <!--{/if}-->
</script>
{/block}