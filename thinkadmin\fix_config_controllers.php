<?php

/**
 * 修复配置类控制器
 */

echo "=== 修复配置类控制器 ===\n\n";

// 需要修复的配置控制器
$configControllers = [
    'ApiKey' => [
        'title' => '接口密钥管理',
        'configs' => [
            'openai_api_key' => 'OpenAI API密钥',
            'openai_api_base' => 'OpenAI API基础URL',
            'claude_api_key' => 'Claude API密钥',
            'baidu_api_key' => '百度API密钥',
            'baidu_secret_key' => '百度Secret密钥',
            'zhipu_api_key' => '智谱API密钥',
            'qianfan_api_key' => '千帆API密钥',
            'qianfan_secret_key' => '千帆Secret密钥'
        ]
    ],
    'WebhookConfig' => [
        'title' => 'Webhook配置',
        'configs' => [
            'n8n_webhook_url' => 'n8n Webhook URL',
            'webhook_secret' => 'Webhook密钥',
            'webhook_timeout' => 'Webhook超时时间(秒)',
            'webhook_retry_times' => 'Webhook重试次数',
            'webhook_enable' => '启用Webhook'
        ]
    ],
    'ContentFilter' => [
        'title' => '内容风控规则',
        'configs' => [
            'content_filter_enable' => '启用内容过滤',
            'sensitive_words' => '敏感词列表',
            'filter_level' => '过滤级别',
            'auto_replace' => '自动替换敏感词',
            'replacement_char' => '替换字符'
        ]
    ],
    'BasicConfig' => [
        'title' => '基础参数设置',
        'configs' => [
            'site_name' => '网站名称',
            'site_logo' => '网站Logo',
            'site_description' => '网站描述',
            'default_paper_type' => '默认论文类型',
            'max_paper_length' => '论文最大字数',
            'min_paper_length' => '论文最小字数',
            'auto_save_interval' => '自动保存间隔(秒)',
            'enable_ai_writing' => '启用AI写作',
            'enable_plagiarism_check' => '启用查重功能',
            'enable_rewrite' => '启用降重功能'
        ]
    ],
    'EmailConfig' => [
        'title' => '邮件配置',
        'configs' => [
            'smtp_host' => 'SMTP服务器',
            'smtp_port' => 'SMTP端口',
            'smtp_user' => 'SMTP用户名',
            'smtp_pass' => 'SMTP密码',
            'smtp_secure' => 'SMTP加密方式',
            'mail_from' => '发件人邮箱',
            'mail_from_name' => '发件人名称'
        ]
    ]
];

foreach ($configControllers as $controllerName => $config) {
    echo "修复控制器: {$controllerName}\n";
    
    $controllerFile = "app/admin/controller/{$controllerName}.php";
    
    if (!file_exists($controllerFile)) {
        echo "  ❌ 控制器文件不存在: {$controllerFile}\n";
        continue;
    }
    
    // 备份原文件
    $backupFile = $controllerFile . '.backup_' . date('YmdHis');
    copy($controllerFile, $backupFile);
    echo "  📄 备份原文件: {$backupFile}\n";
    
    // 生成新的控制器内容
    $newContent = generateConfigController($controllerName, $config);
    
    // 写入新内容
    file_put_contents($controllerFile, $newContent);
    echo "  ✅ 已更新: {$controllerFile}\n";
    
    // 创建对应的视图模板
    $viewDir = "app/admin/view/" . strtolower(preg_replace('/([A-Z])/', '_$1', $controllerName));
    $viewDir = ltrim($viewDir, '_');
    
    if (!is_dir($viewDir)) {
        mkdir($viewDir, 0755, true);
        echo "  📁 创建视图目录: {$viewDir}\n";
    }
    
    // 创建index.html模板
    $indexTemplate = generateConfigIndexTemplate($config);
    $indexFile = "{$viewDir}/index.html";
    file_put_contents($indexFile, $indexTemplate);
    echo "  📄 创建模板: {$indexFile}\n";
}

echo "\n=== 修复完成 ===\n";

/**
 * 生成配置控制器内容
 */
function generateConfigController($controllerName, $config) {
    $title = $config['title'];
    $configs = $config['configs'];
    
    return <<<PHP
<?php

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\model\SystemConfig;

/**
 * {$title}
 * @class {$controllerName}
 * @package app\admin\controller
 */
class {$controllerName} extends Controller
{
    /**
     * {$title}
     * @auth true
     * @menu true
     */
    public function index()
    {
        try {
            \$this->title = '{$title}';
            
            if (\$this->request->isPost()) {
                \$data = \$this->request->post();
                
                // 保存配置
                foreach (\$data as \$name => \$value) {
                    SystemConfig::set(\$name, \$value);
                }
                
                \$this->success('配置保存成功！');
            }
            
            // 获取配置值
            \$configs = [];
PHP;

    foreach ($configs as $key => $label) {
        $newContent .= "\n            \$configs['{$key}'] = SystemConfig::get('{$key}', '');";
    }

    $newContent .= <<<PHP

            
            \$this->assign([
                'title' => '{$title}',
                'configs' => \$configs
            ]);
            
            return \$this->fetch();
            
        } catch (\\Exception \$e) {
            \$this->error('页面加载失败：' . \$e->getMessage());
        }
    }
}
PHP;

    return $newContent;
}

/**
 * 生成配置页面模板
 */
function generateConfigIndexTemplate($config) {
    $title = $config['title'];
    $configs = $config['configs'];
    
    $formFields = '';
    foreach ($configs as $key => $label) {
        if (strpos($key, 'pass') !== false || strpos($key, 'secret') !== false || strpos($key, 'key') !== false) {
            // 密码类型字段
            $formFields .= <<<HTML
            <div class="layui-form-item">
                <label class="layui-form-label">{$label}</label>
                <div class="layui-input-block">
                    <input type="password" name="{$key}" value="{\$configs.{$key}}" placeholder="请输入{$label}" class="layui-input">
                </div>
            </div>
HTML;
        } elseif (strpos($key, 'enable') !== false) {
            // 开关类型字段
            $formFields .= <<<HTML
            <div class="layui-form-item">
                <label class="layui-form-label">{$label}</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="{$key}" value="1" lay-skin="switch" lay-text="启用|禁用" {if \$configs.{$key}}checked{/if}>
                </div>
            </div>
HTML;
        } elseif (strpos($key, 'description') !== false || strpos($key, 'words') !== false) {
            // 文本域字段
            $formFields .= <<<HTML
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">{$label}</label>
                <div class="layui-input-block">
                    <textarea name="{$key}" placeholder="请输入{$label}" class="layui-textarea">{\$configs.{$key}}</textarea>
                </div>
            </div>
HTML;
        } else {
            // 普通文本字段
            $formFields .= <<<HTML
            <div class="layui-form-item">
                <label class="layui-form-label">{$label}</label>
                <div class="layui-input-block">
                    <input type="text" name="{$key}" value="{\$configs.{$key}}" placeholder="请输入{$label}" class="layui-input">
                </div>
            </div>
HTML;
        }
    }
    
    return <<<HTML
{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        {$title}
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
{$formFields}
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        \$.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                \$.msg.success(ret.info);
            } else {
                \$.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}
HTML;
}
