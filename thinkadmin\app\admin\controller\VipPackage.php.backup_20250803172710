<?php

namespace app\admin\controller;

use app\admin\model\VipPackageModel;
use think\admin\Controller;

/**
 * VIP套餐管理
 * @class VipPackage
 * @package app\admin\controller
 */
class VipPackage extends Controller
{
    /**
     * VIP套餐管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        VipPackageModel::mQuery($this)->layPage(function () {
            $this->title = 'VIP套餐管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加VIP套餐管理
     * @auth true
     */
    public function add()
    {
        VipPackageModel::mForm('vip_package/form');
    }

    /**
     * 编辑VIP套餐管理
     * @auth true
     */
    public function edit()
    {
        VipPackageModel::mForm('vip_package/form');
    }

    /**
     * 删除VIP套餐管理
     * @auth true
     */
    public function remove()
    {
        VipPackageModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        VipPackageModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}