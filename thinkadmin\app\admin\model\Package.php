<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;
use think\model\relation\HasMany;

/**
 * 套餐配置模型
 * @class Package
 * @package app\admin\model
 */
class Package extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'package';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'credits' => 'integer',
        'vip_days' => 'integer',
        'writing_quota' => 'integer',
        'rewrite_quota' => 'integer',
        'check_quota' => 'integer',
        'original_price' => 'float',
        'sale_price' => 'float',
        'discount_rate' => 'float',
        'sort' => 'integer',
        'is_hot' => 'integer',
        'status' => 'integer'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取套餐类型选项
     * @return array
     */
    public static function getTypeOptions(): array
    {
        return [
            'credits' => '积分包',
            'vip' => 'VIP套餐',
            'combo' => '组合套餐'
        ];
    }

    /**
     * 获取套餐类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = static::getTypeOptions();
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] ? '启用' : '禁用';
    }

    /**
     * 获取热门标识文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsHotTextAttr($value, $data)
    {
        return $data['is_hot'] ? '是' : '否';
    }

    /**
     * 获取折扣率文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getDiscountTextAttr($value, $data)
    {
        if ($data['original_price'] > 0) {
            $discount = round(($data['sale_price'] / $data['original_price']) * 100, 1);
            return $discount . '折';
        }
        return '无折扣';
    }

    /**
     * 获取节省金额
     * @param $value
     * @param $data
     * @return float
     */
    public function getSaveAmountAttr($value, $data)
    {
        return max(0, $data['original_price'] - $data['sale_price']);
    }

    /**
     * 获取套餐特色描述
     * @param $value
     * @param $data
     * @return array
     */
    public function getFeaturesAttr($value, $data)
    {
        $features = [];
        
        if ($data['credits'] > 0) {
            $features[] = $data['credits'] . ' 积分';
        }
        
        if ($data['vip_days'] > 0) {
            $features[] = $data['vip_days'] . ' 天VIP';
        }
        
        if ($data['writing_quota'] > 0) {
            $features[] = $data['writing_quota'] . ' 次写作';
        } elseif ($data['writing_quota'] == 0 && $data['type'] == 'vip') {
            $features[] = '无限写作';
        }
        
        if ($data['rewrite_quota'] > 0) {
            $features[] = $data['rewrite_quota'] . ' 次降重';
        } elseif ($data['rewrite_quota'] == 0 && $data['type'] == 'vip') {
            $features[] = '无限降重';
        }
        
        if ($data['check_quota'] > 0) {
            $features[] = $data['check_quota'] . ' 次查重';
        } elseif ($data['check_quota'] == 0 && $data['type'] == 'vip') {
            $features[] = '无限查重';
        }
        
        return $features;
    }

    /**
     * 关联订单
     * @return HasMany
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'package_id', 'id');
    }

    /**
     * 获取销量统计
     * @return int
     */
    public function getSalesCount(): int
    {
        return $this->orders()->where('payment_status', 'paid')->count();
    }

    /**
     * 获取销售额统计
     * @return float
     */
    public function getSalesAmount(): float
    {
        return (float) $this->orders()->where('payment_status', 'paid')->sum('final_price');
    }

    /**
     * 检查是否为VIP套餐
     * @return bool
     */
    public function isVipPackage(): bool
    {
        return $this->type === 'vip';
    }

    /**
     * 检查是否为积分包
     * @return bool
     */
    public function isCreditsPackage(): bool
    {
        return $this->type === 'credits';
    }

    /**
     * 检查是否为组合套餐
     * @return bool
     */
    public function isComboPackage(): bool
    {
        return $this->type === 'combo';
    }

    /**
     * 获取有效的套餐列表
     * @param string $type 套餐类型
     * @return array
     */
    public static function getAvailablePackages(string $type = ''): array
    {
        $query = static::mk()->where(['status' => 1]);
        
        if (!empty($type)) {
            $query->where('type', $type);
        }
        
        return $query->order('sort asc, id asc')->column('name', 'id');
    }

    /**
     * 获取热门套餐
     * @param int $limit
     * @return \think\Collection
     */
    public static function getHotPackages(int $limit = 5)
    {
        return static::mk()
            ->where(['status' => 1, 'is_hot' => 1])
            ->order('sort asc, id asc')
            ->limit($limit)
            ->select();
    }

    /**
     * 获取推荐套餐（按销量）
     * @param int $limit
     * @return \think\Collection
     */
    public static function getRecommendedPackages(int $limit = 5)
    {
        return static::mk()
            ->with(['orders'])
            ->where(['status' => 1])
            ->order('sort asc, id asc')
            ->limit($limit)
            ->select();
    }

    /**
     * 计算实际价格（考虑优惠券等）
     * @param float $couponDiscount 优惠券折扣
     * @return float
     */
    public function calculateFinalPrice(float $couponDiscount = 0): float
    {
        $finalPrice = $this->sale_price - $couponDiscount;
        return max(0, $finalPrice);
    }

    /**
     * 验证套餐数据
     * @param array $data
     * @return array
     */
    public static function validatePackageData(array $data): array
    {
        $errors = [];
        
        // 验证价格
        if ($data['original_price'] < 0) {
            $errors[] = '原价不能为负数';
        }
        
        if ($data['sale_price'] < 0) {
            $errors[] = '售价不能为负数';
        }
        
        if ($data['sale_price'] > $data['original_price']) {
            $errors[] = '售价不能高于原价';
        }
        
        // 验证配额
        if ($data['credits'] < 0 || $data['writing_quota'] < 0 || 
            $data['rewrite_quota'] < 0 || $data['check_quota'] < 0) {
            $errors[] = '配额数量不能为负数';
        }
        
        // 验证VIP天数
        if ($data['vip_days'] < 0) {
            $errors[] = 'VIP天数不能为负数';
        }
        
        return $errors;
    }
}
