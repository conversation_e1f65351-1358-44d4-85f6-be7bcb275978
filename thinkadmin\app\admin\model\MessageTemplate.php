<?php

// +----------------------------------------------------------------------
// | Message Template Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 消息模板模型
 * @class MessageTemplate
 * @package app\admin\model
 */
class MessageTemplate extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'message_template';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['variables'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'status' => 'integer',
        'variables' => 'array'
    ];

    /**
     * 获取器 - 变量定义
     * @param $value
     * @return array
     */
    public function getVariablesAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 变量定义
     * @param $value
     * @return string
     */
    public function setVariablesAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            0 => '禁用',
            1 => '启用'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            'system' => '系统通知',
            'email' => '邮件通知',
            'sms' => '短信通知',
            'wechat' => '微信通知'
        ];
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 搜索器 - 模板名称
     * @param $query
     * @param $value
     */
    public function searchNameAttr($query, $value)
    {
        $query->whereLike('name', "%{$value}%");
    }

    /**
     * 搜索器 - 模板代码
     * @param $query
     * @param $value
     */
    public function searchCodeAttr($query, $value)
    {
        $query->whereLike('code', "%{$value}%");
    }

    /**
     * 搜索器 - 主题
     * @param $query
     * @param $value
     */
    public function searchSubjectAttr($query, $value)
    {
        $query->whereLike('subject', "%{$value}%");
    }

    /**
     * 搜索器 - 类型
     * @param $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        $query->where('type', $value);
    }

    /**
     * 搜索器 - 状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 根据代码获取模板
     * @param string $code 模板代码
     * @return array|null
     */
    public static function getByCode(string $code)
    {
        $template = self::mk()->where(['code' => $code, 'status' => 1])->findOrEmpty();
        return $template->isEmpty() ? null : $template->toArray();
    }

    /**
     * 替换模板变量
     * @param string $content 模板内容
     * @param array $variables 变量值
     * @return string
     */
    public static function replaceVariables(string $content, array $variables = []): string
    {
        if (empty($variables)) {
            return $content;
        }
        
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        
        return $content;
    }

    /**
     * 发送消息
     * @param string $code 模板代码
     * @param array $variables 变量值
     * @param array $options 发送选项
     * @return bool
     */
    public static function sendMessage(string $code, array $variables = [], array $options = []): bool
    {
        $template = self::getByCode($code);
        if (!$template) {
            return false;
        }
        
        $content = self::replaceVariables($template['content'], $variables);
        $subject = !empty($template['subject']) ? self::replaceVariables($template['subject'], $variables) : '';
        
        // 这里可以根据模板类型调用不同的发送方法
        switch ($template['type']) {
            case 'email':
                // 调用邮件发送方法
                break;
            case 'sms':
                // 调用短信发送方法
                break;
            case 'wechat':
                // 调用微信发送方法
                break;
            case 'system':
            default:
                // 系统通知
                break;
        }
        
        return true;
    }
}
