<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\AiModel as AiModelModel;

/**
 * AI模型管理
 * @class AiModel
 * @package app\admin\controller
 */
class AiModel extends Controller
{
    /**
     * AI模型管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        AiModelModel::mQuery()->layTable(function () {
            $this->title = 'AI模型管理';
        }, static function (QueryHelper $query) {
            $query->like('name,provider,model_code')->equal('provider,status,health_status');
            $query->order('priority desc,id desc');
        });
    }

    /**
     * 添加AI模型
     * @auth true
     */
    public function add()
    {
        AiModelModel::mForm('ai_model/form');
    }

    /**
     * 编辑AI模型
     * @auth true
     */
    public function edit()
    {
        AiModelModel::mForm('ai_model/form');
    }

    /**
     * 查看AI模型详情
     * @auth true
     */
    public function view()
    {
        AiModelModel::mForm('ai_model/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 提供商选项
            $this->providerOptions = [
                'openai' => 'OpenAI',
                'baidu' => '百度',
                'aliyun' => '阿里云',
                'tencent' => '腾讯云',
                'zhipu' => '智谱AI',
                'moonshot' => 'Moonshot',
                'other' => '其他'
            ];
        } else {
            // POST请求时的数据验证
            $map = [];
            $map[] = ['provider', '=', $data['provider']];
            $map[] = ['model_code', '=', $data['model_code']];
            $map[] = ['id', '<>', $data['id'] ?? 0];
            if (AiModelModel::mk()->where($map)->count() > 0) {
                $this->error("该提供商下已存在相同模型代码！");
            }
            
            // 设置创建和更新时间
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['health_status'] = 1;
                $data['last_health_check'] = date('Y-m-d H:i:s');
            }
            $data['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 修改模型状态
     * @auth true
     */
    public function state()
    {
        AiModelModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除AI模型
     * @auth true
     */
    public function remove()
    {
        AiModelModel::mDelete();
    }

    /**
     * 健康检查
     * @auth true
     */
    public function healthCheck()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }
        
        $model = AiModelModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }
        
        // 这里可以实现实际的健康检查逻辑
        // 暂时模拟检查结果
        $healthStatus = rand(0, 1); // 随机模拟健康状态
        
        $model->save([
            'health_status' => $healthStatus,
            'last_health_check' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        if ($healthStatus) {
            $this->success('健康检查通过！');
        } else {
            $this->error('健康检查失败！');
        }
    }

    /**
     * 批量健康检查
     * @auth true
     */
    public function batchHealthCheck()
    {
        $models = AiModelModel::mk()->where(['status' => 1])->select();
        $successCount = 0;
        $failCount = 0;
        
        foreach ($models as $model) {
            // 这里可以实现实际的健康检查逻辑
            // 暂时模拟检查结果
            $healthStatus = rand(0, 1);
            
            $model->save([
                'health_status' => $healthStatus,
                'last_health_check' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
            
            if ($healthStatus) {
                $successCount++;
            } else {
                $failCount++;
            }
        }
        
        $this->success("批量健康检查完成！成功：{$successCount}个，失败：{$failCount}个");
    }

    /**
     * 测试模型连接
     * @auth true
     */
    public function testConnection()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }
        
        $model = AiModelModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }
        
        // 这里可以实现实际的连接测试逻辑
        // 暂时返回成功状态
        $this->success('连接测试成功！');
    }
}
