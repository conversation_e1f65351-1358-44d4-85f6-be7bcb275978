<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 查重接口配置模型
 * @class CheckApi
 * @package app\admin\model
 */
class CheckApi extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'check_api';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'cost_per_check' => 'float',
        'max_word_count' => 'integer',
        'priority' => 'integer',
        'status' => 'integer',
        'health_status' => 'integer'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取提供商选项
     * @return array
     */
    public static function getProviderOptions(): array
    {
        return [
            'paperpass' => 'PaperPass',
            'weipu' => '维普',
            'wanfang' => '万方',
            'cnki' => '知网',
            'turnitin' => 'Turnitin',
            'grammarly' => 'Grammarly',
            'copyscape' => 'Copyscape',
            'custom' => '自定义'
        ];
    }

    /**
     * 获取支持格式数组
     * @param $value
     * @return array
     */
    public function getSupportedFormatsAttr($value)
    {
        return is_string($value) ? explode(',', $value) : [];
    }

    /**
     * 设置支持格式
     * @param $value
     * @return string
     */
    public function setSupportedFormatsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        return $data['status'] ? '启用' : '禁用';
    }

    /**
     * 获取健康状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getHealthStatusTextAttr($value, $data)
    {
        $statusMap = [
            1 => '正常',
            0 => '异常',
            -1 => '未检测'
        ];
        return $statusMap[$data['health_status']] ?? '未知';
    }

    /**
     * 获取可用的查重接口
     * @return array
     */
    public static function getAvailableApis(): array
    {
        return static::mk()
            ->where(['status' => 1, 'health_status' => 1])
            ->order('priority desc, id asc')
            ->column('name', 'id');
    }

    /**
     * 检查接口健康状态
     * @param int $id
     * @return bool
     */
    public static function checkHealth(int $id): bool
    {
        $api = static::mk()->findOrEmpty($id);
        if ($api->isEmpty()) {
            return false;
        }

        // 这里应该实现实际的健康检查逻辑
        // 比如发送测试请求到API端点
        
        // 暂时返回true，实际应该根据API响应判断
        $isHealthy = true;
        
        // 更新健康状态
        $api->save([
            'health_status' => $isHealthy ? 1 : 0,
            'last_health_check' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);

        return $isHealthy;
    }

    /**
     * 获取最优接口（根据优先级和健康状态）
     * @param int $wordCount 文档字数
     * @return array
     */
    public static function getBestApi(int $wordCount = 0): array
    {
        $query = static::mk()->where(['status' => 1, 'health_status' => 1]);
        
        if ($wordCount > 0) {
            $query->where('max_word_count', '>=', $wordCount);
        }
        
        $api = $query->order('priority desc, cost_per_check asc, id asc')->findOrEmpty();
        
        return $api->toArray();
    }

    /**
     * 关联查重任务
     * @return \think\model\relation\HasMany
     */
    public function checkTasks()
    {
        return $this->hasMany(CheckTask::class, 'check_api_id', 'id');
    }
}
