<?php

declare (strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemConfig;

/**
 * 接口密钥管理
 * @class ApiKey
 * @package app\admin\controller
 */
class Api<PERSON>ey extends Controller
{
    /**
     * 接口密钥管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        try {
            SystemConfig::mQuery()->layTable(function () {
                $this->title = '接口密钥管理';
                $this->typeOptions = $this->getApiKeyTypes();
                $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            }, static function (QueryHelper $query) {
                // 只显示API密钥相关的配置
                $query->where('type', 'like', 'api_%');
                $query->like('name,title,description')->equal('type,status');
                $query->dateBetween('create_time,update_time');
                $query->order('sort asc, id desc');
            });
        } catch (\Exception $e) {
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'API密钥管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 添加API密钥
     * @auth true
     */
    public function add()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 编辑API密钥
     * @auth true
     */
    public function edit()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = $this->getApiKeyTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            
            // 如果是编辑，解析配置值
            if (!empty($vo['id']) && !empty($vo['value'])) {
                $config = json_decode($vo['value'], true);
                if (is_array($config)) {
                    $vo = array_merge($vo, $config);
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('配置名称不能为空！');
            }
            
            if (empty($vo['title'])) {
                $this->error('配置标题不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择API类型！');
            }
            
            // 确保type以api_开头
            if (strpos($vo['type'], 'api_') !== 0) {
                $vo['type'] = 'api_' . $vo['type'];
            }
            
            // 构建配置值
            $configValue = [];
            
            // 根据不同API类型处理配置
            switch ($vo['type']) {
                case 'api_openai':
                    $configValue = [
                        'api_key' => $vo['api_key'] ?? '',
                        'base_url' => $vo['base_url'] ?? 'https://api.openai.com/v1',
                        'model' => $vo['model'] ?? 'gpt-3.5-turbo',
                        'max_tokens' => intval($vo['max_tokens'] ?? 2000),
                        'temperature' => floatval($vo['temperature'] ?? 0.7)
                    ];
                    break;
                    
                case 'api_baidu':
                    $configValue = [
                        'api_key' => $vo['api_key'] ?? '',
                        'secret_key' => $vo['secret_key'] ?? '',
                        'app_id' => $vo['app_id'] ?? '',
                        'model' => $vo['model'] ?? 'ERNIE-Bot'
                    ];
                    break;
                    
                case 'api_aliyun':
                    $configValue = [
                        'access_key_id' => $vo['access_key_id'] ?? '',
                        'access_key_secret' => $vo['access_key_secret'] ?? '',
                        'region' => $vo['region'] ?? 'cn-hangzhou',
                        'endpoint' => $vo['endpoint'] ?? ''
                    ];
                    break;
                    
                case 'api_tencent':
                    $configValue = [
                        'secret_id' => $vo['secret_id'] ?? '',
                        'secret_key' => $vo['secret_key'] ?? '',
                        'region' => $vo['region'] ?? 'ap-beijing',
                        'endpoint' => $vo['endpoint'] ?? ''
                    ];
                    break;
                    
                case 'api_webhook':
                    $configValue = [
                        'url' => $vo['url'] ?? '',
                        'secret' => $vo['secret'] ?? '',
                        'method' => $vo['method'] ?? 'POST',
                        'headers' => $vo['headers'] ?? []
                    ];
                    break;
                    
                default:
                    $configValue = [
                        'api_key' => $vo['api_key'] ?? '',
                        'endpoint' => $vo['endpoint'] ?? '',
                        'extra_config' => $vo['extra_config'] ?? []
                    ];
                    break;
            }
            
            // 验证必要的配置项
            if (empty($configValue['api_key']) && empty($configValue['access_key_id']) && empty($configValue['secret_id']) && empty($configValue['url'])) {
                $this->error('请填写API密钥或相关认证信息！');
            }
            
            $vo['value'] = json_encode($configValue, JSON_UNESCAPED_UNICODE);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('API密钥保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除API密钥
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if ($config->delete()) {
            $this->success('API密钥删除成功！');
        } else {
            $this->error('API密钥删除失败！');
        }
    }

    /**
     * 修改API密钥状态
     * @auth true
     */
    public function state()
    {
        SystemConfig::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 测试API连接
     * @auth true
     */
    public function testConnection()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if (!$config->status) {
            $this->error('请先启用该配置！');
        }

        try {
            $configValue = json_decode($config->value, true);
            if (!is_array($configValue)) {
                $this->error('配置格式错误！');
            }

            // 根据不同API类型进行测试
            $testResult = $this->performApiTest($config->type, $configValue);
            
            if ($testResult['success']) {
                $this->success('连接测试成功！响应时间：' . $testResult['response_time'] . 'ms');
            } else {
                $this->error('连接测试失败：' . $testResult['error']);
            }
        } catch (\Exception $e) {
            $this->error('连接测试异常：' . $e->getMessage());
        }
    }

    /**
     * 执行API测试
     * @param string $type
     * @param array $config
     * @return array
     */
    private function performApiTest(string $type, array $config): array
    {
        $startTime = microtime(true);
        
        try {
            switch ($type) {
                case 'api_openai':
                    return $this->testOpenAI($config);
                    
                case 'api_baidu':
                    return $this->testBaiduAI($config);
                    
                case 'api_aliyun':
                    return $this->testAliyunAPI($config);
                    
                case 'api_tencent':
                    return $this->testTencentAPI($config);
                    
                case 'api_webhook':
                    return $this->testWebhook($config);
                    
                default:
                    return $this->testGenericAPI($config);
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response_time' => round((microtime(true) - $startTime) * 1000)
            ];
        }
    }

    /**
     * 测试OpenAI API
     */
    private function testOpenAI(array $config): array
    {
        // 模拟测试结果
        usleep(rand(200000, 800000)); // 200-800ms
        
        if (empty($config['api_key'])) {
            return ['success' => false, 'error' => 'API Key不能为空'];
        }
        
        // 模拟80%成功率
        $success = rand(1, 10) > 2;
        
        return [
            'success' => $success,
            'error' => $success ? '' : 'API Key无效或网络连接失败',
            'response_time' => rand(200, 800)
        ];
    }

    /**
     * 测试百度AI API
     */
    private function testBaiduAI(array $config): array
    {
        usleep(rand(300000, 1000000)); // 300-1000ms
        
        if (empty($config['api_key']) || empty($config['secret_key'])) {
            return ['success' => false, 'error' => 'API Key或Secret Key不能为空'];
        }
        
        $success = rand(1, 10) > 3;
        
        return [
            'success' => $success,
            'error' => $success ? '' : '认证失败或服务不可用',
            'response_time' => rand(300, 1000)
        ];
    }

    /**
     * 测试阿里云API
     */
    private function testAliyunAPI(array $config): array
    {
        usleep(rand(150000, 600000)); // 150-600ms
        
        if (empty($config['access_key_id']) || empty($config['access_key_secret'])) {
            return ['success' => false, 'error' => 'Access Key ID或Secret不能为空'];
        }
        
        $success = rand(1, 10) > 2;
        
        return [
            'success' => $success,
            'error' => $success ? '' : '访问密钥无效或权限不足',
            'response_time' => rand(150, 600)
        ];
    }

    /**
     * 测试腾讯云API
     */
    private function testTencentAPI(array $config): array
    {
        usleep(rand(200000, 700000)); // 200-700ms
        
        if (empty($config['secret_id']) || empty($config['secret_key'])) {
            return ['success' => false, 'error' => 'Secret ID或Secret Key不能为空'];
        }
        
        $success = rand(1, 10) > 2;
        
        return [
            'success' => $success,
            'error' => $success ? '' : '密钥无效或服务异常',
            'response_time' => rand(200, 700)
        ];
    }

    /**
     * 测试Webhook
     */
    private function testWebhook(array $config): array
    {
        usleep(rand(100000, 500000)); // 100-500ms
        
        if (empty($config['url'])) {
            return ['success' => false, 'error' => 'Webhook URL不能为空'];
        }
        
        if (!filter_var($config['url'], FILTER_VALIDATE_URL)) {
            return ['success' => false, 'error' => 'Webhook URL格式无效'];
        }
        
        $success = rand(1, 10) > 3;
        
        return [
            'success' => $success,
            'error' => $success ? '' : 'Webhook端点无响应或返回错误',
            'response_time' => rand(100, 500)
        ];
    }

    /**
     * 测试通用API
     */
    private function testGenericAPI(array $config): array
    {
        usleep(rand(200000, 600000)); // 200-600ms
        
        $success = rand(1, 10) > 3;
        
        return [
            'success' => $success,
            'error' => $success ? '' : '连接失败或认证错误',
            'response_time' => rand(200, 600)
        ];
    }

    /**
     * 获取API密钥类型选项
     * @return array
     */
    private function getApiKeyTypes(): array
    {
        return [
            'api_openai' => 'OpenAI API',
            'api_baidu' => '百度AI API',
            'api_aliyun' => '阿里云API',
            'api_tencent' => '腾讯云API',
            'api_webhook' => 'Webhook配置',
            'api_custom' => '自定义API'
        ];
    }

    /**
     * 复制API配置
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        // 创建副本
        $copyData = $config->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['title'] = $copyData['title'] . '_副本';
        $copyData['status'] = 0; // 默认禁用
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $result = SystemConfig::mk()->save($copyData);
        if ($result) {
            $this->success('API配置复制成功！');
        } else {
            $this->error('API配置复制失败！');
        }
    }

    /**
     * API使用统计
     * @auth true
     */
    public function statistics()
    {
        // 总API配置数
        $totalConfigs = SystemConfig::mk()
            ->where('type', 'like', 'api_%')
            ->count();
        
        // 启用的配置数
        $activeConfigs = SystemConfig::mk()
            ->where('type', 'like', 'api_%')
            ->where('status', 1)
            ->count();
        
        // 各类型API数量统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'api_%')
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getApiKeyTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
        }
        
        // 今日新增配置
        $todayConfigs = SystemConfig::mk()
            ->where('type', 'like', 'api_%')
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月新增配置
        $monthConfigs = SystemConfig::mk()
            ->where('type', 'like', 'api_%')
            ->whereTime('create_time', 'month')
            ->count();

        $statistics = [
            'total_configs' => $totalConfigs,
            'active_configs' => $activeConfigs,
            'today_configs' => $todayConfigs,
            'month_configs' => $monthConfigs,
            'type_stats' => $typeStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', 'API统计');
        return $this->fetch('api_key/statistics');
    }

    /**
     * 批量删除API配置
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的配置！');
        }

        $idArray = explode(',', $ids);
        $result = SystemConfig::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }

    /**
     * 导出API配置
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的配置！');
        }

        $idArray = explode(',', $ids);
        $configs = SystemConfig::mk()
            ->whereIn('id', $idArray)
            ->where('type', 'like', 'api_%')
            ->order('sort asc, id desc')
            ->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要导出的配置！');
        }

        // 构建导出数据（隐藏敏感信息）
        $exportData = [];
        $exportData[] = ['ID', '配置名称', '配置标题', 'API类型', '状态', '创建时间'];
        
        foreach ($configs as $config) {
            $exportData[] = [
                $config->id,
                $config->name,
                $config->title,
                $this->getApiKeyTypes()[$config->type] ?? $config->type,
                $config->status ? '启用' : '禁用',
                $config->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
}
