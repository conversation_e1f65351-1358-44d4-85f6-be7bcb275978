<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;
use think\model\relation\BelongsTo;

/**
 * 文档模板模型
 * @class DocumentTemplate
 * @package app\admin\model
 */
class DocumentTemplate extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'document_template';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['style_config'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'paper_type_id' => 'integer',
        'is_default' => 'integer',
        'usage_count' => 'integer',
        'status' => 'integer',
        'style_config' => 'array'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取器 - 样式配置
     * @param $value
     * @return array
     */
    public function getStyleConfigAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 样式配置
     * @param $value
     * @return string
     */
    public function setStyleConfigAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 关联论文类型
     * @return BelongsTo
     */
    public function paperType(): BelongsTo
    {
        return $this->belongsTo(PaperType::class, 'paper_type_id', 'id');
    }

    /**
     * 获取指定类型的默认模板
     * @param string $type 模板类型
     * @param int $paperTypeId 论文类型ID
     * @return array
     */
    public static function getDefaultTemplate(string $type, int $paperTypeId = 0): array
    {
        $template = static::mk()->where([
            'type' => $type,
            'paper_type_id' => $paperTypeId,
            'is_default' => 1,
            'status' => 1
        ])->findOrEmpty();

        if ($template->isEmpty() && $paperTypeId > 0) {
            // 如果指定类型没有默认模板，尝试获取通用模板
            $template = static::mk()->where([
                'type' => $type,
                'paper_type_id' => 0,
                'is_default' => 1,
                'status' => 1
            ])->findOrEmpty();
        }

        return $template->toArray();
    }

    /**
     * 获取模板类型选项
     * @return array
     */
    public static function getTypeOptions(): array
    {
        return [
            'cover' => '封面模板',
            'content' => '正文模板',
            'reference' => '参考文献模板',
            'appendix' => '附录模板'
        ];
    }

    /**
     * 增加使用次数
     * @param int $id
     * @return bool
     */
    public static function increaseUsage(int $id): bool
    {
        return static::mk()->where('id', $id)->inc('usage_count')->update() !== false;
    }

    /**
     * 获取可用的模板列表
     * @param string $type 模板类型
     * @param int $paperTypeId 论文类型ID
     * @return array
     */
    public static function getAvailableTemplates(string $type, int $paperTypeId = 0): array
    {
        $query = static::mk()->where([
            'type' => $type,
            'status' => 1
        ]);

        if ($paperTypeId > 0) {
            $query->where(function ($query) use ($paperTypeId) {
                $query->where('paper_type_id', $paperTypeId)
                      ->whereOr('paper_type_id', 0);
            });
        }

        return $query->order('is_default desc, usage_count desc, id desc')
                    ->column('name', 'id');
    }
}
