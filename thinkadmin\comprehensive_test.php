<?php

/**
 * 综合测试所有菜单和控制器
 */

echo "=== 综合测试所有菜单和控制器 ===\n\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在\n";
        exit(1);
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
    
    // 获取所有菜单项
    echo "1. 测试所有菜单项:\n";
    $stmt = $pdo->query("SELECT title, node, url FROM system_menu WHERE status = 1 AND node != '' ORDER BY sort ASC, id ASC");
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $testResults = [];
    $passCount = 0;
    $failCount = 0;
    
    foreach ($menus as $menu) {
        $node = $menu['node'];
        $title = $menu['title'];
        
        echo "  测试: {$title} ({$node})\n";
        
        // 解析控制器路径
        if (strpos($node, 'admin/') === 0) {
            $controllerPath = str_replace('admin/', '', $node);
            $parts = explode('/', $controllerPath);
            
            if (count($parts) >= 2) {
                $controllerName = $parts[0];
                $method = $parts[1];
                $controllerFile = "app/admin/controller/{$controllerName}.php";
                
                $result = [
                    'title' => $title,
                    'controller' => $controllerName,
                    'method' => $method,
                    'file' => $controllerFile,
                    'node' => $node,
                    'tests' => []
                ];
                
                // 测试1: 控制器文件是否存在
                if (file_exists($controllerFile)) {
                    $result['tests']['file_exists'] = '✅ 控制器文件存在';
                } else {
                    $result['tests']['file_exists'] = '❌ 控制器文件不存在';
                    $failCount++;
                    $testResults[] = $result;
                    continue;
                }
                
                // 测试2: 控制器语法是否正确
                $syntaxCheck = shell_exec("php -l {$controllerFile} 2>&1");
                if (strpos($syntaxCheck, 'No syntax errors') !== false) {
                    $result['tests']['syntax'] = '✅ 语法正确';
                } else {
                    $result['tests']['syntax'] = '❌ 语法错误: ' . trim($syntaxCheck);
                    $failCount++;
                    $testResults[] = $result;
                    continue;
                }
                
                // 测试3: 方法是否存在
                $content = file_get_contents($controllerFile);
                if (strpos($content, "public function {$method}()") !== false) {
                    $result['tests']['method_exists'] = '✅ 方法存在';
                } else {
                    $result['tests']['method_exists'] = '❌ 方法不存在';
                    $failCount++;
                    $testResults[] = $result;
                    continue;
                }
                
                // 测试4: 是否使用了有问题的mQuery方法
                if (strpos($content, '::mQuery') !== false) {
                    $result['tests']['no_mquery'] = '⚠️  仍在使用mQuery方法';
                } else {
                    $result['tests']['no_mquery'] = '✅ 未使用mQuery方法';
                }
                
                // 测试5: 视图模板是否存在
                $viewDir = "app/admin/view/" . strtolower(preg_replace('/([A-Z])/', '_$1', $controllerName));
                $viewDir = ltrim($viewDir, '_');
                $viewFile = "{$viewDir}/index.html";

                // 特殊处理一些控制器名称映射
                $specialMappings = [
                    'DraftBox' => 'draft_box',
                    'PaperType' => 'paper_type',
                    'AiModel' => 'ai_model',
                    'RewriteRecord' => 'rewrite_record',
                    'SystemNotice' => 'system_notice',
                    'ExportTemplate' => 'export_template',
                    'OutlineTemplate' => 'outline_template',
                    'MessageTemplate' => 'message_template',
                    'N8nWorkflow' => 'n8n_workflow',
                    'RewriteModel' => 'rewrite_model',
                    'VipPackage' => 'vip_package',
                    'PackageConfig' => 'package_config',
                    'ExportMonitor' => 'export_monitor',
                    'PromptTemplate' => 'prompt_template',
                    'CheckRecord' => 'check_record',
                    'UserPoints' => 'user_points',
                    'RechargeRecord' => 'recharge_record',
                    'RewriteTask' => 'rewrite_task',
                    'ContentTemplate' => 'content_template',
                    'NotificationLog' => 'notification_log',
                    'PaperProject' => 'paper_project',
                    'ApiKey' => 'api_key',
                    'WebhookConfig' => 'webhook_config',
                    'ContentFilter' => 'content_filter',
                    'BasicConfig' => 'basic_config',
                    'EmailConfig' => 'email_config'
                ];

                if (isset($specialMappings[$controllerName])) {
                    $viewDir = "app/admin/view/" . $specialMappings[$controllerName];
                    $viewFile = "{$viewDir}/index.html";
                }
                
                if (file_exists($viewFile)) {
                    $result['tests']['view_exists'] = '✅ 视图模板存在';
                    
                    // 测试6: 视图模板是否使用安全的继承
                    $viewContent = file_get_contents($viewFile);
                    if (strpos($viewContent, "extend name='table'") !== false) {
                        $result['tests']['safe_template'] = '⚠️  使用了table模板';
                    } else {
                        $result['tests']['safe_template'] = '✅ 使用安全模板';
                    }
                } else {
                    $result['tests']['view_exists'] = '❌ 视图模板不存在';
                }
                
                // 判断整体测试结果
                $hasError = false;
                foreach ($result['tests'] as $test) {
                    if (strpos($test, '❌') !== false) {
                        $hasError = true;
                        break;
                    }
                }
                
                if ($hasError) {
                    $failCount++;
                    echo "    ❌ 测试失败\n";
                } else {
                    $passCount++;
                    echo "    ✅ 测试通过\n";
                }
                
                $testResults[] = $result;
            }
        }
    }
    
    echo "\n2. 测试结果统计:\n";
    echo "  ✅ 通过: {$passCount}\n";
    echo "  ❌ 失败: {$failCount}\n";
    echo "  📊 总计: " . ($passCount + $failCount) . "\n";
    
    // 显示失败的详细信息
    if ($failCount > 0) {
        echo "\n3. 失败项目详情:\n";
        foreach ($testResults as $result) {
            $hasError = false;
            foreach ($result['tests'] as $test) {
                if (strpos($test, '❌') !== false) {
                    $hasError = true;
                    break;
                }
            }
            
            if ($hasError) {
                echo "  ❌ {$result['title']} ({$result['controller']}::{$result['method']}):\n";
                foreach ($result['tests'] as $testName => $testResult) {
                    echo "    - {$testResult}\n";
                }
                echo "\n";
            }
        }
    }
    
    // 显示警告项目
    echo "\n4. 需要注意的项目:\n";
    foreach ($testResults as $result) {
        $hasWarning = false;
        foreach ($result['tests'] as $test) {
            if (strpos($test, '⚠️') !== false) {
                $hasWarning = true;
                break;
            }
        }
        
        if ($hasWarning) {
            echo "  ⚠️  {$result['title']} ({$result['controller']}::{$result['method']}):\n";
            foreach ($result['tests'] as $testName => $testResult) {
                if (strpos($testResult, '⚠️') !== false) {
                    echo "    - {$testResult}\n";
                }
            }
            echo "\n";
        }
    }
    
    // 最终建议
    echo "\n5. 修复建议:\n";
    if ($failCount == 0) {
        echo "  🎉 所有菜单项都已正确配置！\n";
        echo "  💡 建议:\n";
        echo "    - 清除runtime缓存\n";
        echo "    - 重启Web服务器\n";
        echo "    - 在浏览器中逐一测试各个菜单\n";
    } else {
        echo "  🔧 需要修复的问题:\n";
        echo "    - 创建缺失的控制器文件\n";
        echo "    - 修复语法错误\n";
        echo "    - 添加缺失的方法\n";
        echo "    - 创建缺失的视图模板\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
