<?php

/**
 * 创建缺失的模板文件
 */

echo "=== 创建缺失的模板文件 ===\n\n";

// 需要创建模板的控制器
$controllers = [
    'vip_package' => [
        'title' => 'VIP套餐管理',
        'fields' => [
            'id' => 'ID',
            'name' => '套餐名称',
            'description' => '套餐描述',
            'price' => '价格',
            'duration_days' => '有效期(天)',
            'features' => '功能特性',
            'sort' => '排序',
            'status' => '状态',
            'create_time' => '创建时间'
        ]
    ],
    'user_points' => [
        'title' => '用户积分管理',
        'fields' => [
            'id' => 'ID',
            'user_id' => '用户ID',
            'points' => '积分余额',
            'change_type' => '变动类型',
            'change_amount' => '变动数量',
            'description' => '变动说明',
            'create_time' => '创建时间'
        ]
    ],
    'recharge_record' => [
        'title' => '充值记录',
        'fields' => [
            'id' => 'ID',
            'user_id' => '用户ID',
            'amount' => '充值金额',
            'payment_method' => '支付方式',
            'payment_status' => '支付状态',
            'transaction_id' => '交易号',
            'create_time' => '创建时间',
            'update_time' => '更新时间'
        ]
    ]
];

foreach ($controllers as $controllerKey => $config) {
    $viewDir = "app/admin/view/{$controllerKey}";
    
    // 创建视图目录
    if (!is_dir($viewDir)) {
        mkdir($viewDir, 0755, true);
        echo "✅ 创建目录: {$viewDir}\n";
    }
    
    // 创建index.html模板
    $indexTemplate = generateIndexTemplate($config);
    $indexFile = "{$viewDir}/index.html";
    file_put_contents($indexFile, $indexTemplate);
    echo "✅ 创建模板: {$indexFile}\n";
    
    // 创建form.html模板
    $formTemplate = generateFormTemplate($config);
    $formFile = "{$viewDir}/form.html";
    file_put_contents($formFile, $formTemplate);
    echo "✅ 创建模板: {$formFile}\n";
}

echo "\n=== 创建完成 ===\n";

/**
 * 生成index模板
 */
function generateIndexTemplate($config) {
    $title = $config['title'];
    $fields = $config['fields'];
    
    $searchFields = '';
    $tableHeaders = '';
    $tableRows = '';
    
    // 生成搜索字段
    foreach ($fields as $field => $label) {
        if (in_array($field, ['name', 'title', 'description', 'user_id'])) {
            $placeholder = "请输入{$label}";
            $searchFields .= <<<HTML
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">{$label}</label>
                <div class="layui-input-inline">
                    <input name="{$field}" value="{\$get.{$field}|default=''}" placeholder="{$placeholder}" class="layui-input">
                </div>
            </div>
HTML;
        }
    }
    
    // 生成表格头
    foreach ($fields as $field => $label) {
        if ($field !== 'features' && $field !== 'description') { // 跳过长文本字段
            $tableHeaders .= "                        <th class='text-left nowrap'>{$label}</th>\n";
        }
    }
    
    // 生成表格行
    foreach ($fields as $field => $label) {
        if ($field === 'features' || $field === 'description') {
            continue; // 跳过长文本字段
        }
        
        if ($field === 'status') {
            $tableRows .= <<<HTML
                        <td class='text-left nowrap'>
                            {if auth("state")}
                            <input type="checkbox" value="{\$vo.id}" lay-skin="switch" lay-text="启用|禁用" lay-filter="StatusSwitch" {if \$vo.status}checked{/if}>
                            {else}
                            {if \$vo.status}<span class="layui-badge layui-bg-green">启用</span>{else}<span class="layui-badge layui-bg-red">禁用</span>{/if}
                            {/if}
                        </td>
HTML;
        } elseif ($field === 'create_time' || $field === 'update_time') {
            $tableRows .= "                        <td class='text-left nowrap'>{\$vo.{$field}|format_datetime}</td>\n";
        } elseif ($field === 'price' || $field === 'amount') {
            $tableRows .= "                        <td class='text-left nowrap'>￥{\$vo.{$field}|default=0}</td>\n";
        } else {
            $tableRows .= "                        <td class='text-left nowrap'>{\$vo.{$field}|default=''}</td>\n";
        }
    }
    
    return <<<HTML
{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        {$title}
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
{$searchFields}
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">全部状态</option>
                        <option value="1" {if isset(\$get.status) and \$get.status eq '1'}selected{/if}>启用</option>
                        <option value="0" {if isset(\$get.status) and \$get.status eq '0'}selected{/if}>禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <!-- 操作按钮 -->
        <div class="layui-row" style="margin-bottom: 15px;">
            <div class="layui-col-md12">
                {if auth("add")}
                <button data-modal='{:url("add")}' data-title="添加{$title}" class='layui-btn layui-btn-sm layui-btn-primary'>
                    <i class='layui-icon layui-icon-add-1'></i> 添加{$title}
                </button>
                {/if}
                {if auth("remove")}
                <button data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                    <i class='layui-icon layui-icon-delete'></i> 批量删除
                </button>
                {/if}
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
{$tableHeaders}
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach \$list as \$key=>\$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{\$vo.id}' type='checkbox'>
                        </td>
{$tableRows}
                        <td class='text-left nowrap'>
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-xs' data-modal='{:url("edit")}?id={\$vo.id}' data-title="编辑{$title}">编辑</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-xs layui-btn-danger' data-confirm="确定要删除吗？" data-action='{:url("remove")}' data-value="id#{\$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                
                {if empty(\$list)}
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <p class="help-block text-center well" style="padding: 20px; text-align: center; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><br>
                            暂 无 数 据！
                        </p>
                    </div>
                </div>
                {/if}
                
                <!-- 分页 -->
                {\$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}

{block name='script'}
<script>
    \$(function () {
        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            \$.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) {
                    \$.msg.error(ret.info, 3, function () {
                        location.reload();
                    });
                } else {
                    \$.msg.success(ret.info);
                }
                return false;
            }, false);
        });
    });
</script>
{/block}
HTML;
}

/**
 * 生成form模板
 */
function generateFormTemplate($config) {
    $title = $config['title'];
    
    return <<<HTML
{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-form"></span>
        {\$title}
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{\$vo.name|default=''}" placeholder="请输入名称" class="layui-input" required>
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">{\$vo.description|default=''}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" value="{\$vo.sort|default=100}" placeholder="请输入排序值" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset(\$vo.status) || \$vo.status eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset(\$vo.status) && \$vo.status eq 0}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(formSubmit)', function(data){
        \$.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                \$.msg.success(ret.info, 3, function(){
                    parent.layer.closeAll();
                    parent.location.reload();
                });
            } else {
                \$.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}
HTML;
}
