<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;
use think\model\relation\BelongsTo;

/**
 * 查重报告详情模型
 * @class CheckDetail
 * @package app\admin\model
 */
class CheckDetail extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'check_detail';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['matched_sources'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'task_id' => 'integer',
        'paragraph_index' => 'integer',
        'similarity_rate' => 'float',
        'matched_sources' => 'array'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取相似度等级
     * @param $value
     * @param $data
     * @return string
     */
    public function getSimilarityLevelAttr($value, $data)
    {
        $rate = $data['similarity_rate'] ?? 0;
        if ($rate <= 10) {
            return '低';
        } elseif ($rate <= 30) {
            return '中';
        } else {
            return '高';
        }
    }

    /**
     * 获取相似度等级颜色
     * @param $value
     * @param $data
     * @return string
     */
    public function getSimilarityColorAttr($value, $data)
    {
        $rate = $data['similarity_rate'] ?? 0;
        if ($rate <= 10) {
            return 'green';
        } elseif ($rate <= 30) {
            return 'orange';
        } else {
            return 'red';
        }
    }

    /**
     * 获取匹配来源数量
     * @param $value
     * @param $data
     * @return int
     */
    public function getSourceCountAttr($value, $data)
    {
        $sources = $data['matched_sources'] ?? [];
        return is_array($sources) ? count($sources) : 0;
    }

    /**
     * 获取原文预览（截取前100字符）
     * @param $value
     * @param $data
     * @return string
     */
    public function getTextPreviewAttr($value, $data)
    {
        $text = $data['original_text'] ?? '';
        return mb_strlen($text) > 100 ? mb_substr($text, 0, 100) . '...' : $text;
    }

    /**
     * 关联查重任务
     * @return BelongsTo
     */
    public function checkTask(): BelongsTo
    {
        return $this->belongsTo(CheckTask::class, 'task_id', 'id');
    }

    /**
     * 获取匹配来源列表
     * @return array
     */
    public function getMatchedSourcesList(): array
    {
        $sources = $this->matched_sources ?: [];
        if (!is_array($sources)) {
            return [];
        }

        $result = [];
        foreach ($sources as $source) {
            if (is_array($source)) {
                $result[] = [
                    'title' => $source['title'] ?? '未知来源',
                    'url' => $source['url'] ?? '',
                    'similarity' => $source['similarity'] ?? 0,
                    'matched_text' => $source['matched_text'] ?? ''
                ];
            }
        }

        return $result;
    }

    /**
     * 检查是否需要修改
     * @return bool
     */
    public function needsModification(): bool
    {
        return $this->similarity_rate > 30;
    }

    /**
     * 检查是否为高风险段落
     * @return bool
     */
    public function isHighRisk(): bool
    {
        return $this->similarity_rate > 50;
    }

    /**
     * 获取修改建议列表
     * @return array
     */
    public function getSuggestionsList(): array
    {
        $suggestions = $this->suggestions ?: '';
        if (empty($suggestions)) {
            return [];
        }

        // 如果是JSON格式
        $decoded = json_decode($suggestions, true);
        if (is_array($decoded)) {
            return $decoded;
        }

        // 如果是纯文本，按行分割
        return array_filter(explode("\n", $suggestions));
    }

    /**
     * 添加修改建议
     * @param string $suggestion
     * @return bool
     */
    public function addSuggestion(string $suggestion): bool
    {
        $suggestions = $this->getSuggestionsList();
        $suggestions[] = $suggestion;
        
        return $this->save(['suggestions' => json_encode($suggestions, JSON_UNESCAPED_UNICODE)]);
    }

    /**
     * 根据相似度获取段落列表
     * @param int $taskId
     * @param float $minSimilarity
     * @return \think\Collection
     */
    public static function getHighSimilarityParagraphs(int $taskId, float $minSimilarity = 30.0)
    {
        return static::mk()
            ->where('task_id', $taskId)
            ->where('similarity_rate', '>', $minSimilarity)
            ->order('similarity_rate desc, paragraph_index asc')
            ->select();
    }

    /**
     * 获取段落统计信息
     * @param int $taskId
     * @return array
     */
    public static function getParagraphStats(int $taskId): array
    {
        $details = static::mk()->where('task_id', $taskId)->select();
        
        $stats = [
            'total' => $details->count(),
            'low_risk' => 0,      // ≤10%
            'medium_risk' => 0,   // 10-30%
            'high_risk' => 0,     // >30%
            'avg_similarity' => 0
        ];

        if ($stats['total'] > 0) {
            $totalSimilarity = 0;
            foreach ($details as $detail) {
                $rate = $detail->similarity_rate;
                $totalSimilarity += $rate;
                
                if ($rate <= 10) {
                    $stats['low_risk']++;
                } elseif ($rate <= 30) {
                    $stats['medium_risk']++;
                } else {
                    $stats['high_risk']++;
                }
            }
            
            $stats['avg_similarity'] = round($totalSimilarity / $stats['total'], 2);
        }

        return $stats;
    }
}
