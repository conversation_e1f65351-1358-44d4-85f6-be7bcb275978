<?php

declare (strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemConfig;

/**
 * Webhook配置
 * @class WebhookConfig
 * @package app\admin\controller
 */
class WebhookConfig extends Controller
{
    /**
     * Webhook配置
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        WebhookConfigModel::mQuery()->layTable(function () {
            $this->title = 'Webhook配置管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示Webhook相关的配置
            $query->where('type', 'like', 'webhook_%');
            $query->like('name,title,description')->equal('type,status');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加Webhook配置
     * @auth true
     */public function add()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 编辑Webhook配置
     * @auth true
     */
    public function edit()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = $this->getWebhookTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            $this->methodOptions = ['GET' => 'GET', 'POST' => 'POST', 'PUT' => 'PUT', 'DELETE' => 'DELETE'];
            $this->eventOptions = $this->getWebhookEvents();
            
            // 如果是编辑，解析配置值
            if (!empty($vo['id']) && !empty($vo['value'])) {
                $config = json_decode($vo['value'], true);
                if (is_array($config)) {
                    $vo = array_merge($vo, $config);
                    // 处理数组字段
                    if (isset($config['events']) && is_array($config['events'])) {
                        $vo['events'] = implode(',', $config['events']);
                    }
                    if (isset($config['headers']) && is_array($config['headers'])) {
                        $vo['headers_json'] = json_encode($config['headers'], JSON_UNESCAPED_UNICODE);
                    }
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('配置名称不能为空！');
            }
            
            if (empty($vo['title'])) {
                $this->error('配置标题不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择Webhook类型！');
            }
            
            // 确保type以webhook_开头
            if (strpos($vo['type'], 'webhook_') !== 0) {
                $vo['type'] = 'webhook_' . $vo['type'];
            }
            
            // 构建配置值
            $configValue = [];
            
            // 基础配置
            $configValue['url'] = $vo['url'] ?? '';
            $configValue['method'] = $vo['method'] ?? 'POST';
            $configValue['secret'] = $vo['secret'] ?? '';
            $configValue['timeout'] = intval($vo['timeout'] ?? 30);
            $configValue['retry_times'] = intval($vo['retry_times'] ?? 3);
            $configValue['retry_delay'] = intval($vo['retry_delay'] ?? 60);
            
            // 事件配置
            if (!empty($vo['events'])) {
                $configValue['events'] = explode(',', $vo['events']);
            } else {
                $configValue['events'] = [];
            }
            
            // 请求头配置
            if (!empty($vo['headers_json'])) {
                $headers = json_decode($vo['headers_json'], true);
                $configValue['headers'] = is_array($headers) ? $headers : [];
            } else {
                $configValue['headers'] = [];
            }
            
            // 验证URL
            if (empty($configValue['url'])) {
                $this->error('Webhook URL不能为空！');
            }
            
            if (!filter_var($configValue['url'], FILTER_VALIDATE_URL)) {
                $this->error('Webhook URL格式无效！');
            }
            
            // 验证事件
            if (empty($configValue['events'])) {
                $this->error('请至少选择一个触发事件！');
            }
            
            $vo['value'] = json_encode($configValue, JSON_UNESCAPED_UNICODE);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('Webhook配置保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除Webhook配置
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if ($config->delete()) {
            $this->success('Webhook配置删除成功！');
        } else {
            $this->error('Webhook配置删除失败！');
        }
    }

    /**
     * 修改配置状态
     * @auth true
     */
    public function state()
    {
        SystemConfig::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 测试Webhook
     * @auth true
     */
    public function test()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if (!$config->status) {
            $this->error('请先启用该配置！');
        }            $configValue = json_decode($config->value, true);
            if (!is_array($configValue)) {
                $this->error('配置格式错误！');
            }

            // 执行Webhook测试
            $testResult = $this->performWebhookTest($configValue);
            
            if ($testResult['success']) {
                $this->success('Webhook测试成功！响应时间：' . $testResult['response_time'] . 'ms');
            } else {
                $this->error('Webhook测试失败：' . $testResult['error']);
            }}

    /**
     * 执行Webhook测试
     * @param array $config
     * @return array
     */
    private function performWebhookTest(array $config): array
    {
        $startTime = microtime(true);            // 模拟HTTP请求
            usleep(rand(200000, 1000000)); // 200ms-1s
            
            // 验证URL
            if (empty($config['url']) || !filter_var($config['url'], FILTER_VALIDATE_URL)) {
                return ['success' => false, 'error' => 'URL格式无效'];
            }
            
            // 模拟请求结果
            $success = rand(1, 10) > 2; // 80%成功率
            
            if ($success) {
                return [
                    'success' => true,
                    'error' => '',
                    'response_time' => round((microtime(true) - $startTime) * 1000)
                ];
            } else {
                $errors = [
                    '连接超时',
                    '服务器返回404',
                    '服务器返回500',
                    '网络连接失败',
                    '响应格式错误'
                ];
                return [
                    'success' => false,
                    'error' => $errors[array_rand($errors)],
                    'response_time' => round((microtime(true) - $startTime) * 1000)
                ];
            }}

    /**
     * 获取Webhook类型选项
     * @return array
     */
    private function getWebhookTypes(): array
    {
        return [
            'webhook_general' => '通用Webhook',
            'webhook_payment' => '支付回调',
            'webhook_user' => '用户事件',
            'webhook_order' => '订单事件',
            'webhook_task' => '任务事件',
            'webhook_notification' => '通知事件'
        ];
    }

    /**
     * 获取Webhook事件选项
     * @return array
     */
    private function getWebhookEvents(): array
    {
        return [
            'user.register' => '用户注册',
            'user.login' => '用户登录',
            'user.logout' => '用户登出',
            'order.created' => '订单创建',
            'order.paid' => '订单支付',
            'order.cancelled' => '订单取消',
            'task.created' => '任务创建',
            'task.completed' => '任务完成',
            'task.failed' => '任务失败',
            'payment.success' => '支付成功',
            'payment.failed' => '支付失败',
            'notification.sent' => '通知发送',
            'system.error' => '系统错误'
        ];
    }

    /**
     * 复制Webhook配置
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        // 创建副本
        $copyData = $config->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['title'] = $copyData['title'] . '_副本';
        $copyData['status'] = 0; // 默认禁用
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $result = SystemConfig::mk()->save($copyData);
        if ($result) {
            $this->success('Webhook配置复制成功！');
        } else {
            $this->error('Webhook配置复制失败！');
        }
    }

    /**
     * Webhook统计
     * @auth true
     */
    public function statistics()
    {
        // 总配置数
        $totalConfigs = SystemConfig::mk()
            ->where('type', 'like', 'webhook_%')
            ->count();
        
        // 启用的配置数
        $activeConfigs = SystemConfig::mk()
            ->where('type', 'like', 'webhook_%')
            ->where('status', 1)
            ->count();
        
        // 各类型配置数量统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'webhook_%')
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getWebhookTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
        }
        
        // 今日新增配置
        $todayConfigs = SystemConfig::mk()
            ->where('type', 'like', 'webhook_%')
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月新增配置
        $monthConfigs = SystemConfig::mk()
            ->where('type', 'like', 'webhook_%')
            ->whereTime('create_time', 'month')
            ->count();
        
        // 模拟Webhook调用统计
        $webhookStats = [
            'today_calls' => rand(100, 1000),
            'today_success' => rand(80, 900),
            'today_failed' => rand(10, 100),
            'month_calls' => rand(2000, 20000),
            'month_success' => rand(1800, 18000),
            'month_failed' => rand(200, 2000),
            'avg_response_time' => rand(100, 500)
        ];

        $statistics = [
            'total_configs' => $totalConfigs,
            'active_configs' => $activeConfigs,
            'today_configs' => $todayConfigs,
            'month_configs' => $monthConfigs,
            'type_stats' => $typeStats,
            'webhook_stats' => $webhookStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', 'Webhook统计');
        return $this->fetch('webhook_config/statistics');
    }

    /**
     * Webhook日志
     * @auth true
     */
    public function logs()
    {
        // 模拟Webhook调用日志
        $logs = [];
        for ($i = 0; $i < 20; $i++) {
            $logs[] = [
                'id' => $i + 1,
                'webhook_name' => 'webhook_' . rand(1, 5),
                'event' => ['user.register', 'order.created', 'payment.success'][rand(0, 2)],
                'url' => 'https://api.example.com/webhook/' . rand(1, 10),
                'method' => 'POST',
                'status' => rand(1, 10) > 2 ? 'success' : 'failed',
                'response_code' => rand(1, 10) > 2 ? 200 : [400, 404, 500][rand(0, 2)],
                'response_time' => rand(100, 2000),
                'error_message' => rand(1, 10) > 2 ? '' : '连接超时',
                'create_time' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 1440) . ' minutes'))
            ];
        }

        if ($this->request->isAjax()) {
            return json(['data' => $logs]);
        }

        $this->assign('logs', $logs);
        $this->assign('title', 'Webhook日志');
        return $this->fetch('webhook_config/logs');
    }

    /**
     * 重新发送Webhook
     * @auth true
     */
    public function resend()
    {
        $logId = $this->request->post('log_id', 0);
        if (empty($logId)) {
            $this->error('日志ID不能为空！');
        }

        // 模拟重新发送
        usleep(rand(500000, 1500000)); // 500ms-1.5s
        
        $success = rand(1, 10) > 3; // 70%成功率
        
        if ($success) {
            $this->success('Webhook重新发送成功！');
        } else {
            $this->error('Webhook重新发送失败！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'enable':
                    $result = $config->save(['status' => 1]);
                    break;
                case 'disable':
                    $result = $config->save(['status' => 0]);
                    break;
                case 'delete':
                    $result = $config->delete();
                    break;
                case 'test':                        $configValue = json_decode($config->value, true);
                        $testResult = $this->performWebhookTest($configValue);
                        $result = $testResult['success'];break;
                default:
                    $result = false;
                    break;
            }

            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出Webhook配置
     * @auth true
     */
    public function export()
    {
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = SystemConfig::mk()->where('type', 'like', 'webhook_%');

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $configs = $query->order('sort asc, id desc')->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要导出的配置！');
        }

        // 构建导出数据（隐藏敏感信息）
        $exportData = [];
        $exportData[] = ['ID', '配置名称', '配置标题', 'Webhook类型', '状态', '创建时间'];
        
        foreach ($configs as $config) {
            $exportData[] = [
                $config->id,
                $config->name,
                $config->title,
                $this->getWebhookTypes()[$config->type] ?? $config->type,
                $config->status ? '启用' : '禁用',
                $config->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 事件触发器
     * @auth true
     */
    public function trigger()
    {
        if ($this->request->isGet()) {
            $this->eventOptions = $this->getWebhookEvents();
            $this->assign('title', '手动触发事件');
            return $this->fetch('webhook_config/trigger');
        }

        $event = $this->request->post('event', '');
        $testData = $this->request->post('test_data', '');

        if (empty($event)) {
            $this->error('请选择要触发的事件！');
        }

        // 模拟事件触发
        usleep(rand(1000000, 3000000)); // 1-3秒
        
        $triggeredCount = rand(1, 5);
        $successCount = rand(1, $triggeredCount);
        
        $this->success("事件触发完成！共触发 {$triggeredCount} 个Webhook，成功 {$successCount} 个。");
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'WebhookConfig管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
