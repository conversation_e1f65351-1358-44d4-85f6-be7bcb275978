<?php

namespace app\admin\controller;

use app\admin\model\CheckRecordModel;
use think\admin\Controller;

/**
 * 查重记录管理
 * @class CheckRecord
 * @package app\admin\controller
 */
class CheckRecord extends Controller
{
    /**
     * 查重记录管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        CheckRecordModel::mQuery($this)->layPage(function () {
            $this->title = '查重记录管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加查重记录管理
     * @auth true
     */
    public function add()
    {
        CheckRecordModel::mForm('check_record/form');
    }

    /**
     * 编辑查重记录管理
     * @auth true
     */
    public function edit()
    {
        CheckRecordModel::mForm('check_record/form');
    }

    /**
     * 删除查重记录管理
     * @auth true
     */
    public function remove()
    {
        CheckRecordModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        CheckRecordModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}