<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card" data-table-id="BaseTable">

    <div class="layui-card-body padding-left-40">

        <div class="layui-form-item label-required-prev">
            <div class="help-label"><b>数据类型</b>Data Type</div>
            {if isset($vo.type)}
            <label><input readonly value="{$vo.type|default=''}" class="layui-input think-bg-gray"></label>
            {else}
            <select class="layui-select" lay-filter="DataType">
                {foreach $types as $type}{if (isset($vo.type) and $type eq $vo.type) or ($type eq input('get.type'))}
                <option selected value="{$type}">{$type}</option>
                {else}
                <option value="{$type}">{$type}</option>
                {/if}{/foreach}
            </select>
            <script>
                (function (callable) {
                    layui.form.on('select(DataType)', callable);
                    callable({value: "{$vo.type|default=''}" || $('[lay-filter=DataType]').val()});
                })(function (data) {
                    if (data.value === '--- 新增类型 ---') {
                        $('#DataTypeInput').removeClass('layui-hide').find('input').val('').focus();
                    } else {
                        $('#DataTypeInput').addClass('layui-hide').find('input').val(data.value);
                    }
                });
            </script>
            {/if}
            <p class="help-block">请选择数据类型，数据创建后不能再次修改哦 ~</p>
            <div id="DataTypeInput" class="layui-hide relative">
                <input class="layui-input" maxlength="20" name="type" required vali-name="数据类型" placeholder="请输入数据类型" value="{$vo.type|default=''}">
                <p class="help-block">请输入新的数据类型，数据创建后不能再次修改哦 ~</p>
            </div>
        </div>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>数据编码</b>Data Code</span>
            {if isset($vo.code)}
            <input readonly maxlength="100" class="layui-input think-bg-gray" name="code" value='{$vo.code|default=""}' required placeholder="请输入数据编码">
            {else}
            <input maxlength="100" class="layui-input" name="code" value='{$vo.code|default=""}' required vali-name="数据编码" placeholder="请输入数据编码">
            {/if}
            <span class="help-block">请输入新的数据编码，数据创建后不能再次修改，同种数据类型的数据编码不能出现重复 ~</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>数据名称</b>Data Name</span>
            <input maxlength="500" class="layui-input" name="name" value='{$vo.name|default=""}' required vali-name="数据名称" placeholder="请输入数据名称">
            <span class="help-block">请输入当前数据名称，请尽量保持名称的唯一性，数据名称尽量不要出现重复 ~</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>数据内容</b>Data Content</span>
            <textarea name="content" class="layui-textarea" placeholder="请输入数据内容">{$vo.content|default=''}</textarea>
        </label>

    </div>

    <div class="hr-line-dashed"></div>
    {notempty name='vo.id'}<input type='hidden' value='{$vo.id}' name='id'>{/notempty}

    <div class="layui-form-item text-center">
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>

</form>