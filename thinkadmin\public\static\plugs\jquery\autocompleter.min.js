/**
 * jquery-autocompleter v0.3.0 - 2018-03-08
 * Easy customisable and with localStorage cache support.
 * http://github.com/ArtemFitiskin/jquery-autocompleter
 *
 * @license (c) 2018 Artem Fitiskin MIT Licensed
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t():"function"==typeof define&&define.amd?define(t):t()}(0,function(){"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(t,o){var a=0,l=[9,13,17,19,20,27,33,34,35,36,37,39,44,92,113,114,115,118,119,120,122,123,144,145],n=["source","empty","limit","cache","cacheExpires","focusOpen","selectFirst","changeWhenSelect","highlightMatches","ignoredKeyCode","customLabel","customValue","template","offset","combine","callback","minLength","delay"],r=o.navigator.userAgent||o.navigator.vendor||o.opera,c=/Firefox/i.test(r),u=/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(r),i=c&&u,s=null,p=null,m="autocompleterCache",d=function(){var e=void 0!==o.localStorage;if(e)try{localStorage.setItem("autocompleter","autocompleter"),localStorage.removeItem("autocompleter")}catch(t){e=!1}return e}(),f={source:null,asLocal:!1,empty:!0,limit:10,minLength:0,delay:0,customClass:[],cache:!0,cacheExpires:86400,focusOpen:!0,hint:!1,selectFirst:!1,changeWhenSelect:!0,highlightMatches:!1,ignoredKeyCode:[],customLabel:!1,customValue:!1,template:!1,offset:!1,combine:null,callback:t.noop},h={defaults:function(o){return f=t.extend(f,o||{}),"object"!==e(this)||t(this)},option:function(e){return t(this).each(function(o,a){var l=t(a).next(".autocompleter").data("autocompleter");for(var r in e)-1!==t.inArray(r,n)&&(l[r]=e[r])})},open:function(){return t(this).each(function(e,o){var a=t(o).next(".autocompleter").data("autocompleter");a&&S(null,a)})},close:function(){return t(this).each(function(e,o){var a=t(o).next(".autocompleter").data("autocompleter");a&&k(null,a)})},clearCache:function(){O()},destroy:function(){return t(this).each(function(e,o){var a=t(o).next(".autocompleter").data("autocompleter");a&&(a.jqxhr&&a.jqxhr.abort(),a.$autocompleter.hasClass("open")&&a.$autocompleter.find(".autocompleter-selected").trigger("click.autocompleter"),a.originalAutocomplete?a.$node.attr("autocomplete",a.originalAutocomplete):a.$node.removeAttr("autocomplete"),a.$node.off(".autocompleter").removeClass("autocompleter-node"),a.$autocompleter.off(".autocompleter").remove())})}};function v(e,o){if(!e.hasClass("autocompleter-node")){"string"!=typeof(o=t.extend({},o,e.data("autocompleter-options"))).source||".json"!==o.source.slice(-5)&&!0!==o.asLocal||t.ajax({url:o.source,type:"GET",dataType:"json",async:!1}).done(function(e){o.source=e});var l='<div class="autocompleter '+o.customClass.join(" ")+'" id="autocompleter-'+(a+1)+'">';o.hint&&(l+='<div class="autocompleter-hint"></div>'),l+='<ul class="autocompleter-list"></ul>',l+="</div>",e.addClass("autocompleter-node").after(l);var n=e.next(".autocompleter").eq(0),r=e.attr("autocomplete");e.attr("autocomplete","off");var c=t.extend({$node:e,$autocompleter:n,$selected:null,$list:null,index:-1,hintText:!1,source:!1,jqxhr:!1,response:null,focused:!1,query:"",originalAutocomplete:r,guid:a++},o);c.$autocompleter.on("mousedown.autocompleter",".autocompleter-item",c,E).data("autocompleter",c),c.$node.on("keyup.autocompleter",c,x).on("keydown.autocompleter",c,C).on("focus.autocompleter",c,w).on("blur.autocompleter",c,q).on("mousedown.autocompleter",c,j)}}function y(e){clearTimeout(p),e.query=t.trim(e.$node.val()),!e.empty&&0===e.query.length||e.minLength&&e.query.length<e.minLength?g(e):e.delay?p=setTimeout(function(){$(e)},e.delay):$(e)}function $(o){if("object"===e(o.source)){g(o);var a=function(e,t,o){var a=[];if(e=e.toUpperCase(),t.length)for(var l=0;l<2;l++)for(var n in t)if(a.length<o.limit){var r=o.customLabel&&t[n][o.customLabel]?t[n][o.customLabel]:t[n].label;switch(l){case 0:0===r.toUpperCase().search(e)&&(a.push(t[n]),delete t[n]);break;case 1:-1!==r.toUpperCase().search(e)&&(a.push(t[n]),delete t[n])}}return a}(o.query,function(t){var o;if(null===t||"object"!==(void 0===t?"undefined":e(t)))return t;for(var a in o=t.constructor(),t)t.hasOwnProperty(a)&&(o[a]=t[a]);return o}(o.source),o);a.length&&b(a,o)}else{o.jqxhr&&o.jqxhr.abort();var l={limit:o.limit,query:o.query};"function"==typeof o.combine&&(l=o.combine(l)),o.jqxhr=t.ajax({url:o.source,dataType:"json",crossDomain:!0,data:l,beforeSend:function(e){if(o.$autocompleter.addClass("autocompleter-ajax"),g(o),o.cache){var t=function(e,t){var o,a=!1;if(t=t||!1,!e)return!1;return!(!(o=D[e])||!o.value)&&(a=o.value,!(o.timestamp&&t&&+new Date-o.timestamp>1e3*t)&&a)}(this.url,o.cacheExpires);t&&(e.abort(),b(t,o))}}}).done(function(e){o.offset&&(e=function(e,t){t=t.split(".");for(;e&&t.length;)e=e[t.shift()];return e}(e,o.offset)),o.cache&&function(e,t){if(!d)return;if(e&&t){D[e]={value:t,timestamp:+new Date};try{localStorage.setItem(m,JSON.stringify(D))}catch(e){var o=e.code||e.number||e.message;if(22!==o)throw e;O()}}}(this.url,e),b(e,o)}).always(function(){o.$autocompleter.removeClass("autocompleter-ajax")})}}function g(e){e.response=null,e.$list=null,e.$selected=null,e.index=0,e.$autocompleter.find(".autocompleter-list").empty(),e.$autocompleter.find(".autocompleter-hint").removeClass("autocompleter-hint-show").empty(),e.hintText=!1,k(null,e)}function b(e,o){!function(e,o){for(var a="",l=0,n=e.length;l<n;l++){var r=["autocompleter-item"],c=new RegExp(o.query,"gi");o.selectFirst&&0===l&&!o.changeWhenSelect&&r.push("autocompleter-item-selected");var u=o.customLabel&&e[l][o.customLabel]?e[l][o.customLabel]:e[l].label,i=u;u=o.highlightMatches?u.replace(c,"<strong>$&</strong>"):u;var s=o.customValue&&e[l][o.customValue]?e[l][o.customValue]:e[l].value;if(o.template){var p=o.template.replace(/({{ label }})/gi,u);for(var m in e[l])if(e[l].hasOwnProperty(m)){var d=new RegExp("{{ "+m+" }}","gi");p=p.replace(d,e[l][m])}u=p}a+=s?'<li data-value="'+s+'" data-label="'+i+'" class="'+r.join(" ")+'">'+u+"</li>":'<li data-label="'+i+'" class="'+r.join(" ")+'">'+u+"</li>"}if(e.length&&o.hint){var f=o.customLabel&&e[0][o.customLabel]?e[0][o.customLabel]:e[0].label,h=f.substr(0,o.query.length).toUpperCase()===o.query.toUpperCase()&&f;if(h&&o.query!==f){var v=new RegExp(o.query,"i"),y=h.replace(v,"<span>"+o.query+"</span>");o.$autocompleter.find(".autocompleter-hint").addClass("autocompleter-hint-show").html(y),o.hintText=y}}o.response=e,o.$autocompleter.find(".autocompleter-list").html(a),o.$selected=o.$autocompleter.find(".autocompleter-item-selected").length?o.$autocompleter.find(".autocompleter-item-selected"):null,o.$list=e.length?o.$autocompleter.find(".autocompleter-item"):null,o.index=o.$selected?o.$list.index(o.$selected):-1,o.$autocompleter.find(".autocompleter-item").each(function(e,a){t(a).data(o.response[e])})}(e,o),o.$autocompleter.hasClass("autocompleter-focus")&&S(null,o)}function x(e){var o=e.data,a=e.keyCode?e.keyCode:e.which;if(40!==a&&38!==a||!o.$autocompleter.hasClass("autocompleter-show"))-1===t.inArray(a,l)&&-1===t.inArray(a,o.ignoredKeyCode)&&y(o);else{var n,r,c=o.$list.length;c&&(c>1?o.index===c-1?(n=o.changeWhenSelect?-1:0,r=o.index-1):0===o.index?(n=o.index+1,r=o.changeWhenSelect?-1:c-1):-1===o.index?(n=0,r=c-1):(n=o.index+1,r=o.index-1):-1===o.index?(n=0,r=0):(r=-1,n=-1),o.index=40===a?n:r,o.$list.removeClass("autocompleter-item-selected"),-1!==o.index&&o.$list.eq(o.index).addClass("autocompleter-item-selected"),o.$selected=o.$autocompleter.find(".autocompleter-item-selected").length?o.$autocompleter.find(".autocompleter-item-selected"):null,o.changeWhenSelect&&A(o))}}function C(e){var t=e.data,o=e.keyCode?e.keyCode:e.which;if(40===o||38===o)e.preventDefault(),e.stopPropagation();else if(39===o){if(t.hint&&t.hintText&&t.$autocompleter.find(".autocompleter-hint").hasClass("autocompleter-hint-show")){e.preventDefault(),e.stopPropagation();var a=!!t.$autocompleter.find(".autocompleter-item").length&&t.$autocompleter.find(".autocompleter-item").eq(0).attr("data-label");a&&(t.query=a,function(e){A(e),T(e),y(e)}(t))}}else 13===o&&t.$autocompleter.hasClass("autocompleter-show")&&t.$selected&&E(e)}function w(e,t){if(!t){var o=e.data;o.$autocompleter.addClass("autocompleter-focus"),o.$node.prop("disabled")||o.$autocompleter.hasClass("autocompleter-show")||o.focusOpen&&(y(o),o.focused=!0,setTimeout(function(){o.focused=!1},500))}}function q(e,t){e.preventDefault(),e.stopPropagation();var o=e.data;t||(o.$autocompleter.removeClass("autocompleter-focus"),k(e))}function j(e){if("mousedown"!==e.type||-1===t.inArray(e.which,[2,3])){var a=e.data;if(a.$list&&!a.focused&&!a.$node.is(":disabled"))if(u&&!i){var l=a.$select[0];if(o.document.createEvent){var n=o.document.createEvent("MouseEvents");n.initMouseEvent("mousedown",!1,!0,o,0,0,0,0,0,!1,!1,!1,!1,0,null),l.dispatchEvent(n)}else l.fireEvent&&l.fireEvent("onmousedown")}else a.$autocompleter.hasClass("autocompleter-closed")?S(e):a.$autocompleter.hasClass("autocompleter-show")&&k(e)}}function S(e,t){var o=e?e.data:t;!o.$node.prop("disabled")&&!o.$autocompleter.hasClass("autocompleter-show")&&o.$list&&o.$list.length&&(o.$autocompleter.removeClass("autocompleter-closed").addClass("autocompleter-show"),s.on("click.autocompleter-"+o.guid,":not(.autocompleter-item)",o,L))}function L(e){t(e.target).hasClass("autocompleter-node")||0===t(e.currentTarget).parents(".autocompleter").length&&k(e)}function k(e,t){var o=e?e.data:t;o.$autocompleter.hasClass("autocompleter-show")&&(o.$autocompleter.removeClass("autocompleter-show").addClass("autocompleter-closed"),s.off(".autocompleter-"+o.guid))}function E(e){if("mousedown"!==e.type||-1===t.inArray(e.which,[2,3])){var o=e.data;e.preventDefault(),e.stopPropagation(),"mousedown"===e.type&&t(this).length&&(o.$selected=t(this),o.index=o.$list.index(o.$selected)),o.$node.prop("disabled")||(k(e),function(e){A(e),T(e),g(e)}(o),"click"===e.type&&o.$node.trigger("focus",[!0]))}}function A(e){e.$selected?(e.hintText&&e.$autocompleter.find(".autocompleter-hint").hasClass("autocompleter-hint-show")&&e.$autocompleter.find(".autocompleter-hint").removeClass("autocompleter-hint-show"),e.$node.val(e.$selected.attr("data-value")?e.$selected.attr("data-value"):e.$selected.attr("data-label"))):(e.hintText&&!e.$autocompleter.find(".autocompleter-hint").hasClass("autocompleter-hint-show")&&e.$autocompleter.find(".autocompleter-hint").addClass("autocompleter-hint-show"),e.$node.val(e.query))}function T(e){e.callback.call(e.$autocompleter,e.$node.val(),e.index,e.response[e.index]),e.$node.trigger("change")}function P(){if(d)return JSON.parse(localStorage.getItem(m)||"{}")}function O(){try{localStorage.removeItem(m),D=P()}catch(e){throw e}}var D=P();t.fn.autocompleter=function(o){return h[o]?h[o].apply(this,Array.prototype.slice.call(arguments,1)):"object"!==(void 0===o?"undefined":e(o))&&o?this:function(e){e=t.extend({},f,e||{}),null===s&&(s=t("body"));for(var o=t(this),a=0,l=o.length;a<l;a++)v(o.eq(a),e);return o}.apply(this,arguments)},t.autocompleter=function(e){"defaults"===e?h.defaults.apply(this,Array.prototype.slice.call(arguments,1)):"clearCache"===e&&h.clearCache.apply(this,null)}}(jQuery,window)});
