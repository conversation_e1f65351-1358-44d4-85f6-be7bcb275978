<?php

// +----------------------------------------------------------------------
// | N8N Execution Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * n8n执行记录模型
 * @class N8nExecution
 * @package app\admin\model
 */
class N8nExecution extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'n8n_execution';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['input_data', 'output_data'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'workflow_id' => 'integer',
        'duration' => 'integer',
        'input_data' => 'array',
        'output_data' => 'array'
    ];

    /**
     * 获取器 - 输入数据
     * @param $value
     * @return array
     */
    public function getInputDataAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 输入数据
     * @param $value
     * @return string
     */
    public function setInputDataAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取器 - 输出数据
     * @param $value
     * @return array
     */
    public function getOutputDataAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 输出数据
     * @param $value
     * @return string
     */
    public function setOutputDataAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 关联工作流
     * @return \think\model\relation\BelongsTo
     */
    public function workflow()
    {
        return $this->belongsTo(N8nWorkflow::class, 'workflow_id', 'id');
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            'running' => '运行中',
            'success' => '成功',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取执行时长文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getDurationTextAttr($value, $data)
    {
        if (empty($data['duration'])) {
            return '-';
        }
        
        $duration = $data['duration'];
        if ($duration < 1000) {
            return $duration . 'ms';
        } elseif ($duration < 60000) {
            return round($duration / 1000, 2) . 's';
        } else {
            return round($duration / 60000, 2) . 'min';
        }
    }

    /**
     * 搜索器 - 执行ID
     * @param $query
     * @param $value
     */
    public function searchExecutionIdAttr($query, $value)
    {
        $query->whereLike('execution_id', "%{$value}%");
    }

    /**
     * 搜索器 - 工作流ID
     * @param $query
     * @param $value
     */
    public function searchWorkflowIdAttr($query, $value)
    {
        $query->where('workflow_id', $value);
    }

    /**
     * 搜索器 - 状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }
}
