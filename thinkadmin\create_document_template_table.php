<?php

/**
 * 手动创建document_template表
 */

echo "=== 创建document_template表 ===\n\n";

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在: {$dbPath}\n";
        exit;
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 检查表是否已存在
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='document_template'");
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "⚠️  表 document_template 已存在\n";
    } else {
        echo "创建 document_template 表...\n";
        
        // 创建表的SQL
        $sql = "
        CREATE TABLE document_template (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL DEFAULT '',
            type VARCHAR(50) NOT NULL DEFAULT '',
            paper_type_id INTEGER NULL,
            template_content TEXT NOT NULL,
            style_config TEXT NULL,
            is_default INTEGER NOT NULL DEFAULT 0,
            usage_count INTEGER NOT NULL DEFAULT 0,
            status INTEGER NOT NULL DEFAULT 1,
            create_time DATETIME NULL,
            update_time DATETIME NULL
        )";
        
        $pdo->exec($sql);
        echo "✅ 表创建成功\n";
        
        // 创建索引
        $indexes = [
            "CREATE INDEX idx_document_template_type ON document_template(type)",
            "CREATE INDEX idx_document_template_paper_type_id ON document_template(paper_type_id)",
            "CREATE INDEX idx_document_template_status ON document_template(status)",
            "CREATE INDEX idx_document_template_create_time ON document_template(create_time)"
        ];
        
        foreach ($indexes as $index) {
            $pdo->exec($index);
        }
        echo "✅ 索引创建成功\n";
    }
    
    // 插入测试数据
    echo "\n插入测试数据...\n";
    
    $testData = [
        [
            'name' => '学术论文正文模板',
            'type' => 'content',
            'paper_type_id' => 1,
            'template_content' => '<div class="paper-content"><h1>{{title}}</h1><div class="abstract">{{abstract}}</div><div class="content">{{content}}</div></div>',
            'style_config' => '{"font_family":"宋体","font_size":"12pt","line_height":"1.5","margin":"2.5cm"}',
            'is_default' => 1,
            'status' => 1,
            'create_time' => date('Y-m-d H:i:s')
        ],
        [
            'name' => '毕业论文正文模板',
            'type' => 'content',
            'paper_type_id' => 2,
            'template_content' => '<div class="thesis-content"><h1>{{title}}</h1><div class="chapter">{{content}}</div></div>',
            'style_config' => '{"font_family":"宋体","font_size":"12pt","line_height":"1.5","margin":"3cm"}',
            'is_default' => 0,
            'status' => 1,
            'create_time' => date('Y-m-d H:i:s')
        ],
        [
            'name' => '通用正文模板',
            'type' => 'content',
            'paper_type_id' => 0,
            'template_content' => '<div class="generic-content">{{content}}</div>',
            'style_config' => '{"font_family":"宋体","font_size":"12pt","line_height":"1.5"}',
            'is_default' => 0,
            'status' => 1,
            'create_time' => date('Y-m-d H:i:s')
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO document_template (name, type, paper_type_id, template_content, style_config, is_default, status, create_time)
        VALUES (:name, :type, :paper_type_id, :template_content, :style_config, :is_default, :status, :create_time)
    ");
    
    foreach ($testData as $data) {
        $stmt->execute($data);
    }
    
    echo "✅ 已插入 " . count($testData) . " 条测试数据\n";
    
    // 验证数据
    echo "\n验证数据...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_template WHERE type = 'content'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "正文模板数量: {$result['count']}\n";
    
    $stmt = $pdo->query("SELECT id, name, paper_type_id, is_default FROM document_template WHERE type = 'content'");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "  - ID: {$row['id']}, 名称: {$row['name']}, 类型ID: {$row['paper_type_id']}, 默认: {$row['is_default']}\n";
    }
    
    echo "\n✅ document_template表创建完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 创建完成 ===\n";
