<?php /*a:2:{s:86:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\paper_project\index.html";i:1754096479;s:72:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\table.html";i:1754094170;}*/ ?>
<div class="layui-card"><?php if(!(empty($title) || (($title instanceof \think\Collection || $title instanceof \think\Paginator ) && $title->isEmpty()))): ?><div class="layui-card-header"><span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span><?php echo htmlentities((string) lang($title)); ?><div class="pull-right"><!--<?php if(auth("add")): ?>--><button data-table-id="PaperProjectTable" data-modal='<?php echo url("add"); ?>' class='layui-btn layui-btn-sm layui-btn-primary'>创建论文项目</button><!--<?php endif; ?>--><!--<?php if(auth("remove")): ?>--><button data-table-id="PaperProjectTable" data-action='<?php echo url("remove"); ?>' data-rule="id#{id}" data-confirm="确定要批量删除论文项目吗？" class='layui-btn layui-btn-sm layui-btn-primary'>批量删除</button><!--<?php endif; ?>--></div></div><?php endif; ?><div class="layui-card-line"></div><div class="layui-card-body"><div class="layui-card-table"><?php if(!(empty($showErrorMessage) || (($showErrorMessage instanceof \think\Collection || $showErrorMessage instanceof \think\Paginator ) && $showErrorMessage->isEmpty()))): ?><div class="think-box-notify" type="error"><b><?php echo lang('系统提示：'); ?></b><span><?php echo $showErrorMessage; ?></span></div><?php endif; ?><div class="layui-card"><div class="layui-card-body"><form class="layui-form layui-form-pane form-search" action="<?php echo request()->url(); ?>" onsubmit="return false"><div class="layui-form-item layui-inline"><label class="layui-form-label">项目标题</label><div class="layui-input-inline"><input name="title" value="<?php echo htmlentities((string) (isset($get['title']) && ($get['title'] !== '')?$get['title']:'')); ?>" placeholder="请输入项目标题" class="layui-input"></div></div><div class="layui-form-item layui-inline"><label class="layui-form-label">论文类型</label><div class="layui-input-inline"><select name="paper_type_id" class="layui-select"><option value="">全部类型</option><?php foreach($paperTypes as $k=>$v): if(isset($get['paper_type_id']) and $get['paper_type_id'] == $k): ?><option selected value="<?php echo htmlentities((string) $k); ?>"><?php echo htmlentities((string) $v); ?></option><?php else: ?><option value="<?php echo htmlentities((string) $k); ?>"><?php echo htmlentities((string) $v); ?></option><?php endif; ?><?php endforeach; ?></select></div></div><div class="layui-form-item layui-inline"><label class="layui-form-label">项目状态</label><div class="layui-input-inline"><select name="status" class="layui-select"><option value="">全部状态</option><option value="draft" <?php if(isset($get['status']) and $get['status'] == 'draft'): ?>selected<?php endif; ?>>草稿</option><option value="outline" <?php if(isset($get['status']) and $get['status'] == 'outline'): ?>selected<?php endif; ?>>大纲生成中</option><option value="writing" <?php if(isset($get['status']) and $get['status'] == 'writing'): ?>selected<?php endif; ?>>写作中</option><option value="completed" <?php if(isset($get['status']) and $get['status'] == 'completed'): ?>selected<?php endif; ?>>已完成</option><option value="failed" <?php if(isset($get['status']) and $get['status'] == 'failed'): ?>selected<?php endif; ?>>失败</option></select></div></div><div class="layui-form-item layui-inline"><button class="layui-btn layui-btn-primary" type="submit">搜 索</button></div></form><table id="PaperProjectTable" data-url="<?php echo request()->url(); ?>" data-target-search="form.form-search"></table></div></div></div></div><script>
    $(function () {
        // 初始化表格组件
        $('#PaperProjectTable').layTable({
            even: true, height: 'full',
            sort: {field: 'id', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'title', title: '项目标题', width: '25%', align: 'left'},
                {field: 'paper_type.name', title: '论文类型', width: 120, align: 'center'},
                {field: 'word_count_progress', title: '字数进度', width: 150, align: 'center'},
                {field: 'progress_text', title: '完成进度', width: 100, align: 'center'},
                {field: 'status_text', title: '状态', width: 100, align: 'center', templet: '#StatusTpl'},
                {field: 'create_time', title: '创建时间', width: 170, align: 'center', sort: true},
                {toolbar: '#toolbar', align: 'center', width: 200, title: '操作', fixed: 'right'},
            ]]
        });
    });
</script><!-- 状态显示模板 --><script type="text/html" id="StatusTpl">
    {{# if(d.status === 'draft') { }}
        <span class="layui-badge layui-bg-gray">草稿</span>
    {{# } else if(d.status === 'outline') { }}
        <span class="layui-badge layui-bg-blue">大纲生成中</span>
    {{# } else if(d.status === 'writing') { }}
        <span class="layui-badge layui-bg-orange">写作中</span>
    {{# } else if(d.status === 'completed') { }}
        <span class="layui-badge layui-bg-green">已完成</span>
    {{# } else if(d.status === 'failed') { }}
        <span class="layui-badge layui-bg-red">失败</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未知</span>
    {{# } }}
</script><!-- 数据操作工具条模板 --><script type="text/html" id="toolbar"><!--<?php if(auth('edit')): ?>--><a class="layui-btn layui-btn-primary layui-btn-sm" data-event-dbclick data-title="编辑项目" data-modal='<?php echo url("edit"); ?>?id={{d.id}}'>编辑</a><!--<?php endif; ?>--><!--<?php if(auth('generateOutline')): ?>-->
    {{# if(d.status === 'draft') { }}
    <a class="layui-btn layui-btn-sm" data-confirm="确定要生成大纲吗？" data-action="<?php echo url('generateOutline'); ?>" data-value="id#{{d.id}}">生成大纲</a>
    {{# } }}
    <!--<?php endif; ?>--><!--<?php if(auth('startWriting')): ?>-->
    {{# if(d.status === 'outline') { }}
    <a class="layui-btn layui-btn-warm layui-btn-sm" data-confirm="确定要开始写作吗？" data-action="<?php echo url('startWriting'); ?>" data-value="id#{{d.id}}">开始写作</a>
    {{# } }}
    <!--<?php endif; ?>--><!--<?php if(auth("remove")): ?>--><a class="layui-btn layui-btn-danger layui-btn-sm" data-confirm="确定要删除该项目吗?" data-action="<?php echo url('remove'); ?>" data-value="id#{{d.id}}">删除</a><!--<?php endif; ?>--></script></div>