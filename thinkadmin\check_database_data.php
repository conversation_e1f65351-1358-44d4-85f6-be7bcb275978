<?php

/**
 * 检查数据库数据
 */

echo "=== 检查数据库数据 ===\n\n";

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在: {$dbPath}\n";
        exit;
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 检查document_template表
    echo "1. 检查document_template表:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_template WHERE type = 'content'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "正文模板数量: {$result['count']}\n";
    
    if ($result['count'] == 0) {
        echo "没有正文模板数据，创建测试数据...\n";
        
        // 插入测试数据
        $sql = "INSERT INTO document_template (name, type, template_content, paper_type_id, status, is_default, usage_count, create_time) VALUES 
                ('学术论文正文模板', 'content', '<h1>{{title}}</h1><p>{{content}}</p>', 1, 1, 1, 0, datetime('now')),
                ('毕业论文正文模板', 'content', '<h1>{{title}}</h1><div>{{content}}</div>', 2, 1, 0, 0, datetime('now')),
                ('通用正文模板', 'content', '<div class=\"content\">{{content}}</div>', 0, 1, 0, 0, datetime('now'))";
        
        $pdo->exec($sql);
        echo "✅ 已创建3个测试模板\n";
    } else {
        // 显示现有数据
        $stmt = $pdo->query("SELECT id, name, paper_type_id, status, is_default FROM document_template WHERE type = 'content' LIMIT 5");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "  - ID: {$row['id']}, 名称: {$row['name']}, 类型ID: {$row['paper_type_id']}, 状态: {$row['status']}\n";
        }
    }
    
    // 检查paper_type表
    echo "\n2. 检查paper_type表:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM paper_type WHERE status = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "启用的论文类型数量: {$result['count']}\n";
    
    if ($result['count'] == 0) {
        echo "没有论文类型数据，创建测试数据...\n";
        
        // 插入测试数据
        $sql = "INSERT INTO paper_type (name, description, status, sort, create_time) VALUES 
                ('学术论文', '学术研究论文', 1, 100, datetime('now')),
                ('毕业论文', '本科/研究生毕业论文', 1, 200, datetime('now')),
                ('期刊论文', '期刊发表论文', 1, 300, datetime('now'))";
        
        $pdo->exec($sql);
        echo "✅ 已创建3个论文类型\n";
    } else {
        // 显示现有数据
        $stmt = $pdo->query("SELECT id, name, status FROM paper_type WHERE status = 1 LIMIT 5");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "  - ID: {$row['id']}, 名称: {$row['name']}, 状态: {$row['status']}\n";
        }
    }
    
    echo "\n✅ 数据库检查完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
