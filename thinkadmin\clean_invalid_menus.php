<?php

// 清理无效菜单项
$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 清理无效菜单项 ===\n\n";
    
    // 需要删除的无效菜单项（没有对应控制器的）
    $invalidMenuNodes = [
        'admin/content_filter/index',
        'admin/webhook_config/index', 
        'admin/api_key/index',
        'admin/notification_log/index',
        'admin/email_config/index',
        'admin/system_notice/index',
        'admin/recharge_record/index',
        'admin/package_config/index',
        'admin/order/index',
        'admin/user_credit/index',
        'admin/vip_package/index',
        'admin/export_record/index',
        'admin/document_template/index',
        'admin/check_api/index',
        'admin/check_task/index',
        'admin/rewrite_result/index'
    ];
    
    foreach ($invalidMenuNodes as $node) {
        $stmt = $pdo->prepare("DELETE FROM system_menu WHERE node = ?");
        $result = $stmt->execute([$node]);
        $deletedCount = $pdo->lastInsertId() ? 0 : $stmt->rowCount();
        echo "删除菜单项: {$node} (删除 {$deletedCount} 条记录)\n";
    }
    
    // 删除空的父菜单（没有子菜单的一级菜单）
    echo "\n=== 清理空的父菜单 ===\n";
    
    // 查找没有子菜单的一级菜单
    $stmt = $pdo->query("
        SELECT p.id, p.title 
        FROM system_menu p 
        WHERE p.pid = 0 
        AND NOT EXISTS (
            SELECT 1 FROM system_menu c WHERE c.pid = p.id AND c.status = 1
        )
        AND p.node = ''
    ");
    $emptyParents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($emptyParents as $parent) {
        $stmt = $pdo->prepare("DELETE FROM system_menu WHERE id = ?");
        $stmt->execute([$parent['id']]);
        echo "删除空父菜单: {$parent['title']} (ID: {$parent['id']})\n";
    }
    
    echo "\n=== 清理完成 ===\n";
    echo "已删除所有无效菜单项\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
