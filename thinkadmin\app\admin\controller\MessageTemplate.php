<?php

// +----------------------------------------------------------------------
// | Message Template Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\MessageTemplate as MessageTemplateModel;

/**
 * 消息模板管理
 * @class MessageTemplate
 * @package app\admin\controller
 */
class MessageTemplate extends Controller
{
    /**
     * 消息模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        MessageTemplateModel::mQuery()->layTable(function () {
            $this->title = '消息模板管理';
        }, static function (QueryHelper $query) {
            $query->like('name,code,subject')->equal('type,status');
            $query->dateBetween('create_time');
            $query->order('type asc,id desc');
        });
    }

    /**
     * 添加消息模板
     * @auth true
     */
    public function add()
    {
        MessageTemplateModel::mForm('message_template/form');
    }

    /**
     * 编辑消息模板
     * @auth true
     */
    public function edit()
    {
        MessageTemplateModel::mForm('message_template/form');
    }

    /**
     * 查看消息模板详情
     * @auth true
     */
    public function view()
    {
        MessageTemplateModel::mForm('message_template/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 模板类型选项
            $this->typeOptions = [
                'system' => '系统通知',
                'email' => '邮件通知',
                'sms' => '短信通知',
                'wechat' => '微信通知'
            ];
            
            // 状态选项
            $this->statusOptions = [
                0 => '禁用',
                1 => '启用'
            ];
            
            // 常用变量说明
            $this->variableHelp = [
                '{username}' => '用户名',
                '{title}' => '标题',
                '{content}' => '内容',
                '{amount}' => '金额',
                '{order_no}' => '订单号',
                '{task_type}' => '任务类型',
                '{task_title}' => '任务标题',
                '{credits}' => '积分数量',
                '{version}' => '版本号',
                '{invoice_number}' => '发票号码',
                '{error_message}' => '错误信息',
                '{url}' => '链接地址',
                '{date}' => '日期',
                '{time}' => '时间'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
            }
            $data['update_time'] = date('Y-m-d H:i:s');
            
            // 处理变量定义JSON
            if (!empty($data['variables']) && is_array($data['variables'])) {
                $data['variables'] = json_encode($data['variables'], JSON_UNESCAPED_UNICODE);
            }
            
            // 验证模板代码唯一性
            if (!empty($data['code'])) {
                $exists = MessageTemplateModel::mk()
                    ->where(['code' => $data['code']])
                    ->where('id', '<>', $data['id'] ?? 0)
                    ->findOrEmpty();
                if (!$exists->isEmpty()) {
                    $this->error('模板代码已存在！');
                }
            }
        }
    }

    /**
     * 修改模板状态
     * @auth true
     */
    public function state()
    {
        MessageTemplateModel::mSave($this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除消息模板
     * @auth true
     */
    public function remove()
    {
        MessageTemplateModel::mDelete();
    }

    /**
     * 复制模板
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = MessageTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        $copyData = $template->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . ' - 副本';
        $copyData['code'] = $copyData['code'] . '_copy_' . time();
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');
        
        MessageTemplateModel::mk()->save($copyData);
        $this->success('模板复制成功！');
    }

    /**
     * 预览模板
     * @auth true
     */
    public function preview()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = MessageTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        $this->template = $template;
        $this->variables = !empty($template['variables']) ? json_decode($template['variables'], true) : [];
        
        $this->fetch('message_template/preview');
    }

    /**
     * 测试发送
     * @auth true
     */
    public function test()
    {
        $data = $this->_vali([
            'id.require' => '模板ID不能为空！',
            'test_data' => '测试数据',
        ]);
        
        $template = MessageTemplateModel::mk()->findOrEmpty($data['id']);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        if ($template['status'] != 1) {
            $this->error('模板未启用！');
        }
        
        // 解析测试数据
        $testData = !empty($data['test_data']) ? json_decode($data['test_data'], true) : [];
        
        // 替换模板变量
        $content = $template['content'];
        $subject = $template['subject'];
        
        if (!empty($testData)) {
            foreach ($testData as $key => $value) {
                $content = str_replace('{' . $key . '}', $value, $content);
                if ($subject) {
                    $subject = str_replace('{' . $key . '}', $value, $subject);
                }
            }
        }
        
        // 这里可以根据模板类型实际发送测试消息
        // 暂时返回预览结果
        $result = [
            'type' => $template['type'],
            'subject' => $subject,
            'content' => $content,
            'variables' => $testData
        ];
        
        $this->success('测试发送成功！', $result);
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', []);
        
        if (empty($action) || empty($ids)) {
            $this->error('参数错误！');
        }
        
        $count = 0;
        foreach ($ids as $id) {
            $template = MessageTemplateModel::mk()->findOrEmpty($id);
            if ($template->isEmpty()) continue;
            
            switch ($action) {
                case 'enable':
                    $template->save(['status' => 1, 'update_time' => date('Y-m-d H:i:s')]);
                    $count++;
                    break;
                case 'disable':
                    $template->save(['status' => 0, 'update_time' => date('Y-m-d H:i:s')]);
                    $count++;
                    break;
                case 'delete':
                    $template->delete();
                    $count++;
                    break;
            }
        }
        
        $this->success("批量操作完成，处理了 {$count} 个模板！");
    }
}
