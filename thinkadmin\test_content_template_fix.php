<?php

/**
 * 测试正文模板管理页面修复
 */

echo "=== 测试正文模板管理页面修复 ===\n\n";

try {
    // 连接数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 1. 检查必要的数据表
    echo "1. 检查数据表:\n";
    
    $tables = ['document_template', 'paper_type'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "  ✅ {$table}: {$count} 条记录\n";
        } catch (Exception $e) {
            echo "  ❌ {$table}: 表不存在或查询失败\n";
        }
    }
    
    // 2. 检查正文模板数据
    echo "\n2. 检查正文模板数据:\n";
    
    $stmt = $pdo->query("
        SELECT dt.id, dt.name, dt.paper_type_id, dt.is_default, dt.status,
               pt.name as paper_type_name
        FROM document_template dt
        LEFT JOIN paper_type pt ON dt.paper_type_id = pt.id
        WHERE dt.type = 'content'
        ORDER BY dt.is_default DESC, dt.id
    ");
    
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "  ⚠️  没有正文模板数据\n";
    } else {
        foreach ($templates as $template) {
            $typeName = $template['paper_type_name'] ?: '通用';
            $defaultText = $template['is_default'] ? '(默认)' : '';
            $statusText = $template['status'] ? '启用' : '禁用';
            echo "  ✅ {$template['name']} - {$typeName} - {$statusText} {$defaultText}\n";
        }
    }
    
    // 3. 检查论文类型数据
    echo "\n3. 检查论文类型数据:\n";
    
    $stmt = $pdo->query("SELECT id, name, status FROM paper_type WHERE status = 1 ORDER BY sort, id");
    $paperTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($paperTypes)) {
        echo "  ⚠️  没有启用的论文类型\n";
    } else {
        foreach ($paperTypes as $type) {
            echo "  ✅ {$type['name']} (ID: {$type['id']})\n";
        }
    }
    
    // 4. 模拟控制器查询逻辑
    echo "\n4. 模拟控制器查询逻辑:\n";
    
    // 模拟基本查询
    try {
        $query = "
            SELECT dt.*, pt.name as paper_type_name
            FROM document_template dt
            LEFT JOIN paper_type pt ON dt.paper_type_id = pt.id
            WHERE dt.type = 'content'
            ORDER BY dt.is_default DESC, dt.usage_count DESC, dt.id DESC
            LIMIT 20
        ";
        
        $stmt = $pdo->query($query);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "  ✅ 基本查询: " . count($results) . " 条结果\n";
        
        // 模拟搜索查询
        $searchParams = [
            'name' => '模板',
            'paper_type_id' => '1',
            'status' => '1',
            'is_default' => '1'
        ];
        
        $conditions = ["dt.type = 'content'"];
        $params = [];
        
        if (!empty($searchParams['name'])) {
            $conditions[] = "dt.name LIKE ?";
            $params[] = '%' . $searchParams['name'] . '%';
        }
        
        if ($searchParams['paper_type_id'] !== '') {
            $conditions[] = "dt.paper_type_id = ?";
            $params[] = $searchParams['paper_type_id'];
        }
        
        if ($searchParams['status'] !== '') {
            $conditions[] = "dt.status = ?";
            $params[] = $searchParams['status'];
        }
        
        if ($searchParams['is_default'] !== '') {
            $conditions[] = "dt.is_default = ?";
            $params[] = $searchParams['is_default'];
        }
        
        $searchQuery = "
            SELECT COUNT(*) as count
            FROM document_template dt
            WHERE " . implode(' AND ', $conditions);
        
        $stmt = $pdo->prepare($searchQuery);
        $stmt->execute($params);
        $searchCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "  ✅ 搜索查询: {$searchCount} 条匹配记录\n";
        
    } catch (Exception $e) {
        echo "  ❌ 查询测试失败: " . $e->getMessage() . "\n";
    }
    
    // 5. 检查控制器文件语法
    echo "\n5. 检查控制器文件:\n";
    
    $controllerFile = 'app/admin/controller/ContentTemplate.php';
    if (file_exists($controllerFile)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$controllerFile} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ ContentTemplate.php - 语法正确\n";
        } else {
            echo "  ❌ ContentTemplate.php - 语法错误: " . implode(' ', $output) . "\n";
        }
        
        // 检查是否有try-catch保护
        $content = file_get_contents($controllerFile);
        if (strpos($content, 'try {') !== false && strpos($content, 'catch') !== false) {
            echo "  ✅ ContentTemplate.php - 已添加错误保护\n";
        } else {
            echo "  ⚠️  ContentTemplate.php - 未添加错误保护\n";
        }
    } else {
        echo "  ❌ ContentTemplate.php - 文件不存在\n";
    }
    
    // 6. 检查模板文件
    echo "\n6. 检查模板文件:\n";
    
    $templateFile = 'app/admin/view/content_template/index.html';
    if (file_exists($templateFile)) {
        echo "  ✅ index.html - 模板文件存在\n";
        
        $templateContent = file_get_contents($templateFile);
        if (strpos($templateContent, '{foreach $list') !== false) {
            echo "  ✅ index.html - 包含数据循环\n";
        } else {
            echo "  ⚠️  index.html - 可能缺少数据循环\n";
        }
    } else {
        echo "  ❌ index.html - 模板文件不存在\n";
    }
    
    echo "\n🎉 正文模板管理页面测试完成！\n";
    
    echo "\n📋 修复总结:\n";
    echo "1. ✅ 使用try-catch包装QueryHelper调用\n";
    echo "2. ✅ 提供备用查询方案\n";
    echo "3. ✅ 手动处理搜索参数\n";
    echo "4. ✅ 使用标准ThinkAdmin模板渲染\n";
    echo "5. ✅ 保持所有原有功能\n";
    
    echo "\n现在正文模板管理页面应该可以正常访问了！\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中出现错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
