{extend name="../../admin/view/main/layout.html" /}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-file margin-right-5"></span>
        查重报告 - {$task.title}
        <div class="pull-right">
            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="downloadReport()">
                <i class="layui-icon layui-icon-download-circle"></i> 下载报告
            </button>
            <button class="layui-btn layui-btn-sm" onclick="history.back()">
                <i class="layui-icon layui-icon-return"></i> 返回
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 报告概览 -->
        <div class="layui-card margin-bottom-15">
            <div class="layui-card-header">检测概览</div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <div class="text-center">
                            <div class="font-s36 {if condition='$task.similarity_rate > 30'}color-red{elseif condition='$task.similarity_rate > 15'}color-orange{else}color-green{/if}">
                                {$task.similarity_rate}%
                            </div>
                            <div class="color-desc">总相似度</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="text-center">
                            <div class="font-s24 color-blue">{$task.total_paragraphs}</div>
                            <div class="color-desc">总段落数</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="text-center">
                            <div class="font-s24 color-red">{$task.similar_paragraphs}</div>
                            <div class="color-desc">相似段落数</div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="text-center">
                            <div class="font-s24 color-orange">{$task.matched_sources}</div>
                            <div class="color-desc">匹配来源数</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相似度分布 -->
        <div class="layui-row layui-col-space15 margin-bottom-15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">相似度分布</div>
                    <div class="layui-card-body">
                        <div id="similarity-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">检测详情</div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                                <tr>
                                    <td width="120">文档标题</td>
                                    <td>{$task.title}</td>
                                </tr>
                                <tr>
                                    <td>文件名称</td>
                                    <td>{$task.file_name}</td>
                                </tr>
                                <tr>
                                    <td>文件大小</td>
                                    <td>{$task.file_size|format_bytes}</td>
                                </tr>
                                <tr>
                                    <td>检测接口</td>
                                    <td>{$task.api.name|default='未知'}</td>
                                </tr>
                                <tr>
                                    <td>检测时间</td>
                                    <td>{$task.create_time}</td>
                                </tr>
                                <tr>
                                    <td>完成时间</td>
                                    <td>{$task.complete_time}</td>
                                </tr>
                                <tr>
                                    <td>检测费用</td>
                                    <td>¥{$task.cost|default='0.00'}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 风险评估 -->
        <div class="layui-card margin-bottom-15">
            <div class="layui-card-header">风险评估</div>
            <div class="layui-card-body">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md4">
                        <div class="layui-panel">
                            <div class="layui-panel-header">
                                <span class="layui-badge {if condition='$risk_assessment.high_risk_count > 0'}layui-bg-red{else}layui-bg-green{/if}">
                                    高风险段落
                                </span>
                            </div>
                            <div class="layui-panel-body text-center">
                                <div class="font-s24 {if condition='$risk_assessment.high_risk_count > 0'}color-red{else}color-green{/if}">
                                    {$risk_assessment.high_risk_count|default=0}
                                </div>
                                <div class="color-desc">相似度 > 30%</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-panel">
                            <div class="layui-panel-header">
                                <span class="layui-badge {if condition='$risk_assessment.medium_risk_count > 0'}layui-bg-orange{else}layui-bg-green{/if}">
                                    中风险段落
                                </span>
                            </div>
                            <div class="layui-panel-body text-center">
                                <div class="font-s24 {if condition='$risk_assessment.medium_risk_count > 0'}color-orange{else}color-green{/if}">
                                    {$risk_assessment.medium_risk_count|default=0}
                                </div>
                                <div class="color-desc">相似度 15-30%</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="layui-panel">
                            <div class="layui-panel-header">
                                <span class="layui-badge layui-bg-green">低风险段落</span>
                            </div>
                            <div class="layui-panel-body text-center">
                                <div class="font-s24 color-green">
                                    {$risk_assessment.low_risk_count|default=0}
                                </div>
                                <div class="color-desc">相似度 < 15%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要来源分析 -->
        {if condition="!empty($top_sources)"}
        <div class="layui-card margin-bottom-15">
            <div class="layui-card-header">主要匹配来源</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>来源标题</th>
                            <th>来源URL</th>
                            <th>匹配次数</th>
                            <th>平均相似度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="top_sources" id="source"}
                        <tr>
                            <td>{$i}</td>
                            <td>{$source.title|default='未知来源'|mb_substr=0,50,'utf-8'}</td>
                            <td>
                                {if condition="!empty($source.url)"}
                                    <a href="{$source.url}" target="_blank" class="color-blue">
                                        {$source.url|mb_substr=0,50,'utf-8'}
                                    </a>
                                {else}
                                    <span class="color-desc">未知</span>
                                {/if}
                            </td>
                            <td>
                                <span class="layui-badge layui-bg-blue">{$source.match_count}</span>
                            </td>
                            <td>
                                <span class="layui-badge {if condition='$source.avg_similarity > 30'}layui-bg-red{elseif condition='$source.avg_similarity > 15'}layui-bg-orange{else}layui-bg-green{/if}">
                                    {$source.avg_similarity}%
                                </span>
                            </td>
                            <td>
                                <button class="layui-btn layui-btn-xs" onclick="viewSourceDetails('{$source.url}')">
                                    <i class="layui-icon layui-icon-search"></i> 查看详情
                                </button>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>
        {/if}

        <!-- 修改建议 -->
        <div class="layui-card margin-bottom-15">
            <div class="layui-card-header">修改建议</div>
            <div class="layui-card-body">
                {if condition="$task.similarity_rate > 30"}
                <div class="layui-panel">
                    <div class="layui-panel-header" style="background: #ffebee;">
                        <span class="color-red"><i class="layui-icon layui-icon-close-fill"></i> 高风险警告</span>
                    </div>
                    <div class="layui-panel-body">
                        <p>您的文档总相似度为 <strong class="color-red">{$task.similarity_rate}%</strong>，存在较高的重复风险。建议：</p>
                        <ul>
                            <li>重点修改相似度超过30%的段落</li>
                            <li>对重复内容进行改写或引用标注</li>
                            <li>增加原创性内容和个人观点</li>
                            <li>检查引用格式是否规范</li>
                        </ul>
                    </div>
                </div>
                {elseif condition="$task.similarity_rate > 15"}
                <div class="layui-panel">
                    <div class="layui-panel-header" style="background: #fff3e0;">
                        <span class="color-orange"><i class="layui-icon layui-icon-tips"></i> 中等风险提醒</span>
                    </div>
                    <div class="layui-panel-body">
                        <p>您的文档总相似度为 <strong class="color-orange">{$task.similarity_rate}%</strong>，存在一定的重复风险。建议：</p>
                        <ul>
                            <li>检查并修改相似度较高的段落</li>
                            <li>确保引用内容有正确的标注</li>
                            <li>适当增加原创性分析和讨论</li>
                        </ul>
                    </div>
                </div>
                {else}
                <div class="layui-panel">
                    <div class="layui-panel-header" style="background: #e8f5e8;">
                        <span class="color-green"><i class="layui-icon layui-icon-ok"></i> 低风险通过</span>
                    </div>
                    <div class="layui-panel-body">
                        <p>您的文档总相似度为 <strong class="color-green">{$task.similarity_rate}%</strong>，重复风险较低。建议：</p>
                        <ul>
                            <li>继续保持原创性写作</li>
                            <li>确保引用格式规范</li>
                            <li>可以进一步完善内容质量</li>
                        </ul>
                    </div>
                </div>
                {/if}
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="text-center">
            <button class="layui-btn layui-btn-lg" onclick="viewParagraphs()">
                <i class="layui-icon layui-icon-list"></i> 查看段落详情
            </button>
            <button class="layui-btn layui-btn-lg layui-btn-normal" onclick="downloadReport()">
                <i class="layui-icon layui-icon-download-circle"></i> 下载完整报告
            </button>
            {if condition="$task.canResubmit()"}
            <button class="layui-btn layui-btn-lg layui-btn-warm" onclick="resubmitTask()">
                <i class="layui-icon layui-icon-refresh-3"></i> 重新检测
            </button>
            {/if}
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 初始化相似度分布图表
    var chartDom = document.getElementById('similarity-chart');
    var myChart = echarts.init(chartDom);
    
    var option = {
        title: {
            text: '段落相似度分布',
            left: 'center',
            textStyle: {
                fontSize: 14
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            data: ['低风险(<15%)', '中风险(15-30%)', '高风险(>30%)']
        },
        series: [
            {
                name: '段落分布',
                type: 'pie',
                radius: '50%',
                data: [
                    {value: {$risk_assessment.low_risk_count|default=0}, name: '低风险(<15%)', itemStyle: {color: '#5FB878'}},
                    {value: {$risk_assessment.medium_risk_count|default=0}, name: '中风险(15-30%)', itemStyle: {color: '#FF9800'}},
                    {value: {$risk_assessment.high_risk_count|default=0}, name: '高风险(>30%)', itemStyle: {color: '#FF5722'}}
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    
    myChart.setOption(option);
    
    // 响应式
    window.addEventListener('resize', function() {
        myChart.resize();
    });
    
    // 查看段落详情
    window.viewParagraphs = function() {
        window.open('{:url("paragraph")}?id={$task.id}', '_blank');
    };
    
    // 下载报告
    window.downloadReport = function() {
        {if condition="$task.report_file"}
            window.open('{$task.report_file}', '_blank');
        {else}
            layer.msg('报告文件不存在', {icon: 2});
        {/if}
    };
    
    // 重新提交任务
    window.resubmitTask = function() {
        layer.confirm('确定要重新检测这个文档吗？', {icon: 3, title: '提示'}, function(index){
            $.post('{:url("resubmit")}', {id: {$task.id}}, function(res){
                if(res.code === 1) {
                    layer.msg(res.info, {icon: 1});
                    setTimeout(function(){
                        location.reload();
                    }, 1500);
                } else {
                    layer.msg(res.info, {icon: 2});
                }
            });
            layer.close(index);
        });
    };
    
    // 查看来源详情
    window.viewSourceDetails = function(url) {
        if (url && url !== '未知') {
            window.open(url, '_blank');
        } else {
            layer.msg('来源URL不可用', {icon: 2});
        }
    };
});
</script>

<style>
.font-s24 {
    font-size: 24px;
}

.font-s36 {
    font-size: 36px;
}

.color-green {
    color: #5FB878;
}

.color-red {
    color: #FF5722;
}

.color-orange {
    color: #FF9800;
}

.color-blue {
    color: #1E9FFF;
}

.color-desc {
    color: #999;
}

.layui-panel-header {
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: bold;
}

.layui-panel-body {
    padding: 15px;
}
</style>
{/block}
