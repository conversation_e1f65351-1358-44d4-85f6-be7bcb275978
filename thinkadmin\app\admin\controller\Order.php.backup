<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\Order as OrderModel;
use app\admin\model\Package;
use app\admin\model\User;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 订单管理
 * @class Order
 * @package app\admin\controller
 */
class Order extends Controller
{
    /**
     * 订单管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        OrderModel::mQuery()->layTable(function () {
            $this->title = '订单管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            $query->with(['user', 'package']);
            $query->like('order_no,package_name,transaction_id,remark');
            $query->equal('payment_status,payment_method,package_id,user_id');
            $query->dateBetween('createtime,paid_time,expired_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加订单
     * @auth true
     */public function add()
    {
        OrderModel::mForm('form');
    }

    /**
     * 编辑订单
     * @auth true
     */
    public function edit()
    {
        OrderModel::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->paymentStatusOptions = OrderModel::getPaymentStatusOptions();
            $this->paymentMethodOptions = OrderModel::getPaymentMethodOptions();
            $this->packages = Package::getAvailablePackages();
            $this->users = User::mk()->where(['status' => 1])->column('nickname,username', 'id');
            
            // 如果是编辑，获取关联数据
            if (!empty($vo['id'])) {
                $order = OrderModel::mk()->with(['user', 'package'])->findOrEmpty($vo['id']);
                if (!$order->isEmpty()) {
                    $vo = array_merge($vo, $order->toArray());
                }
            }
        } else {
            // 验证订单数据
            $errors = OrderModel::validateOrderData($vo);
            if (!empty($errors)) {
                $this->error(implode('；', $errors));
            }
            
            // 生成订单号（如果是新增）
            if (empty($vo['id']) && empty($vo['order_no'])) {
                $vo['order_no'] = OrderModel::generateOrderNo();
            }
            
            // 设置过期时间（如果是新增且未设置）
            if (empty($vo['id']) && empty($vo['expired_time'])) {
                $vo['expired_time'] = OrderModel::calculateExpiredTime();
            }
            
            // 获取套餐信息
            if (!empty($vo['package_id'])) {
                $package = Package::mk()->findOrEmpty($vo['package_id']);
                if (!$package->isEmpty()) {
                    $vo['package_name'] = $package->name;
                    if (empty($vo['original_price'])) {
                        $vo['original_price'] = $package->sale_price;
                    }
                    if (empty($vo['final_price'])) {
                        $vo['final_price'] = $package->sale_price;
                    }
                }
            }
            
            // 计算优惠金额
            $vo['discount_amount'] = max(0, $vo['original_price'] - $vo['final_price']);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['createtime'] = date('Y-m-d H:i:s');
            }
            $vo['updatetime'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('订单保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除订单
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('订单ID不能为空！');
        }

        $order = OrderModel::mk()->findOrEmpty($id);
        if ($order->isEmpty()) {
            $this->error('订单不存在！');
        }

        // 只能删除未支付的订单
        if ($order->isPaid()) {
            $this->error('已支付的订单不能删除！');
        }

        if ($order->delete()) {
            $this->success('订单删除成功！');
        } else {
            $this->error('订单删除失败！');
        }
    }

    /**
     * 订单详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('订单ID不能为空！');
        }

        $order = OrderModel::mk()->with(['user', 'package', 'coupon'])->findOrEmpty($id);
        if ($order->isEmpty()) {
            $this->error('订单不存在！');
        }

        $this->assign('order', $order);
        $this->assign('title', '订单详情');
        
        return $this->fetch('order/view');
    }

    /**
     * 手动确认支付
     * @auth true
     */
    public function confirmPayment()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('订单ID不能为空！');
        }

        $order = OrderModel::mk()->findOrEmpty($id);
        if ($order->isEmpty()) {
            $this->error('订单不存在！');
        }

        if ($order->isPaid()) {
            $this->error('订单已支付，无需重复确认！');
        }

        if ($order->updatePaymentStatus('paid')) {
            $this->success('支付确认成功！');
        } else {
            $this->error('支付确认失败！');
        }
    }

    /**
     * 取消订单
     * @auth true
     */
    public function cancel()
    {
        $id = $this->request->post('id', 0);
        $reason = $this->request->post('reason', '');
        
        if (empty($id)) {
            $this->error('订单ID不能为空！');
        }

        $order = OrderModel::mk()->findOrEmpty($id);
        if ($order->isEmpty()) {
            $this->error('订单不存在！');
        }

        if (!$order->canCancel()) {
            $this->error('该订单不能取消！');
        }

        $updateData = ['remark' => $order->remark . "\n取消原因：" . $reason];
        if ($order->updatePaymentStatus('cancelled', $updateData)) {
            $this->success('订单取消成功！');
        } else {
            $this->error('订单取消失败！');
        }
    }

    /**
     * 申请退款
     * @auth true
     */
    public function refund()
    {
        $id = $this->request->post('id', 0);
        $reason = $this->request->post('reason', '');
        
        if (empty($id)) {
            $this->error('订单ID不能为空！');
        }

        $order = OrderModel::mk()->findOrEmpty($id);
        if ($order->isEmpty()) {
            $this->error('订单不存在！');
        }

        if (!$order->canRefund()) {
            $this->error('该订单不能退款！');
        }

        $updateData = ['remark' => $order->remark . "\n退款原因：" . $reason];
        if ($order->updatePaymentStatus('refunded', $updateData)) {
            $this->success('退款申请成功！');
        } else {
            $this->error('退款申请失败！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');
        
        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $orders = OrderModel::mk()->whereIn('id', $idArray)->select();
        
        if ($orders->isEmpty()) {
            $this->error('没有找到要操作的订单！');
        }

        $successCount = 0;
        $errorMessages = [];

        foreach ($orders as $order) {                switch ($action) {
                    case 'confirm':
                        if (!$order->isPaid() && $order->updatePaymentStatus('paid')) {
                            $successCount++;
                        }
                        break;
                    case 'cancel':
                        if ($order->canCancel() && $order->updatePaymentStatus('cancelled')) {
                            $successCount++;
                        }
                        break;
                    case 'delete':
                        if (!$order->isPaid() && $order->delete()) {
                            $successCount++;
                        }
                        break;
                }操作失败：" . $e->getMessage();
            }
        }

        if ($successCount > 0) {
            $message = "成功操作{$successCount}个订单";
            if (!empty($errorMessages)) {
                $message .= "，" . implode('；', $errorMessages);
            }
            $this->success($message);
        } else {
            $this->error('批量操作失败：' . implode('；', $errorMessages));
        }
    }

    /**
     * 订单统计
     * @auth true
     */
    public function statistics()
    {
        $startDate = $this->request->get('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $this->request->get('end_date', date('Y-m-d'));
        
        // 基础统计
        $conditions = [
            'start_date' => $startDate,
            'end_date' => $endDate . ' 23:59:59'
        ];
        $basicStats = OrderModel::getOrderStatistics($conditions);
        
        // 热门套餐
        $popularPackages = OrderModel::getPopularPackages(10);
        
        // 每日统计
        $dailyStats = OrderModel::getDailyOrderStats(30);
        
        // 支付方式统计
        $paymentMethodStats = OrderModel::mk()
            ->field('payment_method, COUNT(*) as count, SUM(final_price) as amount')
            ->where('payment_status', 'paid')
            ->where('createtime', '>=', $startDate)
            ->where('createtime', '<=', $endDate . ' 23:59:59')
            ->group('payment_method')
            ->select()
            ->toArray();

        $statistics = [
            'basic_stats' => $basicStats,
            'popular_packages' => $popularPackages,
            'daily_stats' => $dailyStats,
            'payment_method_stats' => $paymentMethodStats
        ];

        return json($statistics);
    }

    /**
     * 导出订单数据
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的订单！');
        }

        $idArray = explode(',', $ids);
        $orders = OrderModel::mk()
            ->with(['user', 'package'])
            ->whereIn('id', $idArray)
            ->order('id desc')
            ->select();

        if ($orders->isEmpty()) {
            $this->error('没有找到要导出的订单！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['订单号', '用户', '套餐名称', '原价', '优惠金额', '实付金额', '支付方式', '支付状态', '支付时间', '创建时间'];
        
        foreach ($orders as $order) {
            $exportData[] = [
                $order->order_no,
                $order->user->nickname ?? $order->user->username ?? '未知用户',
                $order->package_name,
                $order->original_price,
                $order->discount_amount,
                $order->final_price,
                $order->payment_method_text,
                $order->payment_status_text,
                $order->paid_time ?: '未支付',
                $order->createtime
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'Order管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
