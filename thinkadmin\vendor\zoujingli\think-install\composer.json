{"type": "composer-plugin", "name": "zoujingli/think-install", "license": "Apache-2.0", "homepage": "https://thinkadmin.top", "description": "Plugin In<PERSON>ler for ThinkAdmin", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"think\\admin\\install\\": "src"}}, "require": {"php": ">=7.1", "ext-json": "*", "symfony/process": "^5.4 || ^6.0 || *", "composer-plugin-api": "^1.0 || ^2.0 || *"}, "require-dev": {"composer/composer": "^1.0 || ^2.0"}, "extra": {"class": ["think\\admin\\install\\Service"]}}