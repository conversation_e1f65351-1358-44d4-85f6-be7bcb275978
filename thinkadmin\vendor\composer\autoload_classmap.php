<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Ip2Region' => $vendorDir . '/zoujingli/ip2region/Ip2Region.php',
    'Phinx\\Config\\Config' => $vendorDir . '/topthink/think-migration/phinx/Config/Config.php',
    'Phinx\\Config\\ConfigInterface' => $vendorDir . '/topthink/think-migration/phinx/Config/ConfigInterface.php',
    'Phinx\\Config\\FeatureFlags' => $vendorDir . '/topthink/think-migration/phinx/Config/FeatureFlags.php',
    'Phinx\\Config\\NamespaceAwareInterface' => $vendorDir . '/topthink/think-migration/phinx/Config/NamespaceAwareInterface.php',
    'Phinx\\Config\\NamespaceAwareTrait' => $vendorDir . '/topthink/think-migration/phinx/Config/NamespaceAwareTrait.php',
    'Phinx\\Db\\Action\\Action' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/Action.php',
    'Phinx\\Db\\Action\\AddColumn' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/AddColumn.php',
    'Phinx\\Db\\Action\\AddForeignKey' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/AddForeignKey.php',
    'Phinx\\Db\\Action\\AddIndex' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/AddIndex.php',
    'Phinx\\Db\\Action\\ChangeColumn' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/ChangeColumn.php',
    'Phinx\\Db\\Action\\ChangeComment' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/ChangeComment.php',
    'Phinx\\Db\\Action\\ChangePrimaryKey' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/ChangePrimaryKey.php',
    'Phinx\\Db\\Action\\CreateTable' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/CreateTable.php',
    'Phinx\\Db\\Action\\DropForeignKey' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/DropForeignKey.php',
    'Phinx\\Db\\Action\\DropIndex' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/DropIndex.php',
    'Phinx\\Db\\Action\\DropTable' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/DropTable.php',
    'Phinx\\Db\\Action\\RemoveColumn' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/RemoveColumn.php',
    'Phinx\\Db\\Action\\RenameColumn' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/RenameColumn.php',
    'Phinx\\Db\\Action\\RenameTable' => $vendorDir . '/topthink/think-migration/phinx/Db/Action/RenameTable.php',
    'Phinx\\Db\\Adapter\\AbstractAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/AbstractAdapter.php',
    'Phinx\\Db\\Adapter\\AdapterFactory' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/AdapterFactory.php',
    'Phinx\\Db\\Adapter\\AdapterInterface' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/AdapterInterface.php',
    'Phinx\\Db\\Adapter\\AdapterWrapper' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/AdapterWrapper.php',
    'Phinx\\Db\\Adapter\\DirectActionInterface' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/DirectActionInterface.php',
    'Phinx\\Db\\Adapter\\MysqlAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/MysqlAdapter.php',
    'Phinx\\Db\\Adapter\\PdoAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/PdoAdapter.php',
    'Phinx\\Db\\Adapter\\PostgresAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/PostgresAdapter.php',
    'Phinx\\Db\\Adapter\\ProxyAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/ProxyAdapter.php',
    'Phinx\\Db\\Adapter\\SQLiteAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/SQLiteAdapter.php',
    'Phinx\\Db\\Adapter\\SqlServerAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/SqlServerAdapter.php',
    'Phinx\\Db\\Adapter\\TablePrefixAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/TablePrefixAdapter.php',
    'Phinx\\Db\\Adapter\\TimedOutputAdapter' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/TimedOutputAdapter.php',
    'Phinx\\Db\\Adapter\\UnsupportedColumnTypeException' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/UnsupportedColumnTypeException.php',
    'Phinx\\Db\\Adapter\\WrapperInterface' => $vendorDir . '/topthink/think-migration/phinx/Db/Adapter/WrapperInterface.php',
    'Phinx\\Db\\Plan\\AlterTable' => $vendorDir . '/topthink/think-migration/phinx/Db/Plan/AlterTable.php',
    'Phinx\\Db\\Plan\\Intent' => $vendorDir . '/topthink/think-migration/phinx/Db/Plan/Intent.php',
    'Phinx\\Db\\Plan\\NewTable' => $vendorDir . '/topthink/think-migration/phinx/Db/Plan/NewTable.php',
    'Phinx\\Db\\Plan\\Plan' => $vendorDir . '/topthink/think-migration/phinx/Db/Plan/Plan.php',
    'Phinx\\Db\\Plan\\Solver\\ActionSplitter' => $vendorDir . '/topthink/think-migration/phinx/Db/Plan/Solver/ActionSplitter.php',
    'Phinx\\Db\\Table' => $vendorDir . '/topthink/think-migration/phinx/Db/Table.php',
    'Phinx\\Db\\Table\\Column' => $vendorDir . '/topthink/think-migration/phinx/Db/Table/Column.php',
    'Phinx\\Db\\Table\\ForeignKey' => $vendorDir . '/topthink/think-migration/phinx/Db/Table/ForeignKey.php',
    'Phinx\\Db\\Table\\Index' => $vendorDir . '/topthink/think-migration/phinx/Db/Table/Index.php',
    'Phinx\\Db\\Table\\Table' => $vendorDir . '/topthink/think-migration/phinx/Db/Table/Table.php',
    'Phinx\\Db\\Util\\AlterInstructions' => $vendorDir . '/topthink/think-migration/phinx/Db/Util/AlterInstructions.php',
    'Phinx\\Migration\\AbstractMigration' => $vendorDir . '/topthink/think-migration/phinx/Migration/AbstractMigration.php',
    'Phinx\\Migration\\AbstractTemplateCreation' => $vendorDir . '/topthink/think-migration/phinx/Migration/AbstractTemplateCreation.php',
    'Phinx\\Migration\\CreationInterface' => $vendorDir . '/topthink/think-migration/phinx/Migration/CreationInterface.php',
    'Phinx\\Migration\\IrreversibleMigrationException' => $vendorDir . '/topthink/think-migration/phinx/Migration/IrreversibleMigrationException.php',
    'Phinx\\Migration\\Manager' => $vendorDir . '/topthink/think-migration/phinx/Migration/Manager.php',
    'Phinx\\Migration\\Manager\\Environment' => $vendorDir . '/topthink/think-migration/phinx/Migration/Manager/Environment.php',
    'Phinx\\Migration\\MigrationInterface' => $vendorDir . '/topthink/think-migration/phinx/Migration/MigrationInterface.php',
    'Phinx\\Seed\\AbstractSeed' => $vendorDir . '/topthink/think-migration/phinx/Seed/AbstractSeed.php',
    'Phinx\\Seed\\SeedInterface' => $vendorDir . '/topthink/think-migration/phinx/Seed/SeedInterface.php',
    'Phinx\\Util\\Expression' => $vendorDir . '/topthink/think-migration/phinx/Util/Expression.php',
    'Phinx\\Util\\Literal' => $vendorDir . '/topthink/think-migration/phinx/Util/Literal.php',
    'Phinx\\Util\\Util' => $vendorDir . '/topthink/think-migration/phinx/Util/Util.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/src/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/src/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/src/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/src/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/src/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/src/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/src/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/src/NullLogger.php',
    'Psr\\SimpleCache\\CacheException' => $vendorDir . '/psr/simple-cache/src/CacheException.php',
    'Psr\\SimpleCache\\CacheInterface' => $vendorDir . '/psr/simple-cache/src/CacheInterface.php',
    'Psr\\SimpleCache\\InvalidArgumentException' => $vendorDir . '/psr/simple-cache/src/InvalidArgumentException.php',
    'Symfony\\Component\\Process\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/process/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Process\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/process/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Process\\Exception\\LogicException' => $vendorDir . '/symfony/process/Exception/LogicException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessFailedException' => $vendorDir . '/symfony/process/Exception/ProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessSignaledException' => $vendorDir . '/symfony/process/Exception/ProcessSignaledException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessStartFailedException' => $vendorDir . '/symfony/process/Exception/ProcessStartFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessTimedOutException' => $vendorDir . '/symfony/process/Exception/ProcessTimedOutException.php',
    'Symfony\\Component\\Process\\Exception\\RunProcessFailedException' => $vendorDir . '/symfony/process/Exception/RunProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\RuntimeException' => $vendorDir . '/symfony/process/Exception/RuntimeException.php',
    'Symfony\\Component\\Process\\ExecutableFinder' => $vendorDir . '/symfony/process/ExecutableFinder.php',
    'Symfony\\Component\\Process\\InputStream' => $vendorDir . '/symfony/process/InputStream.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessContext' => $vendorDir . '/symfony/process/Messenger/RunProcessContext.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessMessage' => $vendorDir . '/symfony/process/Messenger/RunProcessMessage.php',
    'Symfony\\Component\\Process\\Messenger\\RunProcessMessageHandler' => $vendorDir . '/symfony/process/Messenger/RunProcessMessageHandler.php',
    'Symfony\\Component\\Process\\PhpExecutableFinder' => $vendorDir . '/symfony/process/PhpExecutableFinder.php',
    'Symfony\\Component\\Process\\PhpProcess' => $vendorDir . '/symfony/process/PhpProcess.php',
    'Symfony\\Component\\Process\\PhpSubprocess' => $vendorDir . '/symfony/process/PhpSubprocess.php',
    'Symfony\\Component\\Process\\Pipes\\AbstractPipes' => $vendorDir . '/symfony/process/Pipes/AbstractPipes.php',
    'Symfony\\Component\\Process\\Pipes\\PipesInterface' => $vendorDir . '/symfony/process/Pipes/PipesInterface.php',
    'Symfony\\Component\\Process\\Pipes\\UnixPipes' => $vendorDir . '/symfony/process/Pipes/UnixPipes.php',
    'Symfony\\Component\\Process\\Pipes\\WindowsPipes' => $vendorDir . '/symfony/process/Pipes/WindowsPipes.php',
    'Symfony\\Component\\Process\\Process' => $vendorDir . '/symfony/process/Process.php',
    'Symfony\\Component\\Process\\ProcessUtils' => $vendorDir . '/symfony/process/ProcessUtils.php',
    'XdbSearcher' => $vendorDir . '/zoujingli/ip2region/XdbSearcher.php',
    'app\\admin\\Service' => $baseDir . '/app/admin/Service.php',
    'app\\admin\\controller\\Auth' => $baseDir . '/app/admin/controller/Auth.php',
    'app\\admin\\controller\\Base' => $baseDir . '/app/admin/controller/Base.php',
    'app\\admin\\controller\\Config' => $baseDir . '/app/admin/controller/Config.php',
    'app\\admin\\controller\\File' => $baseDir . '/app/admin/controller/File.php',
    'app\\admin\\controller\\Index' => $baseDir . '/app/admin/controller/Index.php',
    'app\\admin\\controller\\Login' => $baseDir . '/app/admin/controller/Login.php',
    'app\\admin\\controller\\Menu' => $baseDir . '/app/admin/controller/Menu.php',
    'app\\admin\\controller\\Oplog' => $baseDir . '/app/admin/controller/Oplog.php',
    'app\\admin\\controller\\Queue' => $baseDir . '/app/admin/controller/Queue.php',
    'app\\admin\\controller\\User' => $baseDir . '/app/admin/controller/User.php',
    'app\\admin\\controller\\api\\Plugs' => $baseDir . '/app/admin/controller/api/Plugs.php',
    'app\\admin\\controller\\api\\Queue' => $baseDir . '/app/admin/controller/api/Queue.php',
    'app\\admin\\controller\\api\\System' => $baseDir . '/app/admin/controller/api/System.php',
    'app\\admin\\controller\\api\\Upload' => $baseDir . '/app/admin/controller/api/Upload.php',
    'app\\index\\controller\\Index' => $baseDir . '/app/index/controller/Index.php',
    'plugin\\account\\Service' => $vendorDir . '/zoujingli/think-plugs-account/src/Service.php',
    'plugin\\account\\controller\\Device' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/Device.php',
    'plugin\\account\\controller\\Master' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/Master.php',
    'plugin\\account\\controller\\Message' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/Message.php',
    'plugin\\account\\controller\\api\\Auth' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/api/Auth.php',
    'plugin\\account\\controller\\api\\Login' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/api/Login.php',
    'plugin\\account\\controller\\api\\Wechat' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/api/Wechat.php',
    'plugin\\account\\controller\\api\\Wxapp' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/api/Wxapp.php',
    'plugin\\account\\controller\\api\\auth\\Center' => $vendorDir . '/zoujingli/think-plugs-account/src/controller/api/auth/Center.php',
    'plugin\\account\\model\\Abs' => $vendorDir . '/zoujingli/think-plugs-account/src/model/Abs.php',
    'plugin\\account\\model\\PluginAccountAuth' => $vendorDir . '/zoujingli/think-plugs-account/src/model/PluginAccountAuth.php',
    'plugin\\account\\model\\PluginAccountBind' => $vendorDir . '/zoujingli/think-plugs-account/src/model/PluginAccountBind.php',
    'plugin\\account\\model\\PluginAccountMsms' => $vendorDir . '/zoujingli/think-plugs-account/src/model/PluginAccountMsms.php',
    'plugin\\account\\model\\PluginAccountUser' => $vendorDir . '/zoujingli/think-plugs-account/src/model/PluginAccountUser.php',
    'plugin\\account\\service\\Account' => $vendorDir . '/zoujingli/think-plugs-account/src/service/Account.php',
    'plugin\\account\\service\\Message' => $vendorDir . '/zoujingli/think-plugs-account/src/service/Message.php',
    'plugin\\account\\service\\contract\\AccountAccess' => $vendorDir . '/zoujingli/think-plugs-account/src/service/contract/AccountAccess.php',
    'plugin\\account\\service\\contract\\AccountInterface' => $vendorDir . '/zoujingli/think-plugs-account/src/service/contract/AccountInterface.php',
    'plugin\\account\\service\\contract\\MessageInterface' => $vendorDir . '/zoujingli/think-plugs-account/src/service/contract/MessageInterface.php',
    'plugin\\account\\service\\contract\\MessageUsageTrait' => $vendorDir . '/zoujingli/think-plugs-account/src/service/contract/MessageUsageTrait.php',
    'plugin\\account\\service\\message\\Alisms' => $vendorDir . '/zoujingli/think-plugs-account/src/service/message/Alisms.php',
    'plugin\\center\\Service' => $vendorDir . '/zoujingli/think-plugs-center/src/Service.php',
    'plugin\\center\\controller\\Index' => $vendorDir . '/zoujingli/think-plugs-center/src/controller/Index.php',
    'plugin\\center\\service\\Plugin' => $vendorDir . '/zoujingli/think-plugs-center/src/service/Plugin.php',
    'plugin\\payment\\Service' => $vendorDir . '/zoujingli/think-plugs-payment/src/Service.php',
    'plugin\\payment\\controller\\Balance' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/Balance.php',
    'plugin\\payment\\controller\\Config' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/Config.php',
    'plugin\\payment\\controller\\Integral' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/Integral.php',
    'plugin\\payment\\controller\\Record' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/Record.php',
    'plugin\\payment\\controller\\Refund' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/Refund.php',
    'plugin\\payment\\controller\\api\\auth\\Address' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/api/auth/Address.php',
    'plugin\\payment\\controller\\api\\auth\\Balance' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/api/auth/Balance.php',
    'plugin\\payment\\controller\\api\\auth\\Integral' => $vendorDir . '/zoujingli/think-plugs-payment/src/controller/api/auth/Integral.php',
    'plugin\\payment\\model\\PluginPaymentAddress' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentAddress.php',
    'plugin\\payment\\model\\PluginPaymentBalance' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentBalance.php',
    'plugin\\payment\\model\\PluginPaymentConfig' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentConfig.php',
    'plugin\\payment\\model\\PluginPaymentIntegral' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentIntegral.php',
    'plugin\\payment\\model\\PluginPaymentRecord' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentRecord.php',
    'plugin\\payment\\model\\PluginPaymentRefund' => $vendorDir . '/zoujingli/think-plugs-payment/src/model/PluginPaymentRefund.php',
    'plugin\\payment\\queue\\Recount' => $vendorDir . '/zoujingli/think-plugs-payment/src/queue/Recount.php',
    'plugin\\payment\\service\\Balance' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/Balance.php',
    'plugin\\payment\\service\\Integral' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/Integral.php',
    'plugin\\payment\\service\\Payment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/Payment.php',
    'plugin\\payment\\service\\contract\\PaymentInterface' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/contract/PaymentInterface.php',
    'plugin\\payment\\service\\contract\\PaymentResponse' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/contract/PaymentResponse.php',
    'plugin\\payment\\service\\contract\\PaymentUsageTrait' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/contract/PaymentUsageTrait.php',
    'plugin\\payment\\service\\payment\\AliPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/AliPayment.php',
    'plugin\\payment\\service\\payment\\BalancePayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/BalancePayment.php',
    'plugin\\payment\\service\\payment\\CouponPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/CouponPayment.php',
    'plugin\\payment\\service\\payment\\EmptyPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/EmptyPayment.php',
    'plugin\\payment\\service\\payment\\IntegralPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/IntegralPayment.php',
    'plugin\\payment\\service\\payment\\JoinPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/JoinPayment.php',
    'plugin\\payment\\service\\payment\\VoucherPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/VoucherPayment.php',
    'plugin\\payment\\service\\payment\\WechatPayment' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/WechatPayment.php',
    'plugin\\payment\\service\\payment\\wechat\\WechatPaymentV2' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/wechat/WechatPaymentV2.php',
    'plugin\\payment\\service\\payment\\wechat\\WechatPaymentV3' => $vendorDir . '/zoujingli/think-plugs-payment/src/service/payment/wechat/WechatPaymentV3.php',
    'think\\App' => $vendorDir . '/topthink/framework/src/think/App.php',
    'think\\Cache' => $vendorDir . '/topthink/framework/src/think/Cache.php',
    'think\\Collection' => $vendorDir . '/topthink/think-helper/src/Collection.php',
    'think\\Config' => $vendorDir . '/topthink/framework/src/think/Config.php',
    'think\\Console' => $vendorDir . '/topthink/framework/src/think/Console.php',
    'think\\Container' => $vendorDir . '/topthink/think-container/src/Container.php',
    'think\\Cookie' => $vendorDir . '/topthink/framework/src/think/Cookie.php',
    'think\\Db' => $vendorDir . '/topthink/framework/src/think/Db.php',
    'think\\DbManager' => $vendorDir . '/topthink/think-orm/src/DbManager.php',
    'think\\Env' => $vendorDir . '/topthink/framework/src/think/Env.php',
    'think\\Event' => $vendorDir . '/topthink/framework/src/think/Event.php',
    'think\\Exception' => $vendorDir . '/topthink/framework/src/think/Exception.php',
    'think\\Facade' => $vendorDir . '/topthink/think-container/src/Facade.php',
    'think\\File' => $vendorDir . '/topthink/framework/src/think/File.php',
    'think\\Http' => $vendorDir . '/topthink/framework/src/think/Http.php',
    'think\\Lang' => $vendorDir . '/topthink/framework/src/think/Lang.php',
    'think\\Log' => $vendorDir . '/topthink/framework/src/think/Log.php',
    'think\\Manager' => $vendorDir . '/topthink/framework/src/think/Manager.php',
    'think\\Middleware' => $vendorDir . '/topthink/framework/src/think/Middleware.php',
    'think\\Model' => $vendorDir . '/topthink/think-orm/src/Model.php',
    'think\\Paginator' => $vendorDir . '/topthink/think-orm/src/Paginator.php',
    'think\\Pipeline' => $vendorDir . '/topthink/framework/src/think/Pipeline.php',
    'think\\Request' => $vendorDir . '/topthink/framework/src/think/Request.php',
    'think\\Response' => $vendorDir . '/topthink/framework/src/think/Response.php',
    'think\\Route' => $vendorDir . '/topthink/framework/src/think/Route.php',
    'think\\Service' => $vendorDir . '/topthink/framework/src/think/Service.php',
    'think\\Session' => $vendorDir . '/topthink/framework/src/think/Session.php',
    'think\\Template' => $vendorDir . '/topthink/think-template/src/Template.php',
    'think\\Validate' => $vendorDir . '/topthink/think-validate/src/Validate.php',
    'think\\View' => $vendorDir . '/topthink/framework/src/think/View.php',
    'think\\admin\\Builder' => $vendorDir . '/zoujingli/think-library/src/Builder.php',
    'think\\admin\\Command' => $vendorDir . '/zoujingli/think-library/src/Command.php',
    'think\\admin\\Controller' => $vendorDir . '/zoujingli/think-library/src/Controller.php',
    'think\\admin\\Exception' => $vendorDir . '/zoujingli/think-library/src/Exception.php',
    'think\\admin\\Helper' => $vendorDir . '/zoujingli/think-library/src/Helper.php',
    'think\\admin\\Library' => $vendorDir . '/zoujingli/think-library/src/Library.php',
    'think\\admin\\Model' => $vendorDir . '/zoujingli/think-library/src/Model.php',
    'think\\admin\\Plugin' => $vendorDir . '/zoujingli/think-library/src/Plugin.php',
    'think\\admin\\Queue' => $vendorDir . '/zoujingli/think-library/src/Queue.php',
    'think\\admin\\Service' => $vendorDir . '/zoujingli/think-library/src/Service.php',
    'think\\admin\\Storage' => $vendorDir . '/zoujingli/think-library/src/Storage.php',
    'think\\admin\\contract\\StorageInterface' => $vendorDir . '/zoujingli/think-library/src/contract/StorageInterface.php',
    'think\\admin\\contract\\StorageUsageTrait' => $vendorDir . '/zoujingli/think-library/src/contract/StorageUsageTrait.php',
    'think\\admin\\contract\\StreamInterface' => $vendorDir . '/zoujingli/think-library/src/contract/StreamInterface.php',
    'think\\admin\\extend\\CodeExtend' => $vendorDir . '/zoujingli/think-library/src/extend/CodeExtend.php',
    'think\\admin\\extend\\DataExtend' => $vendorDir . '/zoujingli/think-library/src/extend/DataExtend.php',
    'think\\admin\\extend\\ExcelExtend' => $vendorDir . '/zoujingli/think-library/src/extend/ExcelExtend.php',
    'think\\admin\\extend\\FaviconExtend' => $vendorDir . '/zoujingli/think-library/src/extend/FaviconExtend.php',
    'think\\admin\\extend\\HttpExtend' => $vendorDir . '/zoujingli/think-library/src/extend/HttpExtend.php',
    'think\\admin\\extend\\ImageVerify' => $vendorDir . '/zoujingli/think-library/src/extend/ImageVerify.php',
    'think\\admin\\extend\\JsonRpcClient' => $vendorDir . '/zoujingli/think-library/src/extend/JsonRpcClient.php',
    'think\\admin\\extend\\JsonRpcServer' => $vendorDir . '/zoujingli/think-library/src/extend/JsonRpcServer.php',
    'think\\admin\\extend\\JwtExtend' => $vendorDir . '/zoujingli/think-library/src/extend/JwtExtend.php',
    'think\\admin\\extend\\PhinxExtend' => $vendorDir . '/zoujingli/think-library/src/extend/PhinxExtend.php',
    'think\\admin\\extend\\ToolsExtend' => $vendorDir . '/zoujingli/think-library/src/extend/ToolsExtend.php',
    'think\\admin\\extend\\VirtualModel' => $vendorDir . '/zoujingli/think-library/src/extend/VirtualModel.php',
    'think\\admin\\helper\\DeleteHelper' => $vendorDir . '/zoujingli/think-library/src/helper/DeleteHelper.php',
    'think\\admin\\helper\\FormHelper' => $vendorDir . '/zoujingli/think-library/src/helper/FormHelper.php',
    'think\\admin\\helper\\PageHelper' => $vendorDir . '/zoujingli/think-library/src/helper/PageHelper.php',
    'think\\admin\\helper\\QueryHelper' => $vendorDir . '/zoujingli/think-library/src/helper/QueryHelper.php',
    'think\\admin\\helper\\SaveHelper' => $vendorDir . '/zoujingli/think-library/src/helper/SaveHelper.php',
    'think\\admin\\helper\\TokenHelper' => $vendorDir . '/zoujingli/think-library/src/helper/TokenHelper.php',
    'think\\admin\\helper\\ValidateHelper' => $vendorDir . '/zoujingli/think-library/src/helper/ValidateHelper.php',
    'think\\admin\\install\\Installer' => $vendorDir . '/zoujingli/think-install/src/Installer.php',
    'think\\admin\\install\\Service' => $vendorDir . '/zoujingli/think-install/src/Service.php',
    'think\\admin\\install\\Support' => $vendorDir . '/zoujingli/think-install/src/Support.php',
    'think\\admin\\model\\SystemAuth' => $vendorDir . '/zoujingli/think-library/src/model/SystemAuth.php',
    'think\\admin\\model\\SystemBase' => $vendorDir . '/zoujingli/think-library/src/model/SystemBase.php',
    'think\\admin\\model\\SystemConfig' => $vendorDir . '/zoujingli/think-library/src/model/SystemConfig.php',
    'think\\admin\\model\\SystemData' => $vendorDir . '/zoujingli/think-library/src/model/SystemData.php',
    'think\\admin\\model\\SystemFile' => $vendorDir . '/zoujingli/think-library/src/model/SystemFile.php',
    'think\\admin\\model\\SystemMenu' => $vendorDir . '/zoujingli/think-library/src/model/SystemMenu.php',
    'think\\admin\\model\\SystemNode' => $vendorDir . '/zoujingli/think-library/src/model/SystemNode.php',
    'think\\admin\\model\\SystemOplog' => $vendorDir . '/zoujingli/think-library/src/model/SystemOplog.php',
    'think\\admin\\model\\SystemQueue' => $vendorDir . '/zoujingli/think-library/src/model/SystemQueue.php',
    'think\\admin\\model\\SystemUser' => $vendorDir . '/zoujingli/think-library/src/model/SystemUser.php',
    'think\\admin\\service\\AdminService' => $vendorDir . '/zoujingli/think-library/src/service/AdminService.php',
    'think\\admin\\service\\CaptchaService' => $vendorDir . '/zoujingli/think-library/src/service/CaptchaService.php',
    'think\\admin\\service\\ExpressService' => $vendorDir . '/zoujingli/think-library/src/service/ExpressService.php',
    'think\\admin\\service\\InterfaceService' => $vendorDir . '/zoujingli/think-library/src/service/InterfaceService.php',
    'think\\admin\\service\\MenuService' => $vendorDir . '/zoujingli/think-library/src/service/MenuService.php',
    'think\\admin\\service\\MessageService' => $vendorDir . '/zoujingli/think-library/src/service/MessageService.php',
    'think\\admin\\service\\ModuleService' => $vendorDir . '/zoujingli/think-library/src/service/ModuleService.php',
    'think\\admin\\service\\NodeService' => $vendorDir . '/zoujingli/think-library/src/service/NodeService.php',
    'think\\admin\\service\\ProcessService' => $vendorDir . '/zoujingli/think-library/src/service/ProcessService.php',
    'think\\admin\\service\\QueueService' => $vendorDir . '/zoujingli/think-library/src/service/QueueService.php',
    'think\\admin\\service\\RuntimeService' => $vendorDir . '/zoujingli/think-library/src/service/RuntimeService.php',
    'think\\admin\\service\\SystemService' => $vendorDir . '/zoujingli/think-library/src/service/SystemService.php',
    'think\\admin\\service\\ZtSmsService' => $vendorDir . '/zoujingli/think-library/src/service/ZtSmsService.php',
    'think\\admin\\storage\\AliossStorage' => $vendorDir . '/zoujingli/think-library/src/storage/AliossStorage.php',
    'think\\admin\\storage\\AlistStorage' => $vendorDir . '/zoujingli/think-library/src/storage/AlistStorage.php',
    'think\\admin\\storage\\LocalStorage' => $vendorDir . '/zoujingli/think-library/src/storage/LocalStorage.php',
    'think\\admin\\storage\\QiniuStorage' => $vendorDir . '/zoujingli/think-library/src/storage/QiniuStorage.php',
    'think\\admin\\storage\\TxcosStorage' => $vendorDir . '/zoujingli/think-library/src/storage/TxcosStorage.php',
    'think\\admin\\storage\\UpyunStorage' => $vendorDir . '/zoujingli/think-library/src/storage/UpyunStorage.php',
    'think\\admin\\support\\Route' => $vendorDir . '/zoujingli/think-library/src/support/Route.php',
    'think\\admin\\support\\Url' => $vendorDir . '/zoujingli/think-library/src/support/Url.php',
    'think\\admin\\support\\command\\Database' => $vendorDir . '/zoujingli/think-library/src/support/command/Database.php',
    'think\\admin\\support\\command\\Package' => $vendorDir . '/zoujingli/think-library/src/support/command/Package.php',
    'think\\admin\\support\\command\\Publish' => $vendorDir . '/zoujingli/think-library/src/support/command/Publish.php',
    'think\\admin\\support\\command\\Queue' => $vendorDir . '/zoujingli/think-library/src/support/command/Queue.php',
    'think\\admin\\support\\command\\Replace' => $vendorDir . '/zoujingli/think-library/src/support/command/Replace.php',
    'think\\admin\\support\\command\\Sysmenu' => $vendorDir . '/zoujingli/think-library/src/support/command/Sysmenu.php',
    'think\\admin\\support\\middleware\\JwtSession' => $vendorDir . '/zoujingli/think-library/src/support/middleware/JwtSession.php',
    'think\\admin\\support\\middleware\\MultAccess' => $vendorDir . '/zoujingli/think-library/src/support/middleware/MultAccess.php',
    'think\\admin\\support\\middleware\\RbacAccess' => $vendorDir . '/zoujingli/think-library/src/support/middleware/RbacAccess.php',
    'think\\cache\\Driver' => $vendorDir . '/topthink/framework/src/think/cache/Driver.php',
    'think\\cache\\TagSet' => $vendorDir . '/topthink/framework/src/think/cache/TagSet.php',
    'think\\cache\\driver\\File' => $vendorDir . '/topthink/framework/src/think/cache/driver/File.php',
    'think\\cache\\driver\\Memcache' => $vendorDir . '/topthink/framework/src/think/cache/driver/Memcache.php',
    'think\\cache\\driver\\Memcached' => $vendorDir . '/topthink/framework/src/think/cache/driver/Memcached.php',
    'think\\cache\\driver\\Redis' => $vendorDir . '/topthink/framework/src/think/cache/driver/Redis.php',
    'think\\cache\\driver\\Wincache' => $vendorDir . '/topthink/framework/src/think/cache/driver/Wincache.php',
    'think\\console\\Command' => $vendorDir . '/topthink/framework/src/think/console/Command.php',
    'think\\console\\Input' => $vendorDir . '/topthink/framework/src/think/console/Input.php',
    'think\\console\\Output' => $vendorDir . '/topthink/framework/src/think/console/Output.php',
    'think\\console\\Table' => $vendorDir . '/topthink/framework/src/think/console/Table.php',
    'think\\console\\command\\Clear' => $vendorDir . '/topthink/framework/src/think/console/command/Clear.php',
    'think\\console\\command\\Help' => $vendorDir . '/topthink/framework/src/think/console/command/Help.php',
    'think\\console\\command\\Lists' => $vendorDir . '/topthink/framework/src/think/console/command/Lists.php',
    'think\\console\\command\\Make' => $vendorDir . '/topthink/framework/src/think/console/command/Make.php',
    'think\\console\\command\\RouteList' => $vendorDir . '/topthink/framework/src/think/console/command/RouteList.php',
    'think\\console\\command\\RunServer' => $vendorDir . '/topthink/framework/src/think/console/command/RunServer.php',
    'think\\console\\command\\ServiceDiscover' => $vendorDir . '/topthink/framework/src/think/console/command/ServiceDiscover.php',
    'think\\console\\command\\VendorPublish' => $vendorDir . '/topthink/framework/src/think/console/command/VendorPublish.php',
    'think\\console\\command\\Version' => $vendorDir . '/topthink/framework/src/think/console/command/Version.php',
    'think\\console\\command\\make\\Command' => $vendorDir . '/topthink/framework/src/think/console/command/make/Command.php',
    'think\\console\\command\\make\\Controller' => $vendorDir . '/topthink/framework/src/think/console/command/make/Controller.php',
    'think\\console\\command\\make\\Event' => $vendorDir . '/topthink/framework/src/think/console/command/make/Event.php',
    'think\\console\\command\\make\\Listener' => $vendorDir . '/topthink/framework/src/think/console/command/make/Listener.php',
    'think\\console\\command\\make\\Middleware' => $vendorDir . '/topthink/framework/src/think/console/command/make/Middleware.php',
    'think\\console\\command\\make\\Model' => $vendorDir . '/topthink/framework/src/think/console/command/make/Model.php',
    'think\\console\\command\\make\\Service' => $vendorDir . '/topthink/framework/src/think/console/command/make/Service.php',
    'think\\console\\command\\make\\Subscribe' => $vendorDir . '/topthink/framework/src/think/console/command/make/Subscribe.php',
    'think\\console\\command\\make\\Validate' => $vendorDir . '/topthink/framework/src/think/console/command/make/Validate.php',
    'think\\console\\command\\optimize\\Config' => $vendorDir . '/topthink/framework/src/think/console/command/optimize/Config.php',
    'think\\console\\command\\optimize\\Route' => $vendorDir . '/topthink/framework/src/think/console/command/optimize/Route.php',
    'think\\console\\command\\optimize\\Schema' => $vendorDir . '/topthink/framework/src/think/console/command/optimize/Schema.php',
    'think\\console\\input\\Argument' => $vendorDir . '/topthink/framework/src/think/console/input/Argument.php',
    'think\\console\\input\\Definition' => $vendorDir . '/topthink/framework/src/think/console/input/Definition.php',
    'think\\console\\input\\Option' => $vendorDir . '/topthink/framework/src/think/console/input/Option.php',
    'think\\console\\output\\Ask' => $vendorDir . '/topthink/framework/src/think/console/output/Ask.php',
    'think\\console\\output\\Descriptor' => $vendorDir . '/topthink/framework/src/think/console/output/Descriptor.php',
    'think\\console\\output\\Formatter' => $vendorDir . '/topthink/framework/src/think/console/output/Formatter.php',
    'think\\console\\output\\Question' => $vendorDir . '/topthink/framework/src/think/console/output/Question.php',
    'think\\console\\output\\descriptor\\Console' => $vendorDir . '/topthink/framework/src/think/console/output/descriptor/Console.php',
    'think\\console\\output\\driver\\Buffer' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Buffer.php',
    'think\\console\\output\\driver\\Console' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Console.php',
    'think\\console\\output\\driver\\Nothing' => $vendorDir . '/topthink/framework/src/think/console/output/driver/Nothing.php',
    'think\\console\\output\\formatter\\Stack' => $vendorDir . '/topthink/framework/src/think/console/output/formatter/Stack.php',
    'think\\console\\output\\formatter\\Style' => $vendorDir . '/topthink/framework/src/think/console/output/formatter/Style.php',
    'think\\console\\output\\question\\Choice' => $vendorDir . '/topthink/framework/src/think/console/output/question/Choice.php',
    'think\\console\\output\\question\\Confirmation' => $vendorDir . '/topthink/framework/src/think/console/output/question/Confirmation.php',
    'think\\contract\\Arrayable' => $vendorDir . '/topthink/think-helper/src/contract/Arrayable.php',
    'think\\contract\\CacheHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/CacheHandlerInterface.php',
    'think\\contract\\Enumable' => $vendorDir . '/topthink/think-validate/src/contract/Enumable.php',
    'think\\contract\\Jsonable' => $vendorDir . '/topthink/think-helper/src/contract/Jsonable.php',
    'think\\contract\\LogHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/LogHandlerInterface.php',
    'think\\contract\\ModelRelationInterface' => $vendorDir . '/topthink/framework/src/think/contract/ModelRelationInterface.php',
    'think\\contract\\SessionHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/SessionHandlerInterface.php',
    'think\\contract\\TemplateHandlerInterface' => $vendorDir . '/topthink/framework/src/think/contract/TemplateHandlerInterface.php',
    'think\\db\\BaseBuilder' => $vendorDir . '/topthink/think-orm/src/db/BaseBuilder.php',
    'think\\db\\BaseQuery' => $vendorDir . '/topthink/think-orm/src/db/BaseQuery.php',
    'think\\db\\Builder' => $vendorDir . '/topthink/think-orm/src/db/Builder.php',
    'think\\db\\CacheItem' => $vendorDir . '/topthink/think-orm/src/db/CacheItem.php',
    'think\\db\\Connection' => $vendorDir . '/topthink/think-orm/src/db/Connection.php',
    'think\\db\\ConnectionInterface' => $vendorDir . '/topthink/think-orm/src/db/ConnectionInterface.php',
    'think\\db\\Fetch' => $vendorDir . '/topthink/think-orm/src/db/Fetch.php',
    'think\\db\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/Mongo.php',
    'think\\db\\PDOConnection' => $vendorDir . '/topthink/think-orm/src/db/PDOConnection.php',
    'think\\db\\Query' => $vendorDir . '/topthink/think-orm/src/db/Query.php',
    'think\\db\\Raw' => $vendorDir . '/topthink/think-orm/src/db/Raw.php',
    'think\\db\\Where' => $vendorDir . '/topthink/think-orm/src/db/Where.php',
    'think\\db\\builder\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/builder/Mongo.php',
    'think\\db\\builder\\Mysql' => $vendorDir . '/topthink/think-orm/src/db/builder/Mysql.php',
    'think\\db\\builder\\Oracle' => $vendorDir . '/topthink/think-orm/src/db/builder/Oracle.php',
    'think\\db\\builder\\Pgsql' => $vendorDir . '/topthink/think-orm/src/db/builder/Pgsql.php',
    'think\\db\\builder\\Sqlite' => $vendorDir . '/topthink/think-orm/src/db/builder/Sqlite.php',
    'think\\db\\builder\\Sqlsrv' => $vendorDir . '/topthink/think-orm/src/db/builder/Sqlsrv.php',
    'think\\db\\concern\\AggregateQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/AggregateQuery.php',
    'think\\db\\concern\\JoinAndViewQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/JoinAndViewQuery.php',
    'think\\db\\concern\\ModelRelationQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/ModelRelationQuery.php',
    'think\\db\\concern\\ParamsBind' => $vendorDir . '/topthink/think-orm/src/db/concern/ParamsBind.php',
    'think\\db\\concern\\ResultOperation' => $vendorDir . '/topthink/think-orm/src/db/concern/ResultOperation.php',
    'think\\db\\concern\\TableFieldInfo' => $vendorDir . '/topthink/think-orm/src/db/concern/TableFieldInfo.php',
    'think\\db\\concern\\TimeFieldQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/TimeFieldQuery.php',
    'think\\db\\concern\\Transaction' => $vendorDir . '/topthink/think-orm/src/db/concern/Transaction.php',
    'think\\db\\concern\\WhereQuery' => $vendorDir . '/topthink/think-orm/src/db/concern/WhereQuery.php',
    'think\\db\\connector\\Mongo' => $vendorDir . '/topthink/think-orm/src/db/connector/Mongo.php',
    'think\\db\\connector\\Mysql' => $vendorDir . '/topthink/think-orm/src/db/connector/Mysql.php',
    'think\\db\\connector\\Oracle' => $vendorDir . '/topthink/think-orm/src/db/connector/Oracle.php',
    'think\\db\\connector\\Pgsql' => $vendorDir . '/topthink/think-orm/src/db/connector/Pgsql.php',
    'think\\db\\connector\\Sqlite' => $vendorDir . '/topthink/think-orm/src/db/connector/Sqlite.php',
    'think\\db\\connector\\Sqlsrv' => $vendorDir . '/topthink/think-orm/src/db/connector/Sqlsrv.php',
    'think\\db\\exception\\BindParamException' => $vendorDir . '/topthink/think-orm/src/db/exception/BindParamException.php',
    'think\\db\\exception\\DataNotFoundException' => $vendorDir . '/topthink/think-orm/src/db/exception/DataNotFoundException.php',
    'think\\db\\exception\\DbEventException' => $vendorDir . '/topthink/think-orm/src/db/exception/DbEventException.php',
    'think\\db\\exception\\DbException' => $vendorDir . '/topthink/think-orm/src/db/exception/DbException.php',
    'think\\db\\exception\\InvalidArgumentException' => $vendorDir . '/topthink/think-orm/src/db/exception/InvalidArgumentException.php',
    'think\\db\\exception\\ModelEventException' => $vendorDir . '/topthink/think-orm/src/db/exception/ModelEventException.php',
    'think\\db\\exception\\ModelNotFoundException' => $vendorDir . '/topthink/think-orm/src/db/exception/ModelNotFoundException.php',
    'think\\db\\exception\\PDOException' => $vendorDir . '/topthink/think-orm/src/db/exception/PDOException.php',
    'think\\event\\AppInit' => $vendorDir . '/topthink/framework/src/think/event/AppInit.php',
    'think\\event\\HttpEnd' => $vendorDir . '/topthink/framework/src/think/event/HttpEnd.php',
    'think\\event\\HttpRun' => $vendorDir . '/topthink/framework/src/think/event/HttpRun.php',
    'think\\event\\LogRecord' => $vendorDir . '/topthink/framework/src/think/event/LogRecord.php',
    'think\\event\\LogWrite' => $vendorDir . '/topthink/framework/src/think/event/LogWrite.php',
    'think\\event\\RouteLoaded' => $vendorDir . '/topthink/framework/src/think/event/RouteLoaded.php',
    'think\\exception\\ClassNotFoundException' => $vendorDir . '/topthink/think-container/src/exception/ClassNotFoundException.php',
    'think\\exception\\ErrorException' => $vendorDir . '/topthink/framework/src/think/exception/ErrorException.php',
    'think\\exception\\FileException' => $vendorDir . '/topthink/framework/src/think/exception/FileException.php',
    'think\\exception\\FuncNotFoundException' => $vendorDir . '/topthink/think-container/src/exception/FuncNotFoundException.php',
    'think\\exception\\Handle' => $vendorDir . '/topthink/framework/src/think/exception/Handle.php',
    'think\\exception\\HttpException' => $vendorDir . '/topthink/framework/src/think/exception/HttpException.php',
    'think\\exception\\HttpResponseException' => $vendorDir . '/topthink/framework/src/think/exception/HttpResponseException.php',
    'think\\exception\\InvalidArgumentException' => $vendorDir . '/topthink/framework/src/think/exception/InvalidArgumentException.php',
    'think\\exception\\InvalidCacheException' => $vendorDir . '/topthink/framework/src/think/exception/InvalidCacheException.php',
    'think\\exception\\RouteNotFoundException' => $vendorDir . '/topthink/framework/src/think/exception/RouteNotFoundException.php',
    'think\\exception\\ValidateException' => $vendorDir . '/topthink/think-validate/src/exception/ValidateException.php',
    'think\\facade\\App' => $vendorDir . '/topthink/framework/src/think/facade/App.php',
    'think\\facade\\Cache' => $vendorDir . '/topthink/framework/src/think/facade/Cache.php',
    'think\\facade\\Config' => $vendorDir . '/topthink/framework/src/think/facade/Config.php',
    'think\\facade\\Console' => $vendorDir . '/topthink/framework/src/think/facade/Console.php',
    'think\\facade\\Cookie' => $vendorDir . '/topthink/framework/src/think/facade/Cookie.php',
    'think\\facade\\Db' => $vendorDir . '/topthink/think-orm/src/facade/Db.php',
    'think\\facade\\Env' => $vendorDir . '/topthink/framework/src/think/facade/Env.php',
    'think\\facade\\Event' => $vendorDir . '/topthink/framework/src/think/facade/Event.php',
    'think\\facade\\Lang' => $vendorDir . '/topthink/framework/src/think/facade/Lang.php',
    'think\\facade\\Log' => $vendorDir . '/topthink/framework/src/think/facade/Log.php',
    'think\\facade\\Middleware' => $vendorDir . '/topthink/framework/src/think/facade/Middleware.php',
    'think\\facade\\Request' => $vendorDir . '/topthink/framework/src/think/facade/Request.php',
    'think\\facade\\Route' => $vendorDir . '/topthink/framework/src/think/facade/Route.php',
    'think\\facade\\Session' => $vendorDir . '/topthink/framework/src/think/facade/Session.php',
    'think\\facade\\Template' => $vendorDir . '/topthink/think-template/src/facade/Template.php',
    'think\\facade\\Validate' => $vendorDir . '/topthink/think-validate/src/facade/Validate.php',
    'think\\facade\\View' => $vendorDir . '/topthink/framework/src/think/facade/View.php',
    'think\\file\\UploadedFile' => $vendorDir . '/topthink/framework/src/think/file/UploadedFile.php',
    'think\\helper\\Arr' => $vendorDir . '/topthink/think-helper/src/helper/Arr.php',
    'think\\helper\\Macroable' => $vendorDir . '/topthink/think-helper/src/helper/Macroable.php',
    'think\\helper\\Str' => $vendorDir . '/topthink/think-helper/src/helper/Str.php',
    'think\\initializer\\BootService' => $vendorDir . '/topthink/framework/src/think/initializer/BootService.php',
    'think\\initializer\\Error' => $vendorDir . '/topthink/framework/src/think/initializer/Error.php',
    'think\\initializer\\RegisterService' => $vendorDir . '/topthink/framework/src/think/initializer/RegisterService.php',
    'think\\log\\Channel' => $vendorDir . '/topthink/framework/src/think/log/Channel.php',
    'think\\log\\ChannelSet' => $vendorDir . '/topthink/framework/src/think/log/ChannelSet.php',
    'think\\log\\driver\\File' => $vendorDir . '/topthink/framework/src/think/log/driver/File.php',
    'think\\middleware\\AllowCrossDomain' => $vendorDir . '/topthink/framework/src/think/middleware/AllowCrossDomain.php',
    'think\\middleware\\CheckRequestCache' => $vendorDir . '/topthink/framework/src/think/middleware/CheckRequestCache.php',
    'think\\middleware\\FormTokenCheck' => $vendorDir . '/topthink/framework/src/think/middleware/FormTokenCheck.php',
    'think\\middleware\\LoadLangPack' => $vendorDir . '/topthink/framework/src/think/middleware/LoadLangPack.php',
    'think\\middleware\\SessionInit' => $vendorDir . '/topthink/framework/src/think/middleware/SessionInit.php',
    'think\\migration\\Command' => $vendorDir . '/topthink/think-migration/src/Command.php',
    'think\\migration\\Creator' => $vendorDir . '/topthink/think-migration/src/Creator.php',
    'think\\migration\\Factory' => $vendorDir . '/topthink/think-migration/src/Factory.php',
    'think\\migration\\FactoryBuilder' => $vendorDir . '/topthink/think-migration/src/FactoryBuilder.php',
    'think\\migration\\Migrator' => $vendorDir . '/topthink/think-migration/src/Migrator.php',
    'think\\migration\\NullOutput' => $vendorDir . '/topthink/think-migration/src/NullOutput.php',
    'think\\migration\\Seeder' => $vendorDir . '/topthink/think-migration/src/Seeder.php',
    'think\\migration\\Service' => $vendorDir . '/topthink/think-migration/src/Service.php',
    'think\\migration\\UsePhinx' => $vendorDir . '/topthink/think-migration/src/UsePhinx.php',
    'think\\migration\\command\\Migrate' => $vendorDir . '/topthink/think-migration/src/command/Migrate.php',
    'think\\migration\\command\\Seed' => $vendorDir . '/topthink/think-migration/src/command/Seed.php',
    'think\\migration\\command\\factory\\Create' => $vendorDir . '/topthink/think-migration/src/command/factory/Create.php',
    'think\\migration\\command\\migrate\\Breakpoint' => $vendorDir . '/topthink/think-migration/src/command/migrate/Breakpoint.php',
    'think\\migration\\command\\migrate\\Create' => $vendorDir . '/topthink/think-migration/src/command/migrate/Create.php',
    'think\\migration\\command\\migrate\\Rollback' => $vendorDir . '/topthink/think-migration/src/command/migrate/Rollback.php',
    'think\\migration\\command\\migrate\\Run' => $vendorDir . '/topthink/think-migration/src/command/migrate/Run.php',
    'think\\migration\\command\\migrate\\Status' => $vendorDir . '/topthink/think-migration/src/command/migrate/Status.php',
    'think\\migration\\command\\seed\\Create' => $vendorDir . '/topthink/think-migration/src/command/seed/Create.php',
    'think\\migration\\command\\seed\\Run' => $vendorDir . '/topthink/think-migration/src/command/seed/Run.php',
    'think\\migration\\db\\Column' => $vendorDir . '/topthink/think-migration/src/db/Column.php',
    'think\\migration\\db\\Table' => $vendorDir . '/topthink/think-migration/src/db/Table.php',
    'think\\model\\Collection' => $vendorDir . '/topthink/think-orm/src/model/Collection.php',
    'think\\model\\Pivot' => $vendorDir . '/topthink/think-orm/src/model/Pivot.php',
    'think\\model\\Relation' => $vendorDir . '/topthink/think-orm/src/model/Relation.php',
    'think\\model\\concern\\Attribute' => $vendorDir . '/topthink/think-orm/src/model/concern/Attribute.php',
    'think\\model\\concern\\AutoWriteId' => $vendorDir . '/topthink/think-orm/src/model/concern/AutoWriteId.php',
    'think\\model\\concern\\Conversion' => $vendorDir . '/topthink/think-orm/src/model/concern/Conversion.php',
    'think\\model\\concern\\ModelEvent' => $vendorDir . '/topthink/think-orm/src/model/concern/ModelEvent.php',
    'think\\model\\concern\\OptimLock' => $vendorDir . '/topthink/think-orm/src/model/concern/OptimLock.php',
    'think\\model\\concern\\RelationShip' => $vendorDir . '/topthink/think-orm/src/model/concern/RelationShip.php',
    'think\\model\\concern\\SoftDelete' => $vendorDir . '/topthink/think-orm/src/model/concern/SoftDelete.php',
    'think\\model\\concern\\TimeStamp' => $vendorDir . '/topthink/think-orm/src/model/concern/TimeStamp.php',
    'think\\model\\concern\\Virtual' => $vendorDir . '/topthink/think-orm/src/model/concern/Virtual.php',
    'think\\model\\contract\\EnumTransform' => $vendorDir . '/topthink/think-orm/src/model/contract/EnumTransform.php',
    'think\\model\\contract\\FieldTypeTransform' => $vendorDir . '/topthink/think-orm/src/model/contract/FieldTypeTransform.php',
    'think\\model\\relation\\BelongsTo' => $vendorDir . '/topthink/think-orm/src/model/relation/BelongsTo.php',
    'think\\model\\relation\\BelongsToMany' => $vendorDir . '/topthink/think-orm/src/model/relation/BelongsToMany.php',
    'think\\model\\relation\\HasMany' => $vendorDir . '/topthink/think-orm/src/model/relation/HasMany.php',
    'think\\model\\relation\\HasManyThrough' => $vendorDir . '/topthink/think-orm/src/model/relation/HasManyThrough.php',
    'think\\model\\relation\\HasOne' => $vendorDir . '/topthink/think-orm/src/model/relation/HasOne.php',
    'think\\model\\relation\\HasOneThrough' => $vendorDir . '/topthink/think-orm/src/model/relation/HasOneThrough.php',
    'think\\model\\relation\\MorphMany' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphMany.php',
    'think\\model\\relation\\MorphOne' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphOne.php',
    'think\\model\\relation\\MorphTo' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphTo.php',
    'think\\model\\relation\\MorphToMany' => $vendorDir . '/topthink/think-orm/src/model/relation/MorphToMany.php',
    'think\\model\\relation\\OneToOne' => $vendorDir . '/topthink/think-orm/src/model/relation/OneToOne.php',
    'think\\paginator\\driver\\Bootstrap' => $vendorDir . '/topthink/think-orm/src/paginator/driver/Bootstrap.php',
    'think\\response\\File' => $vendorDir . '/topthink/framework/src/think/response/File.php',
    'think\\response\\Html' => $vendorDir . '/topthink/framework/src/think/response/Html.php',
    'think\\response\\Json' => $vendorDir . '/topthink/framework/src/think/response/Json.php',
    'think\\response\\Jsonp' => $vendorDir . '/topthink/framework/src/think/response/Jsonp.php',
    'think\\response\\Redirect' => $vendorDir . '/topthink/framework/src/think/response/Redirect.php',
    'think\\response\\View' => $vendorDir . '/topthink/framework/src/think/response/View.php',
    'think\\response\\Xml' => $vendorDir . '/topthink/framework/src/think/response/Xml.php',
    'think\\route\\Dispatch' => $vendorDir . '/topthink/framework/src/think/route/Dispatch.php',
    'think\\route\\Domain' => $vendorDir . '/topthink/framework/src/think/route/Domain.php',
    'think\\route\\Resource' => $vendorDir . '/topthink/framework/src/think/route/Resource.php',
    'think\\route\\ResourceRegister' => $vendorDir . '/topthink/framework/src/think/route/ResourceRegister.php',
    'think\\route\\Rule' => $vendorDir . '/topthink/framework/src/think/route/Rule.php',
    'think\\route\\RuleGroup' => $vendorDir . '/topthink/framework/src/think/route/RuleGroup.php',
    'think\\route\\RuleItem' => $vendorDir . '/topthink/framework/src/think/route/RuleItem.php',
    'think\\route\\RuleName' => $vendorDir . '/topthink/framework/src/think/route/RuleName.php',
    'think\\route\\Url' => $vendorDir . '/topthink/framework/src/think/route/Url.php',
    'think\\route\\dispatch\\Callback' => $vendorDir . '/topthink/framework/src/think/route/dispatch/Callback.php',
    'think\\route\\dispatch\\Controller' => $vendorDir . '/topthink/framework/src/think/route/dispatch/Controller.php',
    'think\\service\\ModelService' => $vendorDir . '/topthink/framework/src/think/service/ModelService.php',
    'think\\service\\PaginatorService' => $vendorDir . '/topthink/framework/src/think/service/PaginatorService.php',
    'think\\service\\ValidateService' => $vendorDir . '/topthink/framework/src/think/service/ValidateService.php',
    'think\\session\\Store' => $vendorDir . '/topthink/framework/src/think/session/Store.php',
    'think\\session\\driver\\Cache' => $vendorDir . '/topthink/framework/src/think/session/driver/Cache.php',
    'think\\session\\driver\\File' => $vendorDir . '/topthink/framework/src/think/session/driver/File.php',
    'think\\template\\TagLib' => $vendorDir . '/topthink/think-template/src/template/TagLib.php',
    'think\\template\\contract\\DriverInterface' => $vendorDir . '/topthink/think-template/src/template/contract/DriverInterface.php',
    'think\\template\\driver\\File' => $vendorDir . '/topthink/think-template/src/template/driver/File.php',
    'think\\template\\exception\\TemplateNotFoundException' => $vendorDir . '/topthink/think-template/src/template/exception/TemplateNotFoundException.php',
    'think\\template\\taglib\\Cx' => $vendorDir . '/topthink/think-template/src/template/taglib/Cx.php',
    'think\\validate\\ValidateRule' => $vendorDir . '/topthink/think-validate/src/validate/ValidateRule.php',
    'think\\validate\\ValidateRuleSet' => $vendorDir . '/topthink/think-validate/src/validate/ValidateRuleSet.php',
    'think\\view\\driver\\Php' => $vendorDir . '/topthink/framework/src/think/view/driver/Php.php',
    'think\\view\\driver\\Think' => $vendorDir . '/topthink/think-view/src/Think.php',
);
