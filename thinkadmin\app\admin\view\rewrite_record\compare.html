{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">降重对比</div>
    <div class="layui-card-body">
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>原始文本</h3>
                        <p>字数：{$task.original_word_count|default=0} 字</p>
                    </div>
                    <div class="layui-card-body">
                        <div style="white-space: pre-wrap; background: #f8f8f8; padding: 15px; border-radius: 4px; min-height: 400px; max-height: 600px; overflow-y: auto; font-size: 14px; line-height: 1.6;">
{$task.original_text|default=''}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>降重后文本</h3>
                        <p>
                            字数：{$result.rewritten_word_count|default=0} 字 | 
                            AI模型：{$result.aiModel.name|default='未知'} | 
                            相似度：{$result.similarity_score|default='-'}% | 
                            质量分数：{$result.quality_score|default='-'}%
                        </p>
                    </div>
                    <div class="layui-card-body">
                        <div style="white-space: pre-wrap; background: #f0f9ff; padding: 15px; border-radius: 4px; min-height: 400px; max-height: 600px; overflow-y: auto; font-size: 14px; line-height: 1.6;">
{$result.rewritten_text|default=''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hr-line-dashed"></div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>对比统计</h3>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="line">
                            <thead>
                            <tr>
                                <th>项目</th>
                                <th>原始文本</th>
                                <th>降重后文本</th>
                                <th>变化</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>字数</td>
                                <td>{$task.original_word_count|default=0} 字</td>
                                <td>{$result.rewritten_word_count|default=0} 字</td>
                                <td>
                                    {php}
                                    $change = ($result['rewritten_word_count'] ?? 0) - ($task['original_word_count'] ?? 0);
                                    $changePercent = ($task['original_word_count'] ?? 0) > 0 ? round($change / $task['original_word_count'] * 100, 2) : 0;
                                    echo $change >= 0 ? '+' . $change : $change;
                                    echo ' (' . ($changePercent >= 0 ? '+' : '') . $changePercent . '%)';
                                    {/php}
                                </td>
                            </tr>
                            <tr>
                                <td>相似度</td>
                                <td>-</td>
                                <td>{$result.similarity_score|default='-'}%</td>
                                <td>
                                    {if !empty($result.similarity_score)}
                                    {if $result.similarity_score <= $task.target_similarity}
                                    <span class="layui-badge layui-bg-green">达标</span>
                                    {else}
                                    <span class="layui-badge layui-bg-red">未达标</span>
                                    {/if}
                                    {else}
                                    -
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>质量分数</td>
                                <td>-</td>
                                <td>{$result.quality_score|default='-'}%</td>
                                <td>
                                    {if !empty($result.quality_score)}
                                    {if $result.quality_score >= 80}
                                    <span class="layui-badge layui-bg-green">优秀</span>
                                    {elseif $result.quality_score >= 60}
                                    <span class="layui-badge layui-bg-blue">良好</span>
                                    {else}
                                    <span class="layui-badge layui-bg-orange">一般</span>
                                    {/if}
                                    {else}
                                    -
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>处理时间</td>
                                <td>-</td>
                                <td>{$result.processing_time|default=0} 秒</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>是否选中</td>
                                <td>-</td>
                                <td>
                                    {if $result.is_selected eq 1}
                                    <span class="layui-badge layui-bg-green">是</span>
                                    {else}
                                    <span class="layui-badge">否</span>
                                    {/if}
                                </td>
                                <td>-</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hr-line-dashed"></div>
        
        <div class="layui-form-item text-center">
            <button class="layui-btn layui-btn-danger" type='button' data-close>关闭窗口</button>
        </div>
        
    </div>
</div>

<style>
.layui-card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}
.layui-card-header p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 12px;
}
</style>
{/block}
