<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\PaperType as PaperTypeModel;
use app\admin\model\OutlineTemplate;

/**
 * 论文类型管理
 * @class PaperType
 * @package app\admin\controller
 */
class PaperType extends Controller
{
    /**
     * 论文类型管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        try {
            PaperTypeModel::mQuery()->layTable(function () {
                $this->title = '论文类型管理';
            }, static function (QueryHelper $query) {
                $query->where(['status' => 1]);
                $query->like('name,description')->equal('status');
                $query->order('sort asc,id desc');
            });
        } catch (\Exception $e) {
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = '论文类型管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 添加论文类型
     * @auth true
     */
    public function add()
    {
        PaperTypeModel::mForm('paper_type/form');
    }

    /**
     * 编辑论文类型
     * @auth true
     */
    public function edit()
    {
        PaperTypeModel::mForm('paper_type/form');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 获取大纲模板列表
            $this->outline_templates = OutlineTemplate::mk()->where(['status' => 1])->column('name', 'id');
            // 获取提示词模板列表 (暂时为空数组，因为还没有创建PromptTemplate模型)
            $this->prompt_templates = [];
        } else {
            // POST请求时的数据验证
            $map = [];
            $map[] = ['name', '=', $data['name']];
            $map[] = ['id', '<>', $data['id'] ?? 0];
            if (PaperTypeModel::mk()->where($map)->count() > 0) {
                $this->error("论文类型名称已经存在！");
            }
            
            // 设置创建和更新时间
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
            }
            $data['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 修改论文类型状态
     * @auth true
     */
    public function state()
    {
        PaperTypeModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除论文类型
     * @auth true
     */
    public function remove()
    {
        PaperTypeModel::mDelete();
    }
}
