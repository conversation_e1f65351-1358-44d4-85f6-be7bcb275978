<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\PaperProject as PaperProjectModel;
use app\admin\model\PaperType;

/**
 * 论文项目管理
 * @class PaperProject
 * @package app\admin\controller
 */
class PaperProject extends Controller
{
    /**
     * 论文项目管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        PaperProjectModel::mQuery()->layTable(function () {
            $this->title = '论文项目管理';
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
        }, static function (QueryHelper $query) {
            $query->like('title,subject,keywords')->equal('paper_type_id,status,user_id');
            $query->equal('is_draft', 0); // 只显示正式项目
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 草稿箱管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function draft()
    {
        PaperProjectModel::mQuery()->layTable(function () {
            $this->title = '草稿箱管理';
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
        }, static function (QueryHelper $query) {
            $query->like('title,subject,keywords')->equal('paper_type_id,status,user_id');
            $query->equal('is_draft', 1); // 只显示草稿
            $query->dateBetween('create_time');
            $query->order('draft_version desc,id desc');
        });
    }

    /**
     * 添加论文项目
     * @auth true
     */
    public function add()
    {
        PaperProjectModel::mForm('paper_project/form');
    }

    /**
     * 编辑论文项目
     * @auth true
     */
    public function edit()
    {
        PaperProjectModel::mForm('paper_project/form');
    }

    /**
     * 查看论文项目详情
     * @auth true
     */
    public function view()
    {
        PaperProjectModel::mForm('paper_project/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 获取论文类型列表
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
            
            // 状态选项
            $this->statusOptions = [
                'draft' => '草稿',
                'outline_generating' => '大纲生成中',
                'writing' => '写作中',
                'completed' => '已完成',
                'failed' => '失败',
                'cancelled' => '已取消'
            ];

            // 草稿选项
            $this->draftOptions = [
                0 => '正式项目',
                1 => '草稿'
            ];
            
            // 写作风格选项
            $this->styleOptions = [
                'academic' => '学术风格',
                'formal' => '正式风格',
                'casual' => '通俗风格'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['status'] = 'draft';
                $data['progress'] = 0;
                $data['is_draft'] = $data['is_draft'] ?? 1; // 默认为草稿
                $data['draft_version'] = 1;
            }
            $data['update_time'] = date('Y-m-d H:i:s');

            // 计算当前字数
            if (!empty($data['content'])) {
                $data['current_word_count'] = mb_strlen(strip_tags($data['content']));
            }

            // 如果是草稿转正式，更新草稿状态
            if (isset($data['is_draft']) && $data['is_draft'] == 0 && !empty($data['id'])) {
                $data['draft_version'] = 1; // 重置版本号
            }
        }
    }

    /**
     * 修改项目状态
     * @auth true
     */
    public function state()
    {
        PaperProjectModel::mSave($this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除论文项目
     * @auth true
     */
    public function remove()
    {
        PaperProjectModel::mDelete();
    }

    /**
     * 生成大纲
     * @auth true
     */
    public function generateOutline()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('项目ID不能为空！');
        }
        
        $project = PaperProjectModel::mk()->findOrEmpty($id);
        if ($project->isEmpty()) {
            $this->error('项目不存在！');
        }
        
        // 这里可以调用n8n工作流来生成大纲
        // 暂时返回成功状态
        $project->save([
            'status' => 'outline',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('大纲生成任务已启动！');
    }

    /**
     * 开始写作
     * @auth true
     */
    public function startWriting()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('项目ID不能为空！');
        }
        
        $project = PaperProjectModel::mk()->findOrEmpty($id);
        if ($project->isEmpty()) {
            $this->error('项目不存在！');
        }
        
        if (empty($project['outline_content'])) {
            $this->error('请先生成大纲！');
        }
        
        // 这里可以调用n8n工作流来开始写作
        // 暂时返回成功状态
        $project->save([
            'status' => 'writing',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('写作任务已启动！');
    }

    /**
     * 保存草稿
     * @auth true
     */
    public function saveDraft()
    {
        $data = $this->request->post();
        if (empty($data['id'])) {
            $this->error('项目ID不能为空！');
        }

        $project = PaperProjectModel::mk()->findOrEmpty($data['id']);
        if ($project->isEmpty()) {
            $this->error('项目不存在！');
        }

        // 如果是草稿，增加版本号
        if ($project['is_draft'] == 1) {
            $data['draft_version'] = $project['draft_version'] + 1;
        }

        $data['update_time'] = date('Y-m-d H:i:s');

        // 计算当前字数
        if (!empty($data['content'])) {
            $data['current_word_count'] = mb_strlen(strip_tags($data['content']));
        }

        $project->save($data);
        $this->success('草稿保存成功！');
    }

    /**
     * 草稿转正式
     * @auth true
     */
    public function publishDraft()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('项目ID不能为空！');
        }

        $project = PaperProjectModel::mk()->findOrEmpty($id);
        if ($project->isEmpty()) {
            $this->error('项目不存在！');
        }

        if ($project['is_draft'] != 1) {
            $this->error('该项目不是草稿！');
        }

        $project->save([
            'is_draft' => 0,
            'draft_version' => 1,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        $this->success('草稿已发布为正式项目！');
    }

    /**
     * 复制为草稿
     * @auth true
     */
    public function copyToDraft()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('项目ID不能为空！');
        }

        $project = PaperProjectModel::mk()->findOrEmpty($id);
        if ($project->isEmpty()) {
            $this->error('项目不存在！');
        }

        $draftData = $project->toArray();
        unset($draftData['id']);
        $draftData['title'] = $draftData['title'] . ' - 草稿副本';
        $draftData['is_draft'] = 1;
        $draftData['draft_version'] = 1;
        $draftData['parent_id'] = $id;
        $draftData['status'] = 'draft';
        $draftData['create_time'] = date('Y-m-d H:i:s');
        $draftData['update_time'] = date('Y-m-d H:i:s');

        PaperProjectModel::mk()->save($draftData);
        $this->success('已复制为草稿！');
    }
}
