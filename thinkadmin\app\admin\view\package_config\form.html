{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-form"></span>
        {$title}
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{$vo.name|default=''}" placeholder="请输入名称" class="layui-input" required>
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">{$vo.description|default=''}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" value="{$vo.sort|default=100}" placeholder="请输入排序值" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset($vo.status) || $vo.status eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset($vo.status) && $vo.status eq 0}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(formSubmit)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info, 3, function(){
                    parent.layer.closeAll();
                    parent.location.reload();
                });
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}