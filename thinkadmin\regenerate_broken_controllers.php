<?php

/**
 * 重新生成被破坏的控制器文件
 */

echo "=== 重新生成被破坏的控制器文件 ===\n\n";

$controllers = [
    'PromptTemplate' => [
        'title' => '提示词模板管理',
        'model' => 'PromptTemplateModel',
        'table' => 'prompt_template'
    ],
    'OutlineTemplate' => [
        'title' => '大纲模板管理',
        'model' => 'OutlineTemplateModel', 
        'table' => 'outline_template'
    ],
    'RewriteRecord' => [
        'title' => '降重记录管理',
        'model' => 'RewriteRecordModel',
        'table' => 'rewrite_record'
    ],
    'CheckRecord' => [
        'title' => '查重记录管理',
        'model' => 'CheckRecordModel',
        'table' => 'check_record'
    ],
    'ExportTemplate' => [
        'title' => '导出模板管理',
        'model' => 'ExportTemplateModel',
        'table' => 'export_template'
    ],
    'VipPackage' => [
        'title' => 'VIP套餐管理',
        'model' => 'VipPackageModel',
        'table' => 'vip_package'
    ],
    'Order' => [
        'title' => '订单管理',
        'model' => 'OrderModel',
        'table' => 'order'
    ],
    'EmailConfig' => [
        'title' => '邮件配置管理',
        'model' => 'EmailConfigModel',
        'table' => 'email_config'
    ],
    'WebhookConfig' => [
        'title' => 'Webhook配置管理',
        'model' => 'WebhookConfigModel',
        'table' => 'webhook_config'
    ],
    'ContentFilter' => [
        'title' => '内容过滤管理',
        'model' => 'ContentFilterModel',
        'table' => 'content_filter'
    ],
    'RewriteModel' => [
        'title' => '降重模型管理',
        'model' => 'RewriteModelModel',
        'table' => 'rewrite_model'
    ]
];

$regeneratedCount = 0;

foreach ($controllers as $controllerName => $config) {
    echo "重新生成控制器: {$controllerName}\n";
    
    $filePath = "app/admin/controller/{$controllerName}.php";
    
    // 备份原文件
    if (file_exists($filePath)) {
        copy($filePath, $filePath . '.backup');
        echo "  ✅ 已备份原文件\n";
    }
    
    // 生成新的控制器内容
    $content = "<?php

namespace app\\admin\\controller;

use app\\admin\\model\\{$config['model']};
use think\\admin\\Controller;

/**
 * {$config['title']}
 * @class {$controllerName}
 * @package app\\admin\\controller
 */
class {$controllerName} extends Controller
{
    /**
     * {$config['title']}
     * @auth true
     * @menu true
     * @throws \\think\\db\\exception\\DataNotFoundException
     * @throws \\think\\db\\exception\\DbException
     * @throws \\think\\db\\exception\\ModelNotFoundException
     */
    public function index()
    {
        {$config['model']}::mQuery(\$this)->layPage(function () {
            \$this->title = '{$config['title']}';
        }, function (\$query) {
            \$query->like('name,title,description')->equal('status');
            \$query->dateBetween('create_time');
            \$query->order('id desc');
        });
    }

    /**
     * 添加{$config['title']}
     * @auth true
     */
    public function add()
    {
        {$config['model']}::mForm('{$config['table']}/form');
    }

    /**
     * 编辑{$config['title']}
     * @auth true
     */
    public function edit()
    {
        {$config['model']}::mForm('{$config['table']}/form');
    }

    /**
     * 删除{$config['title']}
     * @auth true
     */
    public function remove()
    {
        {$config['model']}::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        {$config['model']}::mSave(\$this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}";

    // 写入新文件
    file_put_contents($filePath, $content);
    
    // 检查语法
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ 重新生成成功\n";
        $regeneratedCount++;
    } else {
        echo "  ❌ 生成失败: " . implode(' ', $output) . "\n";
        // 恢复备份
        if (file_exists($filePath . '.backup')) {
            copy($filePath . '.backup', $filePath);
        }
    }
}

echo "\n=== 重新生成完成 ===\n";
echo "成功重新生成: {$regeneratedCount} 个控制器\n";

// 最终语法检查
echo "\n=== 最终语法检查 ===\n";
foreach (array_keys($controllers) as $controller) {
    $filePath = "app/admin/controller/{$controller}.php";
    
    if (!file_exists($filePath)) {
        continue;
    }
    
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ {$controller}.php - 语法正确\n";
    } else {
        echo "❌ {$controller}.php - 仍有语法错误\n";
    }
}

echo "\n=== 全部完成 ===\n";
