<label class="layui-form-item block relative">
    <span class="help-label"><b>商户绑定的公众号</b></span>
    <input class="layui-input" name="joinpay_appid" placeholder="请输入商户绑定的公众号（必填）" required value="{$vo.content.joinpay_appid|default=''}">
    <span class="help-block">商户绑定的公众号，授权给汇聚支付平台的公众号APPID</span>
</label>

<label class="layui-form-item block relative">
    <span class="help-label"><b>汇聚支付报备商户号</b></span>
    <input class="layui-input" maxlength="15" name="joinpay_trade" placeholder="请输入汇聚支付报备商户号（必填）" required value="{$vo.content.joinpay_trade|default=''}">
    <span class="help-block">汇聚支付报备商户号，需要联系汇聚支付平台的客服获取，通常以 777 开头的15位数字！</span>
</label>

<label class="layui-form-item block relative">
    <span class="help-label"><b>汇聚支付的商户编号</b></span>
    <input class="layui-input" maxlength="15" name="joinpay_mch_id" placeholder="请输入汇聚支付的商户编号（必填）" required value="{$vo.content.joinpay_mch_id|default=''}">
    <span class="help-block">汇聚支付的商户编号，需要在汇聚支付平台商户中心获取，通常是以 888 开头的15位数字！</span>
</label>

<label class="layui-form-item block relative">
    <span class="help-label"><b>汇聚支付的商户密钥</b></span>
    <input class="layui-input" maxlength="32" name="joinpay_mch_key" placeholder="请输入汇聚支付的商户密钥（必填）" required value="{$vo.content.joinpay_mch_key|default=''}">
    <span class="help-block">汇聚支付的商户密钥，需要在汇聚支付平台商户中心的密钥管理处获取，通常为32位字符串！</span>
</label>