<?php

namespace app\admin\controller;

use app\admin\model\ExportTemplateModel;
use think\admin\Controller;

/**
 * 导出模板管理
 * @class ExportTemplate
 * @package app\admin\controller
 */
class ExportTemplate extends Controller
{
    /**
     * 导出模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        ExportTemplateModel::mQuery($this)->layPage(function () {
            $this->title = '导出模板管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加导出模板管理
     * @auth true
     */
    public function add()
    {
        ExportTemplateModel::mForm('export_template/form');
    }

    /**
     * 编辑导出模板管理
     * @auth true
     */
    public function edit()
    {
        ExportTemplateModel::mForm('export_template/form');
    }

    /**
     * 删除导出模板管理
     * @auth true
     */
    public function remove()
    {
        ExportTemplateModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        ExportTemplateModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}