{extend name='table'}

{block name="button"}

{if isset($super) and $super}

<a data-table-id="QueueTable" class="layui-btn layui-btn-sm layui-btn-primary" data-queue="{:url('admin/api.plugs/optimize')}">{:lang('优化数据库')}</a>

{if isset($iswin) and ($iswin or php_sapi_name() eq 'cli')}
<button data-load='{:url("admin/api.queue/start")}' class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('开启服务')}</button>
<button data-load='{:url("admin/api.queue/stop")}' class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('关闭服务')}</button>
{/if}

{if auth("clean")}
<button data-table-id="QueueTable" data-queue='{:url("clean")}' class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('定时清理')}</button>
{/if}

{/if}

{if auth("remove")}
<button data-table-id="QueueTable" data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="{:lang('确定批量删除记录吗？')}" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('批量删除')}</button>
{/if}

{/block}

{block name="content"}
<div class="think-box-notify" type="info">

    <!--{if isset($super) and $super}-->
    <b>{:lang('服务状态')}：</b><b class="margin-right-5" data-queue-message><span class="color-desc">{:lang('检查中')}</span></b>
    <b data-tips-text="{:lang('点击可复制【服务启动指令】')}" class="layui-icon pointer margin-right-20" data-copy="{$command|default=''}">&#xe633;</b>
    <script>$('[data-queue-message]').load('{:sysuri("admin/api.queue/status")}');</script>
    <!--{/if}-->

    <b>{:lang('任务统计')}：</b>{:lang('待处理 %s 个任务，处理中 %s 个任务，已完成 %s 个任务，已失败 %s 个任务。', [
    '<b class="color-text" data-extra="pre">..</b>',
    '<b class="color-blue" data-extra="dos">..</b>',
    '<b class="color-green" data-extra="oks">..</b>',
    '<b class="color-red" data-extra="ers">..</b>'
    ])}

</div>

<div class="think-box-shadow">
    {include file='queue/index_search'}
    <table id="QueueTable" data-line="2" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        $('#QueueTable').layTable({
            even: true, height: 'full',
            sort: {field: 'loops_time desc,id', type: 'desc'},
            // 扩展数据处理，需要返回原 items 数据
            filter: function (items, result) {
                return result && result.extra && $('[data-extra]').map(function () {
                    this.innerHTML = result.extra[this.dataset.extra] || 0;
                }), items;
            },
            cols: [[
                {checkbox: true, fixed: 'left'},
                {
                    field: 'id', title: '{:lang("任务名称")}', width: '25%', sort: true, templet: function (d) {
                        if (d.loops_time > 0) {
                            d.one = '<span class="layui-badge think-bg-blue">循</span>';
                        } else {
                            d.one = '<span class="layui-badge think-bg-red">次</span>';
                        }
                        if (parseInt(d.rscript) === 1) {
                            d.two = '<span class="layui-badge layui-bg-green">复</span>';
                        } else {
                            d.two = '<span class="layui-badge think-bg-violet">单</span>';
                        }
                        return laytpl('{{-d.one}}任务编号：<b>{{d.code}}</b><br>{{-d.two}}任务名称：{{d.title}}').render(d);
                    }
                },
                {
                    field: 'exec_time', title: '{:lang("任务计划")}', width: '25%', templet: function (d) {
                        d.html = '执行指令：' + d.command + '<br>计划执行：' + d.exec_time;
                        if (d.loops_time > 0) {
                            return d.html + ' ( 每 <b class="color-blue">' + d.loops_time + '</b> 秒 ) ';
                        } else {
                            return d.html + ' <span class="color-desc">( 单次任务 )</span> ';
                        }
                    }
                },
                {
                    field: 'loops_time', title: '{:lang("任务状态")}', width: '30%', templet: function (d) {
                        d.html = ([
                            '<span class="pull-left layui-badge layui-badge-middle layui-bg-gray">未知</span>',
                            '<span class="pull-left layui-badge layui-badge-middle layui-bg-black">等待</span>',
                            '<span class="pull-left layui-badge layui-badge-middle layui-bg-blue">执行</span>',
                            '<span class="pull-left layui-badge layui-badge-middle layui-bg-green">完成</span>',
                            '<span class="pull-left layui-badge layui-badge-middle layui-bg-red">失败</span>',
                        ][d.status] || '') + '执行时间：';
                        d.enter_time = d.enter_time || '';
                        d.outer_time = d.outer_time || '0.0000';
                        if (d.enter_time.length > 12) {
                            d.html += d.enter_time.substring(12) + '<span class="color-desc"> ( ' + d.outer_time + ' ) </span>';
                            d.html += ' 已执行 <b class="color-blue">' + (d.attempts || 0) + '</b> 次';
                        } else {
                            d.html += '<span class="color-desc">任务未执行</span>'
                        }
                        return d.html + '<br>执行结果：<span class="color-blue">' + (d.exec_desc || '<span class="color-desc">未获取到执行结果</span>') + '</span>';
                    }
                },
                {toolbar: '#toolbar', title: '{:lang("操作面板")}', align: 'center', minWidth: 210, fixed: 'right'}
            ]]
        });
    });
</script>

<script type="text/html" id="toolbar">

    <!--{if auth('redo')}-->
    {{# if(d.status===4||d.status===3){ }}
    <a class="layui-btn layui-btn-sm" data-confirm="确定要重置该任务吗？" data-queue="{:url('redo')}?code={{d.code}}">{:lang('重 置')}</a>
    {{# }else{ }}
    <a class="layui-btn layui-btn-sm layui-btn-disabled">{:lang('重 置')}</a>
    {{# } }}
    <!--{/if}-->

    <!--{if auth('remove')}-->
    <a class='layui-btn layui-btn-sm layui-btn-danger' data-confirm="{:lang('确定要删除该记录吗？')}" data-action='{:url("remove")}' data-value="id#{{d.id}}">{:lang('删 除')}</a>
    <!--{/if}-->

    <a class='layui-btn layui-btn-sm layui-btn-normal' onclick="$.loadQueue('{{d.code}}',false,this)">{:lang('日 志')}</a>
</script>
{/block}
