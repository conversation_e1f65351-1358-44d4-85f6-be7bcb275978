<?php

/**
 * 更新菜单结构脚本
 * 根据模型分析结果，更新菜单的链接、权限节点和图标信息
 */

$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 开始更新菜单结构 ===\n\n";
    
    // 定义完整的菜单结构更新信息
    $menuUpdates = [
        // 写作中心
        '写作中心' => [
            'icon' => 'layui-icon layui-icon-edit',
            'sort' => 100,
            'children' => [
                '论文类型管理' => [
                    'node' => 'admin/paper_type/index',
                    'url' => 'admin/paper_type/index',
                    'icon' => 'layui-icon layui-icon-template-1',
                    'sort' => 1
                ],
                '大纲模板管理' => [
                    'node' => 'admin/outline_template/index',
                    'url' => 'admin/outline_template/index',
                    'icon' => 'layui-icon layui-icon-list',
                    'sort' => 2
                ],
                '提示词模板管理' => [
                    'node' => 'admin/prompt_template/index',
                    'url' => 'admin/prompt_template/index',
                    'icon' => 'layui-icon layui-icon-dialogue',
                    'sort' => 3
                ],
                '正文模板管理' => [
                    'node' => 'admin/content_template/index',
                    'url' => 'admin/content_template/index',
                    'icon' => 'layui-icon layui-icon-file-b',
                    'sort' => 4
                ],
                '写作任务管理' => [
                    'node' => 'admin/paper_project/index',
                    'url' => 'admin/paper_project/index',
                    'icon' => 'layui-icon layui-icon-file',
                    'sort' => 5
                ],
                '草稿箱管理' => [
                    'node' => 'admin/paper_project/draft',
                    'url' => 'admin/paper_project/draft',
                    'icon' => 'layui-icon layui-icon-survey',
                    'sort' => 6
                ]
            ]
        ],
        
        // 降重与查重
        '降重与查重' => [
            'icon' => 'layui-icon layui-icon-refresh-3',
            'sort' => 200,
            'children' => [
                '降重记录管理' => [
                    'node' => 'admin/rewrite_record/index',
                    'url' => 'admin/rewrite_record/index',
                    'icon' => 'layui-icon layui-icon-log',
                    'sort' => 1
                ],
                '降重模型配置' => [
                    'node' => 'admin/rewrite_model/index',
                    'url' => 'admin/rewrite_model/index',
                    'icon' => 'layui-icon layui-icon-engine',
                    'sort' => 2
                ],
                '查重记录管理' => [
                    'node' => 'admin/check_record/index',
                    'url' => 'admin/check_record/index',
                    'icon' => 'layui-icon layui-icon-search',
                    'sort' => 3
                ],
                '降重任务管理' => [
                    'node' => 'admin/rewrite_task/index',
                    'url' => 'admin/rewrite_task/index',
                    'icon' => 'layui-icon layui-icon-component',
                    'sort' => 4
                ]
            ]
        ],
        
        // 文档导出
        '文档导出' => [
            'icon' => 'layui-icon layui-icon-export',
            'sort' => 300,
            'children' => [
                '导出样式模板' => [
                    'node' => 'admin/export_template/index',
                    'url' => 'admin/export_template/index',
                    'icon' => 'layui-icon layui-icon-template',
                    'sort' => 1
                ],
                '导出任务监控' => [
                    'node' => 'admin/export_monitor/index',
                    'url' => 'admin/export_monitor/index',
                    'icon' => 'layui-icon layui-icon-chart',
                    'sort' => 2
                ],
                '发票管理' => [
                    'node' => 'admin/invoice/index',
                    'url' => 'admin/invoice/index',
                    'icon' => 'layui-icon layui-icon-file-b',
                    'sort' => 3
                ]
            ]
        ],
        
        // 用户中心
        '用户中心' => [
            'icon' => 'layui-icon layui-icon-user',
            'sort' => 400,
            'children' => [
                '用户列表' => [
                    'node' => 'admin/user/index',
                    'url' => 'admin/user/index',
                    'icon' => 'layui-icon layui-icon-username',
                    'sort' => 1
                ],
                'VIP套餐管理' => [
                    'node' => 'admin/vip_package/index',
                    'url' => 'admin/vip_package/index',
                    'icon' => 'layui-icon layui-icon-diamond',
                    'sort' => 2
                ],
                '用户积分管理' => [
                    'node' => 'admin/user_points/index',
                    'url' => 'admin/user_points/index',
                    'icon' => 'layui-icon layui-icon-star-fill',
                    'sort' => 3
                ]
            ]
        ],
        
        // 收费系统
        '收费系统' => [
            'icon' => 'layui-icon layui-icon-rmb',
            'sort' => 500,
            'children' => [
                '订单管理' => [
                    'node' => 'admin/order/index',
                    'url' => 'admin/order/index',
                    'icon' => 'layui-icon layui-icon-cart-simple',
                    'sort' => 1
                ],
                '套餐配置' => [
                    'node' => 'admin/package_config/index',
                    'url' => 'admin/package_config/index',
                    'icon' => 'layui-icon layui-icon-set',
                    'sort' => 2
                ],
                '充值记录' => [
                    'node' => 'admin/recharge_record/index',
                    'url' => 'admin/recharge_record/index',
                    'icon' => 'layui-icon layui-icon-dollar',
                    'sort' => 3
                ],
                '发票管理' => [
                    'node' => 'admin/invoice/index',
                    'url' => 'admin/invoice/index',
                    'icon' => 'layui-icon layui-icon-file-b',
                    'sort' => 4
                ]
            ]
        ],
        
        // 通知与消息
        '通知与消息' => [
            'icon' => 'layui-icon layui-icon-notice',
            'sort' => 600,
            'children' => [
                '系统通知记录' => [
                    'node' => 'admin/system_notice/index',
                    'url' => 'admin/system_notice/index',
                    'icon' => 'layui-icon layui-icon-speaker',
                    'sort' => 1
                ],
                '消息模板管理' => [
                    'node' => 'admin/message_template/index',
                    'url' => 'admin/message_template/index',
                    'icon' => 'layui-icon layui-icon-template-1',
                    'sort' => 2
                ],
                '邮件配置' => [
                    'node' => 'admin/email_config/index',
                    'url' => 'admin/email_config/index',
                    'icon' => 'layui-icon layui-icon-email',
                    'sort' => 3
                ],
                '通知记录' => [
                    'node' => 'admin/notification_log/index',
                    'url' => 'admin/notification_log/index',
                    'icon' => 'layui-icon layui-icon-log',
                    'sort' => 4
                ]
            ]
        ],
        
        // 系统设置
        '系统设置' => [
            'icon' => 'layui-icon layui-icon-set',
            'sort' => 700,
            'children' => [
                'AI模型配置' => [
                    'node' => 'admin/ai_model/index',
                    'url' => 'admin/ai_model/index',
                    'icon' => 'layui-icon layui-icon-engine',
                    'sort' => 1
                ],
                'n8n工作流管理' => [
                    'node' => 'admin/n8n_workflow/index',
                    'url' => 'admin/n8n_workflow/index',
                    'icon' => 'layui-icon layui-icon-component',
                    'sort' => 2
                ],
                '接口密钥管理' => [
                    'node' => 'admin/api_key/index',
                    'url' => 'admin/api_key/index',
                    'icon' => 'layui-icon layui-icon-key',
                    'sort' => 3
                ],
                'Webhook配置' => [
                    'node' => 'admin/webhook_config/index',
                    'url' => 'admin/webhook_config/index',
                    'icon' => 'layui-icon layui-icon-link',
                    'sort' => 4
                ],
                '内容风控规则' => [
                    'node' => 'admin/content_filter/index',
                    'url' => 'admin/content_filter/index',
                    'icon' => 'layui-icon layui-icon-vercode',
                    'sort' => 5
                ],
                '基础参数设置' => [
                    'node' => 'admin/basic_config/index',
                    'url' => 'admin/basic_config/index',
                    'icon' => 'layui-icon layui-icon-slider',
                    'sort' => 6
                ]
            ]
        ]
    ];
    
    $updateCount = 0;
    $errorCount = 0;
    
    // 开始更新菜单
    foreach ($menuUpdates as $sectionTitle => $sectionInfo) {
        echo "处理菜单组: {$sectionTitle}\n";
        
        // 查找父菜单
        $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = 0");
        $stmt->execute([$sectionTitle]);
        $parentMenu = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$parentMenu) {
            echo "  ❌ 未找到父菜单: {$sectionTitle}\n";
            $errorCount++;
            continue;
        }
        
        $parentId = $parentMenu['id'];
        
        // 更新父菜单信息
        $stmt = $pdo->prepare("UPDATE system_menu SET icon = ?, sort = ? WHERE id = ?");
        if ($stmt->execute([$sectionInfo['icon'], $sectionInfo['sort'], $parentId])) {
            echo "  ✅ 更新父菜单: {$sectionTitle}\n";
            $updateCount++;
        } else {
            echo "  ❌ 更新父菜单失败: {$sectionTitle}\n";
            $errorCount++;
        }
        
        // 更新子菜单
        foreach ($sectionInfo['children'] as $childTitle => $childInfo) {
            $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = ?");
            $stmt->execute([$childTitle, $parentId]);
            $childMenu = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($childMenu) {
                $stmt = $pdo->prepare("
                    UPDATE system_menu 
                    SET node = ?, url = ?, icon = ?, sort = ?, status = 1 
                    WHERE id = ?
                ");
                
                if ($stmt->execute([
                    $childInfo['node'],
                    $childInfo['url'],
                    $childInfo['icon'],
                    $childInfo['sort'],
                    $childMenu['id']
                ])) {
                    echo "    ✅ 更新子菜单: {$childTitle}\n";
                    $updateCount++;
                } else {
                    echo "    ❌ 更新子菜单失败: {$childTitle}\n";
                    $errorCount++;
                }
            } else {
                echo "    ⚠️  未找到子菜单: {$childTitle}\n";
            }
        }
        echo "\n";
    }
    
    // 处理重复的发票管理菜单（移除收费系统中的重复项）
    echo "处理重复菜单项...\n";
    
    // 查找收费系统中的发票管理
    $stmt = $pdo->prepare("
        SELECT sm.id 
        FROM system_menu sm 
        JOIN system_menu parent ON sm.pid = parent.id 
        WHERE sm.title = '发票管理' AND parent.title = '收费系统'
    ");
    $stmt->execute();
    $duplicateInvoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($duplicateInvoice) {
        $stmt = $pdo->prepare("DELETE FROM system_menu WHERE id = ?");
        if ($stmt->execute([$duplicateInvoice['id']])) {
            echo "  ✅ 删除收费系统中的重复发票管理菜单\n";
        } else {
            echo "  ❌ 删除重复菜单失败\n";
            $errorCount++;
        }
    }
    
    // 清理无效的菜单项
    echo "\n清理无效菜单项...\n";
    
    $invalidMenus = [
        '写作系统相关设置' // 这个应该合并到系统设置中
    ];
    
    foreach ($invalidMenus as $invalidTitle) {
        // 先将子菜单移动到系统设置下
        $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = 0");
        $stmt->execute([$invalidTitle]);
        $invalidParent = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invalidParent) {
            // 找到系统设置菜单ID
            $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = '系统设置' AND pid = 0");
            $stmt->execute();
            $systemSettingsMenu = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($systemSettingsMenu) {
                // 移动子菜单
                $stmt = $pdo->prepare("UPDATE system_menu SET pid = ? WHERE pid = ?");
                $stmt->execute([$systemSettingsMenu['id'], $invalidParent['id']]);
                
                // 删除无效的父菜单
                $stmt = $pdo->prepare("DELETE FROM system_menu WHERE id = ?");
                if ($stmt->execute([$invalidParent['id']])) {
                    echo "  ✅ 删除无效菜单: {$invalidTitle}\n";
                } else {
                    echo "  ❌ 删除无效菜单失败: {$invalidTitle}\n";
                    $errorCount++;
                }
            }
        }
    }
    
    echo "\n=== 菜单更新完成 ===\n";
    echo "成功更新: {$updateCount} 项\n";
    echo "失败: {$errorCount} 项\n";
    
    // 显示最终的菜单结构
    echo "\n=== 最终菜单结构 ===\n";
    
    $stmt = $pdo->query("
        SELECT id, pid, title, node, icon, sort, status 
        FROM system_menu 
        WHERE pid = 0 AND status = 1 
        ORDER BY sort, id
    ");
    $parentMenus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($parentMenus as $parent) {
        echo "\n{$parent['title']} (ID: {$parent['id']}, Sort: {$parent['sort']})\n";
        echo "  图标: {$parent['icon']}\n";
        
        $stmt = $pdo->prepare("
            SELECT title, node, icon, sort, status 
            FROM system_menu 
            WHERE pid = ? 
            ORDER BY sort, id
        ");
        $stmt->execute([$parent['id']]);
        $children = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($children as $child) {
            $statusIcon = $child['status'] ? '✅' : '❌';
            echo "  {$statusIcon} {$child['title']} -> {$child['node']} (Sort: {$child['sort']})\n";
            echo "    图标: {$child['icon']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
