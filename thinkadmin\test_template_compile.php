<?php

/**
 * 测试模板编译
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\View;

try {
    echo "=== 测试模板编译 ===\n\n";
    
    // 初始化应用
    $app = new App();
    $app->initialize();
    
    // 测试编译关键模板文件
    $testTemplates = [
        'content_template/index',
        'content_template/form',
        'prompt_template/index',
        'prompt_template/form'
    ];
    
    foreach ($testTemplates as $template) {
        try {
            echo "测试模板: {$template}\n";
            
            // 设置测试数据
            View::assign([
                'get' => ['paper_type_id' => '1', 'status' => '1'],
                'vo' => ['id' => 1],
                'paperTypes' => [1 => '学术论文', 2 => '毕业论文'],
                'title' => '测试标题'
            ]);
            
            // 尝试编译模板
            $content = View::fetch($template);
            echo "✅ 编译成功\n";
            
        } catch (Exception $e) {
            echo "❌ 编译失败: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 框架初始化失败: " . $e->getMessage() . "\n";
    echo "这可能是正常的，因为需要完整的Web环境\n";
}

echo "=== 测试完成 ===\n";
