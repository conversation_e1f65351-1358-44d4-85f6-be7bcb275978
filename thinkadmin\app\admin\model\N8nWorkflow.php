<?php

// +----------------------------------------------------------------------
// | N8N Workflow Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * n8n工作流模型
 * @class N8nWorkflow
 * @package app\admin\model
 */
class N8nWorkflow extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'n8n_workflow';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['config'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'is_active' => 'integer',
        'execution_count' => 'integer',
        'success_count' => 'integer',
        'config' => 'array'
    ];

    /**
     * 获取器 - 配置参数
     * @param $value
     * @return array
     */
    public function getConfigAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 配置参数
     * @param $value
     * @return string
     */
    public function setConfigAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 关联执行记录
     * @return \think\model\relation\HasMany
     */
    public function executions()
    {
        return $this->hasMany(N8nExecution::class, 'workflow_id', 'id');
    }

    /**
     * 获取激活状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsActiveTextAttr($value, $data)
    {
        return $data['is_active'] ? '已激活' : '未激活';
    }

    /**
     * 获取类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            'paper_outline' => '论文大纲生成',
            'paper_writing' => '论文写作',
            'content_rewrite' => '内容降重',
            'plagiarism_check' => '查重检测',
            'document_export' => '文档导出',
            'email_notification' => '邮件通知',
            'data_sync' => '数据同步',
            'file_process' => '文件处理',
            'custom' => '自定义流程'
        ];
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 获取成功率
     * @param $value
     * @param $data
     * @return string
     */
    public function getSuccessRateAttr($value, $data)
    {
        if ($data['execution_count'] == 0) {
            return '0%';
        }
        $rate = round($data['success_count'] / $data['execution_count'] * 100, 2);
        return $rate . '%';
    }

    /**
     * 搜索器 - 工作流名称
     * @param $query
     * @param $value
     */
    public function searchNameAttr($query, $value)
    {
        $query->whereLike('name', "%{$value}%");
    }

    /**
     * 搜索器 - 工作流ID
     * @param $query
     * @param $value
     */
    public function searchWorkflowIdAttr($query, $value)
    {
        $query->whereLike('workflow_id', "%{$value}%");
    }

    /**
     * 搜索器 - 类型
     * @param $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        $query->where('type', $value);
    }

    /**
     * 搜索器 - 激活状态
     * @param $query
     * @param $value
     */
    public function searchIsActiveAttr($query, $value)
    {
        $query->where('is_active', $value);
    }

    /**
     * 根据类型获取激活的工作流
     * @param string $type 工作流类型
     * @return array|null
     */
    public static function getActiveByType(string $type)
    {
        $workflow = self::mk()->where(['type' => $type, 'is_active' => 1])->findOrEmpty();
        return $workflow->isEmpty() ? null : $workflow->toArray();
    }

    /**
     * 执行工作流
     * @param int $id 工作流ID
     * @param array $data 执行数据
     * @return array|false
     */
    public static function execute(int $id, array $data = [])
    {
        $workflow = self::mk()->findOrEmpty($id);
        if ($workflow->isEmpty() || !$workflow['is_active']) {
            return false;
        }
        
        // 这里可以调用n8n API执行工作流
        // 暂时返回模拟结果
        $executionId = 'exec_' . time() . '_' . mt_rand(1000, 9999);
        
        // 更新执行统计
        $workflow->inc('execution_count')->update();
        
        return [
            'execution_id' => $executionId,
            'workflow_id' => $workflow['workflow_id'],
            'status' => 'running',
            'data' => $data
        ];
    }

    /**
     * 更新执行结果
     * @param int $id 工作流ID
     * @param bool $success 是否成功
     * @return bool
     */
    public static function updateExecutionResult(int $id, bool $success): bool
    {
        $workflow = self::mk()->findOrEmpty($id);
        if ($workflow->isEmpty()) {
            return false;
        }
        
        $updateData = ['last_execution_time' => date('Y-m-d H:i:s')];
        if ($success) {
            $updateData['success_count'] = $workflow['success_count'] + 1;
        }
        
        return $workflow->save($updateData) !== false;
    }
}
