{extend name='table'}

{block name="button"}
<!--{if auth("types")}-->
<button data-modal="{:url('types')}" data-width="500px" class='layui-btn layui-btn-sm layui-btn-primary'>支付配置</button>
<!--{/if}-->

<!--{if auth("add")}-->
<button class='layui-btn layui-btn-sm layui-btn-primary' data-open='{:url("add")}'>添加支付</button>
<!--{/if}-->

<!--{if auth("remove")}-->
<button class='layui-btn layui-btn-sm layui-btn-primary' data-table-id="PaymentTable" data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要删除选中的记录吗？">批量删除</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="layui-tab layui-tab-card">
    <ul class="layui-tab-title">
        {foreach ['index'=>'支付管理','recycle'=>'回 收 站'] as $k=>$v}{if isset($type) and $type eq $k}
        <li data-open="{:url('index')}?type={$k}" class="layui-this">{$v}</li>
        {else}
        <li data-open="{:url('index')}?type={$k}">{$v}</li>
        {/if}{/foreach}
    </ul>
    <div class="layui-tab-content">
        {include file='config/index_search'}
        <table id="PaymentTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
    </div>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 初始化表格组件
        let $table = $('#PaymentTable').layTable({
            even: true, height: 'full',
            sort: {field: 'sort desc,code', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'sort', title: '排序权重', align: 'center', width: 100, sort: true, templet: '#SortInputTpl'},
                {field: 'cover', title: '图标', width: 60, align: 'center', templet: "<div>{{-showTableImage(d.cover||'')}}</div>"},
                {field: 'code', title: '支付编号', align: "center", minWidth: 145, width: '10%'},
                {field: 'ntype', title: '支付类型', align: "center", minWidth: 140, width: '10%'},
                {field: 'name', title: '支付名称', align: 'left', minWidth: 140, width: '10%'},
                {
                    field: 'atype', title: '终端授权', align: 'left', minWidth: 140, templet: function (d) {
                        let strs = [];
                        layui.each(d.atype || {}, function (k, v) {
                            strs.push(laytpl('<span class="layui-badge think-bg-violet">{{d.v}}</span>').render({v: v}));
                        });
                        return strs.join('');
                    }
                },
                {field: 'status', title: '支付状态', align: 'center', minWidth: 110, width: '5%', templet: '#StatusSwitchTpl'},
                {field: 'create_time', title: '创建时间', align: 'center', minWidth: 170, width: '12%', sort: true},
                {toolbar: '#toolbar', title: '操作面板', align: 'center', minWidth: 80, width: '8%', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            let data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                let fn = () => $table.trigger('reload');
                ret.code > 0 ? fn() : $.msg.error(ret.info, 3, fn)
                return false;
            }, false);
        });
    });

</script>

<!-- 列表排序权重模板 -->
<script type="text/html" id="SortInputTpl">
    <input type="number" min="0" data-blur-number="0" data-action-blur="{:request()->url()}" data-value="id#{{d.id}};action#sort;sort#{value}" data-loading="false" value="{{d.sort}}" class="layui-input text-center">
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchTpl">
    <!--{if auth("state")}-->
    <input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="已激活|已禁用" lay-filter="StatusSwitch" {{-d.status>0?'checked':''}}>
    <!--{else}-->
    {{-d.status ? '<b class="color-green">已启用</b>' : '<b class="color-red">已禁用</b>'}}
    <!--{/if}-->
</script>

<!-- 数据操作工具条模板 -->
<script type="text/html" id="toolbar">
    <!--{if auth("edit") and isset($type) and $type eq 'index'}-->
    <a class="layui-btn layui-btn-primary layui-btn-sm" data-open='{:url("edit")}?id={{d.id}}'>编 辑</a>
    <!--{/if}-->

    <!--{if auth("remove") and isset($type) and $type neq 'index'}-->
    <a class="layui-btn layui-btn-danger layui-btn-sm" data-action="{:url('remove')}" data-value="id#{{d.id}}" data-confirm="确定要删除文章吗?">删 除</a>
    <!--{/if}-->
</script>
{/block}