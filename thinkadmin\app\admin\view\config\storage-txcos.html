<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card">
    <div class="layui-card-body padding-top-20">

        <div class="color-text layui-code text-center layui-bg-gray" style="border-left-width:1px;margin:0 0 15px 40px">
            <p class="margin-bottom-5 font-w7">文件将上传到 <a target="_blank" href="https://curl.qcloud.com/4t0Mbw2K">腾讯云</a> COS 存储，需要配置 COS 公有读私有写访问权限及跨域策略</p>
            <p>配置跨域访问 CORS 规则，设置来源 Origin 为 *，允许 Methods 为 POST，允许 Headers 为 *</p>
        </div>

        {include file='config/storage-0'}

        <div class="layui-form-item">
            <label class="layui-form-label label-required">
                <b class="color-green">访问协议</b><br><span class="nowrap color-desc">Protocol</span>
            </label>
            <div class="layui-input-block">
                {if !sysconf('storage.txcos_http_protocol')}{php}sysconf('storage.txcos_http_protocol','http');{/php}{/if}
                {foreach ['http'=>'HTTP','https'=>'HTTPS','auto'=>"AUTO"] as $protocol=>$remark}
                <label class="think-radio">
                    {if sysconf('storage.txcos_http_protocol') eq $protocol}
                    <input checked type="radio" name="storage.txcos_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                    {else}
                    <input type="radio" name="storage.txcos_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                    {/if}
                </label>
                {/foreach}
                <p class="help-block">腾讯云COS存储访问协议，其中 HTTPS 需要配置证书才能使用（ AUTO 为相对协议 ）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">
                <b class="color-green">存储区域</b><br><span class="nowrap color-desc label-required">Region</span>
            </label>
            <div class="layui-input-block">
                <select class="layui-select" name="storage.txcos_point" lay-search>
                    {foreach $points as $point => $title}
                    {if sysconf('storage.txcos_point') eq $point}
                    <option selected value="{$point}">{$title}（ {$point} ）</option>
                    {else}
                    <option value="{$point}">{$title}（ {$point} ）</option>
                    {/if}{/foreach}
                </select>
                <p class="help-block">腾讯云COS存储空间所在区域，需要严格对应储存所在区域才能上传文件</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.txcos_bucket">
                <b class="color-green">空间名称</b><br><span class="nowrap color-desc">Bucket</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.txcos_bucket" type="text" name="storage.txcos_bucket" value="{:sysconf('storage.txcos_bucket')}" required vali-name="空间名称" placeholder="请输入腾讯云COS存储 Bucket" class="layui-input">
                <p class="help-block">填写腾讯云COS存储空间名称，如：thinkadmin-1251143395</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.txcos_http_domain">
                <b class="color-green">访问域名</b><br><span class="nowrap color-desc">Domain</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.txcos_http_domain" type="text" name="storage.txcos_http_domain" value="{:sysconf('storage.txcos_http_domain')}" required vali-name="访问域名" placeholder="请输入腾讯云COS存储 Domain" class="layui-input">
                <p class="help-block">填写腾讯云COS存储外部访问域名，不需要填写访问协议，如：static.txcos.thinkadmin.top</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.txcos_access_key">
                <b class="color-green">访问密钥</b><br><span class="nowrap color-desc">AccessKey</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.txcos_access_key" type="text" name="storage.txcos_access_key" value="{:sysconf('storage.txcos_access_key')}" required vali-name="访问密钥" placeholder="请输入腾讯云COS存储 AccessKey" class="layui-input">
                <p class="help-block">可以在 [ 腾讯云 > 个人中心 ] 设置并获取到访问密钥</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.txcos_secret_key">
                <b class="color-green">安全密钥</b><br><span class="nowrap color-desc">SecretKey</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.txcos_secret_key" type="text" name="storage.txcos_secret_key" value="{:sysconf('storage.txcos_secret_key')}" maxlength="43" required vali-name="安全密钥" placeholder="请输入腾讯云COS存储 SecretKey" class="layui-input">
                <p class="help-block">可以在 [ 腾讯云 > 个人中心 ] 设置并获取到安全密钥</p>
            </div>
        </div>

        <div class="hr-line-dashed margin-left-40"></div>
        <input type="hidden" name="storage.type" value="txcos">

        <div class="layui-form-item text-center padding-left-40">
            <button class="layui-btn" type="submit">保存配置</button>
            <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消修改吗？" data-close>取消修改</button>
        </div>

    </div>
</form>