<?php

declare (strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemConfig;

/**
 * 基础参数设置
 * @class BasicConfig
 * @package app\admin\controller
 */
class BasicConfig extends Controller
{
    /**
     * 基础参数设置
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        SystemConfig::mQuery()->layTable(function () {
            $this->title = '基础参数设置';
            $this->typeOptions = $this->getConfigTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
        }, static function (QueryHelper $query) {
            // 只显示基础配置相关的参数
            $query->where('type', 'like', 'basic_%');
            $query->like('name,title,description')->equal('type,status');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加基础配置
     * @auth true
     */
    public function add()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 编辑基础配置
     * @auth true
     */
    public function edit()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = $this->getConfigTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            $this->dataTypeOptions = $this->getDataTypes();
            
            // 如果是编辑，解析配置值
            if (!empty($vo['id']) && !empty($vo['value'])) {
                $config = json_decode($vo['value'], true);
                if (is_array($config)) {
                    $vo = array_merge($vo, $config);
                } else {
                    // 如果不是JSON格式，直接作为字符串值
                    $vo['config_value'] = $vo['value'];
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('配置名称不能为空！');
            }
            
            if (empty($vo['title'])) {
                $this->error('配置标题不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择配置类型！');
            }
            
            // 确保type以basic_开头
            if (strpos($vo['type'], 'basic_') !== 0) {
                $vo['type'] = 'basic_' . $vo['type'];
            }
            
            // 构建配置值
            $configValue = [];
            $dataType = $vo['data_type'] ?? 'string';
            
            // 根据数据类型处理配置值
            switch ($dataType) {
                case 'string':
                    $configValue['value'] = strval($vo['config_value'] ?? '');
                    break;
                    
                case 'integer':
                    $configValue['value'] = intval($vo['config_value'] ?? 0);
                    break;
                    
                case 'float':
                    $configValue['value'] = floatval($vo['config_value'] ?? 0.0);
                    break;
                    
                case 'boolean':
                    $configValue['value'] = boolval($vo['config_value'] ?? false);
                    break;
                    
                case 'array':
                    if (!empty($vo['config_value'])) {
                        if (is_string($vo['config_value'])) {
                            // 尝试解析JSON或按行分割
                            $decoded = json_decode($vo['config_value'], true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $configValue['value'] = $decoded;
                            } else {
                                $configValue['value'] = array_filter(explode("\n", $vo['config_value']));
                            }
                        } else {
                            $configValue['value'] = $vo['config_value'];
                        }
                    } else {
                        $configValue['value'] = [];
                    }
                    break;
                    
                case 'json':
                    if (!empty($vo['config_value'])) {
                        $decoded = json_decode($vo['config_value'], true);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            $this->error('JSON格式错误！');
                        }
                        $configValue['value'] = $decoded;
                    } else {
                        $configValue['value'] = [];
                    }
                    break;
                    
                default:
                    $configValue['value'] = $vo['config_value'] ?? '';
                    break;
            }
            
            // 添加元数据
            $configValue['data_type'] = $dataType;
            $configValue['validation'] = $vo['validation'] ?? '';
            $configValue['default_value'] = $vo['default_value'] ?? '';
            $configValue['options'] = $vo['options'] ?? '';
            $configValue['unit'] = $vo['unit'] ?? '';
            $configValue['min_value'] = $vo['min_value'] ?? '';
            $configValue['max_value'] = $vo['max_value'] ?? '';
            
            // 验证配置值
            if ($dataType === 'integer' || $dataType === 'float') {
                $value = $configValue['value'];
                if (!empty($configValue['min_value']) && $value < $configValue['min_value']) {
                    $this->error('配置值不能小于最小值：' . $configValue['min_value']);
                }
                if (!empty($configValue['max_value']) && $value > $configValue['max_value']) {
                    $this->error('配置值不能大于最大值：' . $configValue['max_value']);
                }
            }
            
            $vo['value'] = json_encode($configValue, JSON_UNESCAPED_UNICODE);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('基础配置保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除基础配置
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if ($config->delete()) {
            $this->success('基础配置删除成功！');
        } else {
            $this->error('基础配置删除失败！');
        }
    }

    /**
     * 修改配置状态
     * @auth true
     */
    public function state()
    {
        SystemConfig::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 获取配置类型选项
     * @return array
     */
    private function getConfigTypes(): array
    {
        return [
            'basic_system' => '系统基础配置',
            'basic_upload' => '文件上传配置',
            'basic_security' => '安全配置',
            'basic_performance' => '性能配置',
            'basic_ui' => '界面配置',
            'basic_feature' => '功能配置',
            'basic_limit' => '限制配置',
            'basic_cache' => '缓存配置'
        ];
    }

    /**
     * 获取数据类型选项
     * @return array
     */
    private function getDataTypes(): array
    {
        return [
            'string' => '字符串',
            'integer' => '整数',
            'float' => '浮点数',
            'boolean' => '布尔值',
            'array' => '数组',
            'json' => 'JSON对象'
        ];
    }

    /**
     * 系统配置概览
     * @auth true
     */
    public function overview()
    {
        // 获取各类型配置统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->field('type, COUNT(*) as count, SUM(status) as active_count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getConfigTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
            $stat['inactive_count'] = $stat['count'] - $stat['active_count'];
        }
        
        // 获取重要配置项
        $importantConfigs = [
            'basic_system' => [
                'site_name' => '站点名称',
                'site_url' => '站点URL',
                'admin_email' => '管理员邮箱',
                'timezone' => '时区设置'
            ],
            'basic_security' => [
                'login_attempts' => '登录尝试次数',
                'session_timeout' => '会话超时时间',
                'password_policy' => '密码策略',
                'ip_whitelist' => 'IP白名单'
            ],
            'basic_upload' => [
                'max_file_size' => '最大文件大小',
                'allowed_extensions' => '允许的文件扩展名',
                'upload_path' => '上传路径',
                'image_quality' => '图片质量'
            ]
        ];
        
        // 模拟配置值
        $configValues = [];
        foreach ($importantConfigs as $type => $configs) {
            foreach ($configs as $key => $name) {
                $configValues[$key] = [
                    'name' => $name,
                    'value' => $this->getMockConfigValue($key),
                    'type' => $type
                ];
            }
        }
        
        // 系统状态
        $systemStatus = [
            'php_version' => PHP_VERSION,
            'memory_usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
            'disk_usage' => rand(20, 80) . '%',
            'cache_status' => rand(0, 1) ? '正常' : '异常',
            'database_status' => '正常',
            'queue_status' => rand(0, 1) ? '正常' : '异常'
        ];

        $this->assign('typeStats', $typeStats);
        $this->assign('configValues', $configValues);
        $this->assign('systemStatus', $systemStatus);
        $this->assign('title', '系统配置概览');
        
        return $this->fetch('basic_config/overview');
    }

    /**
     * 获取模拟配置值
     * @param string $key
     * @return string
     */
    private function getMockConfigValue(string $key): string
    {
        $mockValues = [
            'site_name' => '博学文AI写作平台',
            'site_url' => 'https://www.boxiaowen.com',
            'admin_email' => '<EMAIL>',
            'timezone' => 'Asia/Shanghai',
            'login_attempts' => '5',
            'session_timeout' => '7200',
            'password_policy' => '强密码策略',
            'ip_whitelist' => '127.0.0.1,***********/24',
            'max_file_size' => '10MB',
            'allowed_extensions' => 'jpg,png,gif,pdf,doc,docx',
            'upload_path' => '/uploads/',
            'image_quality' => '85'
        ];
        
        return $mockValues[$key] ?? '未设置';
    }

    /**
     * 快速配置
     * @auth true
     */
    public function quick()
    {
        if ($this->request->isGet()) {
            // 获取常用配置项
            $quickConfigs = SystemConfig::mk()
                ->where('type', 'like', 'basic_%')
                ->where('name', 'in', [
                    'site_name', 'site_url', 'admin_email', 'timezone',
                    'max_file_size', 'login_attempts', 'session_timeout'
                ])
                ->select();
            
            $this->assign('quickConfigs', $quickConfigs);
            $this->assign('title', '快速配置');
            return $this->fetch('basic_config/quick');
        }

        $configs = $this->request->post('configs', []);
        
        if (empty($configs)) {
            $this->error('配置数据不能为空！');
        }

        $successCount = 0;
        $failCount = 0;

        foreach ($configs as $id => $value) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            // 解析原配置
            $configValue = json_decode($config->value, true);
            if (!is_array($configValue)) {
                $configValue = ['value' => $config->value];
            }

            // 更新值
            $configValue['value'] = $value;
            
            if ($config->save([
                'value' => json_encode($configValue, JSON_UNESCAPED_UNICODE),
                'update_time' => date('Y-m-d H:i:s')
            ])) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("快速配置完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 配置备份
     * @auth true
     */
    public function backup()
    {
        $configs = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要备份的配置！');
        }

        // 构建备份数据
        $backupData = [
            'backup_time' => date('Y-m-d H:i:s'),
            'backup_version' => '1.0',
            'configs' => $configs->toArray()
        ];

        // 生成备份文件名
        $backupFile = 'basic_config_backup_' . date('YmdHis') . '.json';
        
        // 这里应该保存到实际的备份目录
        // file_put_contents(app()->getRootPath() . 'backup/' . $backupFile, json_encode($backupData, JSON_UNESCAPED_UNICODE));
        
        $this->success('配置备份成功！备份文件：' . $backupFile);
    }

    /**
     * 配置恢复
     * @auth true
     */
    public function restore()
    {
        if ($this->request->isGet()) {
            // 获取备份文件列表（模拟）
            $backupFiles = [
                'basic_config_backup_20241201120000.json' => '2024-12-01 12:00:00',
                'basic_config_backup_20241130180000.json' => '2024-11-30 18:00:00',
                'basic_config_backup_20241129090000.json' => '2024-11-29 09:00:00'
            ];
            
            $this->assign('backupFiles', $backupFiles);
            $this->assign('title', '配置恢复');
            return $this->fetch('basic_config/restore');
        }

        $backupFile = $this->request->post('backup_file', '');
        
        if (empty($backupFile)) {
            $this->error('请选择备份文件！');
        }

        // 模拟恢复过程
        usleep(rand(1000000, 3000000)); // 1-3秒
        
        $this->success('配置恢复成功！');
    }

    /**
     * 配置验证
     * @auth true
     */
    public function validate()
    {
        $configs = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->where('status', 1)
            ->select();

        $validationResults = [];
        $totalConfigs = count($configs);
        $validConfigs = 0;
        $invalidConfigs = 0;

        foreach ($configs as $config) {
            $configValue = json_decode($config->value, true);
            $isValid = $this->validateConfig($config->type, $configValue);
            
            $validationResults[] = [
                'name' => $config->name,
                'title' => $config->title,
                'type' => $config->type,
                'is_valid' => $isValid,
                'message' => $isValid ? '配置有效' : '配置无效或缺失'
            ];
            
            if ($isValid) {
                $validConfigs++;
            } else {
                $invalidConfigs++;
            }
        }

        $summary = [
            'total' => $totalConfigs,
            'valid' => $validConfigs,
            'invalid' => $invalidConfigs,
            'valid_rate' => $totalConfigs > 0 ? round(($validConfigs / $totalConfigs) * 100, 2) : 0
        ];

        if ($this->request->isAjax()) {
            return json([
                'summary' => $summary,
                'results' => $validationResults
            ]);
        }

        $this->assign('summary', $summary);
        $this->assign('results', $validationResults);
        $this->assign('title', '配置验证');
        
        return $this->fetch('basic_config/validate');
    }

    /**
     * 验证单个配置
     * @param string $type
     * @param array $configValue
     * @return bool
     */
    private function validateConfig(string $type, array $configValue): bool
    {
        if (empty($configValue['value'])) {
            return false;
        }

        $value = $configValue['value'];
        $dataType = $configValue['data_type'] ?? 'string';

        // 基础数据类型验证
        switch ($dataType) {
            case 'integer':
                if (!is_numeric($value)) {
                    return false;
                }
                break;
                
            case 'float':
                if (!is_numeric($value)) {
                    return false;
                }
                break;
                
            case 'boolean':
                if (!is_bool($value)) {
                    return false;
                }
                break;
                
            case 'array':
                if (!is_array($value)) {
                    return false;
                }
                break;
        }

        // 特定类型验证
        switch ($type) {
            case 'basic_system':
                // 系统配置验证
                return true;
                
            case 'basic_security':
                // 安全配置验证
                return true;
                
            case 'basic_upload':
                // 上传配置验证
                return true;
                
            default:
                return true;
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'enable':
                    $result = $config->save(['status' => 1]);
                    break;
                case 'disable':
                    $result = $config->save(['status' => 0]);
                    break;
                case 'delete':
                    $result = $config->delete();
                    break;
                case 'reset':
                    // 重置为默认值
                    $configValue = json_decode($config->value, true);
                    if (isset($configValue['default_value'])) {
                        $configValue['value'] = $configValue['default_value'];
                        $result = $config->save([
                            'value' => json_encode($configValue, JSON_UNESCAPED_UNICODE)
                        ]);
                    } else {
                        $result = false;
                    }
                    break;
                default:
                    $result = false;
                    break;
            }

            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出基础配置
     * @auth true
     */
    public function export()
    {
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = SystemConfig::mk()->where('type', 'like', 'basic_%');

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $configs = $query->order('sort asc, id desc')->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要导出的配置！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '配置名称', '配置标题', '配置类型', '配置值', '状态', '创建时间'];
        
        foreach ($configs as $config) {
            $configValue = json_decode($config->value, true);
            $value = $configValue['value'] ?? '';
            
            // 处理复杂数据类型的显示
            if (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            } elseif (is_bool($value)) {
                $value = $value ? '是' : '否';
            }
            
            $exportData[] = [
                $config->id,
                $config->name,
                $config->title,
                $this->getConfigTypes()[$config->type] ?? $config->type,
                $value,
                $config->status ? '启用' : '禁用',
                $config->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 配置统计
     * @auth true
     */
    public function statistics()
    {
        // 总配置数
        $totalConfigs = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->count();
        
        // 启用的配置数
        $activeConfigs = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->where('status', 1)
            ->count();
        
        // 各类型配置数量统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getConfigTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
        }
        
        // 今日修改配置数
        $todayModified = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->whereTime('update_time', 'today')
            ->count();
        
        // 本月修改配置数
        $monthModified = SystemConfig::mk()
            ->where('type', 'like', 'basic_%')
            ->whereTime('update_time', 'month')
            ->count();

        $statistics = [
            'total_configs' => $totalConfigs,
            'active_configs' => $activeConfigs,
            'inactive_configs' => $totalConfigs - $activeConfigs,
            'today_modified' => $todayModified,
            'month_modified' => $monthModified,
            'type_stats' => $typeStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '配置统计');
        return $this->fetch('basic_config/statistics');
    }
}
