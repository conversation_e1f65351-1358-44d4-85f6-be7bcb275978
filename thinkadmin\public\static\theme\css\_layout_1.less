@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

> .layui-layout-admin {
  > .layui-side {
    background: @leftBackColor;

    #leftBackColor() {
      background-color: rgba(0, 0, 0, 0.6);
    }

    .layui-logo {
      #leftBackColor();

      .headtxt {
        text-shadow: rgba(0, 0, 0, 0.5) 1px 2px 2px;
      }
    }

    .layui-side-scroll {
      display: flex !important;
      box-sizing: border-box;
      #leftBackColor();
      #defaTransition(0.2s, background-color);

      .layui-side-icon {
        display: none;
      }

      .layui-side-tree {
        flex: 1;

        .layui-nav-item {

          a {
            color: @leftNormalTextColor;
            border-bottom: none !important;

            &:hover {
              color: @leftActiveTextColor;
            }
          }

          .layui-this, &.layui-this {
            > a {
              color: @leftActiveTextColor;
              background: @leftActiveBackColor;
            }
          }

          &ed > a {
            color: @leftActiveTextColor;
          }
        }
      }
    }
  }

  > .layui-header {
    background: @headNormalBackColor;
    #defaTransition(0.2s, background-color);

    .layui-nav-item {
      &.layui-this > a {
        color: @headActionTextColor;
        background: @headActiveBackColor;
      }

      > a {
        color: @headNormalTextColor;

        &:hover {
          color: @headActionTextColor;
        }
      }
    }
  }
}

.help-label b,
.layui-tab-title .layui-this {
  color: @mainActiveBack
}

.layui-btn-group {
  border-color: @mainActiveBack;
  background-color: @mainActiveBack;

  .layui-btn {
    &:hover:not(.layui-btn-active) {
      color: @mainActiveBack;
    }

    &.layui-btn-active {
      background-color: @mainActiveBack
    }
  }
}