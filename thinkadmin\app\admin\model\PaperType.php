<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 论文类型模型
 * @class PaperType
 * @package app\admin\model
 */
class PaperType extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'paper_type';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'word_count_min' => 'integer',
        'word_count_max' => 'integer',
        'outline_template_id' => 'integer',
        'prompt_template_id' => 'integer',
        'sort' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $status = $data['status'] ?? 0;
        return $status ? '启用' : '禁用';
    }

    /**
     * 获取字数范围文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getWordCountRangeAttr($value, array $data): string
    {
        $min = $data['word_count_min'] ?? 0;
        $max = $data['word_count_max'] ?? 0;
        return "{$min} - {$max} 字";
    }

    /**
     * 关联大纲模板
     * @return \think\model\relation\HasOne
     */
    public function outlineTemplate()
    {
        return $this->hasOne(OutlineTemplate::class, 'id', 'outline_template_id');
    }

    /**
     * 关联提示词模板
     * @return \think\model\relation\HasOne
     */
    public function promptTemplate()
    {
        return $this->hasOne(PromptTemplate::class, 'id', 'prompt_template_id');
    }

    /**
     * 关联论文项目
     * @return \think\model\relation\HasMany
     */
    public function paperProjects()
    {
        return $this->hasMany(PaperProject::class, 'paper_type_id', 'id');
    }

    /**
     * 获取启用的论文类型
     * @return array
     */
    public static function getEnabledTypes(): array
    {
        return static::mk()->where(['status' => 1])->order('sort asc,id desc')->column('name', 'id');
    }

    /**
     * 获取默认论文类型
     * @return array
     */
    public static function getDefaultType(): array
    {
        return static::mk()->where(['status' => 1])->order('sort asc,id desc')->findOrEmpty()->toArray();
    }
}
