{extend name="../../admin/view/main/layout.html" /}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-search margin-right-5"></span>
        查重记录详情
        <div class="pull-right">
            <button class="layui-btn layui-btn-sm" onclick="history.back()">
                <i class="layui-icon layui-icon-return"></i> 返回
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 基本信息 -->
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                                <tr>
                                    <td width="120">任务ID</td>
                                    <td>{$task.id}</td>
                                </tr>
                                <tr>
                                    <td>任务标题</td>
                                    <td>{$task.title}</td>
                                </tr>
                                <tr>
                                    <td>文件名称</td>
                                    <td>{$task.file_name}</td>
                                </tr>
                                <tr>
                                    <td>文件大小</td>
                                    <td>{$task.file_size|format_bytes}</td>
                                </tr>
                                <tr>
                                    <td>查重接口</td>
                                    <td>{$task.api.name|default='未知'}</td>
                                </tr>
                                <tr>
                                    <td>任务状态</td>
                                    <td>
                                        {switch name="task.status"}
                                            {case value="pending"}
                                                <span class="layui-badge">待处理</span>
                                            {/case}
                                            {case value="processing"}
                                                <span class="layui-badge layui-bg-blue">处理中</span>
                                            {/case}
                                            {case value="completed"}
                                                <span class="layui-badge layui-bg-green">已完成</span>
                                            {/case}
                                            {case value="failed"}
                                                <span class="layui-badge layui-bg-red">失败</span>
                                            {/case}
                                            {default /}
                                                <span class="layui-badge layui-bg-gray">{$task.status}</span>
                                        {/switch}
                                    </td>
                                </tr>
                                <tr>
                                    <td>创建时间</td>
                                    <td>{$task.create_time}</td>
                                </tr>
                                {if condition="$task.complete_time"}
                                <tr>
                                    <td>完成时间</td>
                                    <td>{$task.complete_time}</td>
                                </tr>
                                {/if}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">查重结果</div>
                    <div class="layui-card-body">
                        {if condition="$task.status == 'completed'"}
                        <table class="layui-table" lay-skin="nob">
                            <tbody>
                                <tr>
                                    <td width="120">总相似度</td>
                                    <td>
                                        <span class="layui-badge {if condition='$task.similarity_rate > 30'}layui-bg-red{elseif condition='$task.similarity_rate > 15'}layui-bg-orange{else}layui-bg-green{/if}">
                                            {$task.similarity_rate}%
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>总段落数</td>
                                    <td>{$task.total_paragraphs}</td>
                                </tr>
                                <tr>
                                    <td>相似段落数</td>
                                    <td>{$task.similar_paragraphs}</td>
                                </tr>
                                <tr>
                                    <td>匹配来源数</td>
                                    <td>{$task.matched_sources}</td>
                                </tr>
                                <tr>
                                    <td>检测费用</td>
                                    <td>¥{$task.cost|default='0.00'}</td>
                                </tr>
                                <tr>
                                    <td>报告文件</td>
                                    <td>
                                        {if condition="$task.report_file"}
                                            <a href="{$task.report_file}" target="_blank" class="layui-btn layui-btn-xs">
                                                <i class="layui-icon layui-icon-download-circle"></i> 下载报告
                                            </a>
                                        {else}
                                            <span class="color-desc">暂无报告</span>
                                        {/if}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        {else}
                        <div class="text-center color-desc padding-20">
                            {if condition="$task.status == 'pending'"}
                                任务等待处理中...
                            {elseif condition="$task.status == 'processing'"}
                                任务正在处理中，请稍候...
                            {elseif condition="$task.status == 'failed'"}
                                任务处理失败
                                {if condition="$task.error_message"}
                                    <br>错误信息：{$task.error_message}
                                {/if}
                            {/if}
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        {if condition="$task.status == 'completed' || $task.status == 'failed'"}
        <div class="layui-card margin-top-15">
            <div class="layui-card-header">操作</div>
            <div class="layui-card-body">
                {if condition="$task.canResubmit()"}
                <button class="layui-btn layui-btn-normal" onclick="resubmitTask({$task.id})">
                    <i class="layui-icon layui-icon-refresh-3"></i> 重新提交
                </button>
                {/if}
                
                {if condition="$task.hasReport()"}
                <button class="layui-btn layui-btn-normal" onclick="viewReport({$task.id})">
                    <i class="layui-icon layui-icon-file"></i> 查看报告
                </button>
                <button class="layui-btn" onclick="viewParagraphs({$task.id})">
                    <i class="layui-icon layui-icon-list"></i> 段落详情
                </button>
                {/if}
                
                <button class="layui-btn layui-btn-danger" onclick="deleteTask({$task.id})">
                    <i class="layui-icon layui-icon-delete"></i> 删除记录
                </button>
            </div>
        </div>
        {/if}

        <!-- 段落详情（如果有） -->
        {if condition="$task.status == 'completed' && !empty($paragraphs)"}
        <div class="layui-card margin-top-15">
            <div class="layui-card-header">
                段落查重详情
                <span class="layui-badge layui-bg-blue margin-left-10">{$paragraphs|count} 个段落</span>
            </div>
            <div class="layui-card-body">
                <div class="layui-collapse" lay-accordion="">
                    {volist name="paragraphs" id="paragraph"}
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">
                            段落 {$paragraph.paragraph_index}
                            <span class="layui-badge {if condition='$paragraph.similarity_rate > 30'}layui-bg-red{elseif condition='$paragraph.similarity_rate > 15'}layui-bg-orange{else}layui-bg-green{/if} margin-left-10">
                                相似度: {$paragraph.similarity_rate}%
                            </span>
                            {if condition="$paragraph.needsModification()"}
                            <span class="layui-badge layui-bg-red margin-left-5">需修改</span>
                            {/if}
                        </h2>
                        <div class="layui-colla-content">
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md12">
                                    <h4>原文内容</h4>
                                    <div class="layui-form-text" style="background: #f8f8f8; padding: 10px; border-radius: 4px;">
                                        {$paragraph.original_text}
                                    </div>
                                </div>
                                
                                {if condition="!empty($paragraph.matched_sources)"}
                                <div class="layui-col-md12 margin-top-10">
                                    <h4>匹配来源</h4>
                                    {volist name="paragraph.matched_sources" id="source"}
                                    <div class="layui-panel margin-bottom-10">
                                        <div class="layui-panel-header">
                                            来源 {$i}: {$source.title|default='未知来源'}
                                            <span class="layui-badge layui-bg-blue margin-left-10">相似度: {$source.similarity}%</span>
                                        </div>
                                        <div class="layui-panel-body">
                                            <p><strong>URL:</strong> 
                                                {if condition="!empty($source.url)"}
                                                    <a href="{$source.url}" target="_blank">{$source.url}</a>
                                                {else}
                                                    未知
                                                {/if}
                                            </p>
                                            <p><strong>匹配文本:</strong></p>
                                            <div style="background: #fff2e8; padding: 8px; border-radius: 4px; border-left: 3px solid #ff9800;">
                                                {$source.matched_text}
                                            </div>
                                        </div>
                                    </div>
                                    {/volist}
                                </div>
                                {/if}
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>

<script>
layui.use(['layer', 'element'], function(){
    var layer = layui.layer;
    var element = layui.element;
    
    // 重新提交任务
    window.resubmitTask = function(id) {
        layer.confirm('确定要重新提交这个查重任务吗？', {icon: 3, title: '提示'}, function(index){
            $.post('{:url("resubmit")}', {id: id}, function(res){
                if(res.code === 1) {
                    layer.msg(res.info, {icon: 1});
                    setTimeout(function(){
                        location.reload();
                    }, 1500);
                } else {
                    layer.msg(res.info, {icon: 2});
                }
            });
            layer.close(index);
        });
    };
    
    // 查看报告
    window.viewReport = function(id) {
        layer.open({
            type: 2,
            title: '查重报告',
            area: ['90%', '90%'],
            content: '{:url("report")}?id=' + id
        });
    };
    
    // 查看段落详情
    window.viewParagraphs = function(id) {
        layer.open({
            type: 2,
            title: '段落详情',
            area: ['90%', '90%'],
            content: '{:url("paragraph")}?id=' + id
        });
    };
    
    // 删除任务
    window.deleteTask = function(id) {
        layer.confirm('确定要删除这个查重记录吗？删除后无法恢复！', {icon: 3, title: '警告'}, function(index){
            $.post('{:url("remove")}', {id: id}, function(res){
                if(res.code === 1) {
                    layer.msg(res.info, {icon: 1});
                    setTimeout(function(){
                        history.back();
                    }, 1500);
                } else {
                    layer.msg(res.info, {icon: 2});
                }
            });
            layer.close(index);
        });
    };
});
</script>
{/block}
