{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">提示词模板{if isset($vo.id) && $vo.id}编辑{else}添加{/if}</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="{:request()->url()}" data-auto="true" method="post" autocomplete="off">
            
            {if isset($vo['id'])}<input type='hidden' value='{$vo.id}' name='id'>{/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <input name="name" value="{$vo.name|default=''}" required lay-verify="required" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板类型</label>
                <div class="layui-input-block">
                    <select name="type" lay-verify="required" required>
                        <option value="">请选择模板类型</option>
                        {foreach $typeOptions as $key=>$name}
                        <option value="{$key}" {if isset($vo['type']) && $vo['type'] eq $key}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-block">
                    <select name="paper_type_id">
                        <option value="0">通用（适用所有类型）</option>
                        {foreach $paperTypes as $id=>$name}
                        <option value="{$id}" {if isset($vo['paper_type_id']) && $vo['paper_type_id'] eq $id}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">AI模型</label>
                <div class="layui-input-block">
                    <select name="ai_model" lay-verify="required" required>
                        <option value="">请选择AI模型</option>
                        {foreach $aiModelOptions as $key=>$name}
                        <option value="{$key}" {if isset($vo['ai_model']) && $vo['ai_model'] eq $key}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">提示词内容</label>
                <div class="layui-input-block">
                    <textarea name="content" required lay-verify="required" placeholder="请输入提示词内容，可使用变量如 {{title}}、{{keywords}} 等" class="layui-textarea" style="min-height: 200px;">{$vo.content|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        支持变量替换，常用变量：{{title}}（标题）、{{keywords}}（关键词）、{{subject}}（主题）、{{requirements}}（要求）
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">变量定义</label>
                <div class="layui-input-block">
                    <textarea name="variables" placeholder="JSON格式定义变量，如：{&quot;title&quot;:&quot;论文标题&quot;,&quot;keywords&quot;:&quot;关键词&quot;}" class="layui-textarea">{$vo.variables|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        JSON格式定义模板中使用的变量及其说明
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模型参数</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Temperature</label>
                            <div class="layui-input-block">
                                <input name="temperature" value="{$vo.temperature|default=0.7}" type="number" step="0.1" min="0" max="2" placeholder="0.7" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Max Tokens</label>
                            <div class="layui-input-block">
                                <input name="max_tokens" value="{$vo.max_tokens|default=2000}" type="number" min="1" placeholder="2000" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">Top P</label>
                            <div class="layui-input-block">
                                <input name="top_p" value="{$vo.top_p|default=1}" type="number" step="0.1" min="0" max="1" placeholder="1" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">描述说明</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入模板描述说明" class="layui-textarea">{$vo.description|default=''}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_default" value="1" title="是" {if isset($vo['is_default']) && $vo['is_default'] eq 1}checked{/if}>
                    <input type="radio" name="is_default" value="0" title="否" {if !isset($vo['is_default']) || $vo['is_default'] eq 0}checked{/if}>
                    <div class="layui-form-mid layui-word-aux">
                        设为默认后，同类型同论文类型的其他模板将自动取消默认状态
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset($vo['status']) || $vo['status'] eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset($vo['status']) && $vo['status'] eq 0}checked{/if}>
                </div>
            </div>
            
            <div class="hr-line-dashed"></div>
            
            <div class="layui-form-item text-center">
                <button class="layui-btn" type='submit'>保存数据</button>
                <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
            </div>
            
        </form>
    </div>
</div>
{/block}
