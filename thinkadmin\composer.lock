{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "eda5b64f99aa56270b25df5c176809d8", "packages": [{"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "symfony/process", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-17T09:11:12+00:00"}, {"name": "topthink/framework", "version": "v8.1.3", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "e4207e98b66f92d26097ed6efd535930cba90e8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/e4207e98b66f92d26097ed6efd535930cba90e8f", "reference": "e4207e98b66f92d26097ed6efd535930cba90e8f", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=8.0.0", "psr/http-message": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "topthink/think-container": "^3.0", "topthink/think-helper": "^3.1", "topthink/think-orm": "^3.0|^4.0", "topthink/think-validate": "^3.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v8.1.3"}, "time": "2025-07-14T03:48:44+00:00"}, {"name": "topthink/think-container", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/top-think/think-container.git", "reference": "b2df244be1e7399ad4c8be1ccc40ed57868f730a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-container/zipball/b2df244be1e7399ad4c8be1ccc40ed57868f730a", "reference": "b2df244be1e7399ad4c8be1ccc40ed57868f730a", "shasum": ""}, "require": {"php": ">=8.0", "psr/container": "^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "support": {"issues": "https://github.com/top-think/think-container/issues", "source": "https://github.com/top-think/think-container/tree/v3.0.2"}, "time": "2025-04-07T03:21:51+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.11", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.11"}, "time": "2025-04-07T06:55:59+00:00"}, {"name": "topthink/think-migration", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/top-think/think-migration.git", "reference": "22c44058e1454f3af1d346e7f6524fbe654de7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-migration/zipball/22c44058e1454f3af1d346e7f6524fbe654de7fb", "reference": "22c44058e1454f3af1d346e7f6524fbe654de7fb", "shasum": ""}, "require": {"php": ">=7.2", "topthink/framework": "^6.0 || ^8.0", "topthink/think-helper": "^3.0.3"}, "require-dev": {"composer/composer": "^2.5.8", "fzaninotto/faker": "^1.8", "robmorgan/phinx": "^0.13.4"}, "suggest": {"fzaninotto/faker": "Required to use the factory builder (^1.8)."}, "type": "library", "extra": {"think": {"services": ["think\\migration\\Service"]}}, "autoload": {"psr-4": {"Phinx\\": "phinx", "think\\migration\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-migration/issues", "source": "https://github.com/top-think/think-migration/tree/v3.1.1"}, "time": "2023-09-14T05:51:31+00:00"}, {"name": "topthink/think-orm", "version": "v3.0.34", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "715e55da149fe32a12d68ef10e5b00e70bd3dbec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/715e55da149fe32a12d68ef10e5b00e70bd3dbec", "reference": "715e55da149fe32a12d68ef10e5b00e70bd3dbec", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=8.0.0", "psr/log": ">=1.0", "psr/simple-cache": ">=1.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.6|^10"}, "suggest": {"ext-mongodb": "provide mongodb support"}, "type": "library", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the PHP Database&ORM Framework", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/v3.0.34"}, "time": "2025-01-14T06:03:33+00:00"}, {"name": "topthink/think-template", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "shasum": ""}, "require": {"php": ">=8.0.0", "psr/simple-cache": ">=1.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "support": {"issues": "https://github.com/top-think/think-template/issues", "source": "https://github.com/top-think/think-template/tree/v3.0.2"}, "time": "2024-10-16T03:41:06+00:00"}, {"name": "topthink/think-validate", "version": "v3.0.7", "source": {"type": "git", "url": "https://github.com/top-think/think-validate.git", "reference": "85063f6d4ef8ed122f17a36179dc3e0949b30988"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-validate/zipball/85063f6d4ef8ed122f17a36179dc3e0949b30988", "reference": "85063f6d4ef8ed122f17a36179dc3e0949b30988", "shasum": ""}, "require": {"php": ">=8.0", "topthink/think-container": ">=3.0"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think validate", "support": {"issues": "https://github.com/top-think/think-validate/issues", "source": "https://github.com/top-think/think-validate/tree/v3.0.7"}, "time": "2025-06-11T05:51:40+00:00"}, {"name": "topthink/think-view", "version": "v2.0.5", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "b42009b98199b5a3833d3d6fd18c8a55aa511fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/b42009b98199b5a3833d3d6fd18c8a55aa511fad", "reference": "b42009b98199b5a3833d3d6fd18c8a55aa511fad", "shasum": ""}, "require": {"php": ">=8.0.0", "topthink/think-template": "^3.0"}, "type": "library", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "support": {"issues": "https://github.com/top-think/think-view/issues", "source": "https://github.com/top-think/think-view/tree/v2.0.5"}, "time": "2025-03-19T07:04:19+00:00"}, {"name": "zoujingli/ip2region", "version": "v2.0.6", "source": {"type": "git", "url": "https://github.com/zoujingli/ip2region.git", "reference": "66895178be204521e9f5ae9df0ea502893ee53b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/ip2region/zipball/66895178be204521e9f5ae9df0ea502893ee53b2", "reference": "66895178be204521e9f5ae9df0ea502893ee53b2", "shasum": ""}, "require": {"php": ">=5.4"}, "type": "library", "autoload": {"classmap": ["Ip2Region.php", "XdbSearcher.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://thinkadmin.top"}], "description": "Ip2Region for PHP", "homepage": "https://github.com/zoujingli/Ip2Region", "keywords": ["Ip2Region"], "support": {"issues": "https://github.com/zoujingli/ip2region/issues", "source": "https://github.com/zoujingli/ip2region/tree/v2.0.6"}, "time": "2024-08-02T01:01:01+00:00"}, {"name": "zoujingli/think-install", "version": "v1.0.49", "source": {"type": "git", "url": "https://github.com/zoujingli/think-install.git", "reference": "3b8f2eb4fd1275cecdebe8d39d530bb4071d7421"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-install/zipball/3b8f2eb4fd1275cecdebe8d39d530bb4071d7421", "reference": "3b8f2eb4fd1275cecdebe8d39d530bb4071d7421", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0 || *", "ext-json": "*", "php": ">=7.1", "symfony/process": "^5.4 || ^6.0 || *"}, "require-dev": {"composer/composer": "^1.0 || ^2.0"}, "type": "composer-plugin", "extra": {"class": ["think\\admin\\install\\Service"]}, "autoload": {"psr-4": {"think\\admin\\install\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Plugin In<PERSON>ler for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"issues": "https://github.com/zoujingli/think-install/issues", "source": "https://github.com/zoujingli/think-install/tree/v1.0.49"}, "time": "2025-01-07T00:25:14+00:00"}, {"name": "zoujingli/think-library", "version": "v6.1.86", "source": {"type": "git", "url": "https://github.com/zoujingli/ThinkLibrary.git", "reference": "971fb35b01b40e89ae1fac1a75acea444e831aa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/ThinkLibrary/zipball/971fb35b01b40e89ae1fac1a75acea444e831aa9", "reference": "971fb35b01b40e89ae1fac1a75acea444e831aa9", "shasum": ""}, "require": {"ext-curl": "*", "ext-gd": "*", "ext-iconv": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-zlib": "*", "php": ">=7.1", "symfony/process": "^5.4|^6.0|*", "topthink/framework": "^6.0|^8.0|*", "topthink/think-migration": "^3.0|*", "topthink/think-orm": "^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "extra": {"think": {"services": ["think\\admin\\Library"]}}, "autoload": {"files": ["src/common.php"], "psr-4": {"think\\admin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Library for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"email": "<EMAIL>", "forum": "https://thinkadmin.top", "issues": "https://gitee.com/zoujingli/ThinkLibrary/issues", "source": "https://gitee.com/zoujingli/ThinkLibrary", "wiki": "https://thinkadmin.top"}, "time": "2025-04-11T15:09:14+00:00"}, {"name": "zoujingli/think-plugs-account", "version": "v1.0.22", "source": {"type": "git", "url": "https://github.com/zoujingli/think-plugs-account.git", "reference": "152b0df19ac2cdee9ce837544435762a5a282eff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-plugs-account/zipball/152b0df19ac2cdee9ce837544435762a5a282eff", "reference": "152b0df19ac2cdee9ce837544435762a5a282eff", "shasum": ""}, "require": {"ext-curl": "*", "ext-gd": "*", "ext-json": "*", "php": ">7.1", "zoujingli/think-install": "^1.0|@dev", "zoujingli/think-library": "^6.1|@dev"}, "require-dev": {"phpunit/phpunit": "^7.0|*"}, "type": "think-admin-plugin", "extra": {"think": {"services": ["plugin\\account\\Service"]}, "config": {"name": "用户账号管理", "type": "plugin", "license": ["VIP"], "document": "https://thinkadmin.top/plugin/think-plugs-account.html"}, "plugin": {"copy": {"stc/database": "database/migrations"}}}, "autoload": {"psr-4": {"plugin\\account\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Account Plugin for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"issues": "https://github.com/zoujingli/think-plugs-account/issues", "source": "https://github.com/zoujingli/think-plugs-account/tree/v1.0.22"}, "time": "2025-04-11T15:20:15+00:00"}, {"name": "zoujingli/think-plugs-admin", "version": "v1.0.71", "source": {"type": "git", "url": "https://github.com/zoujingli/think-plugs-admin.git", "reference": "9ec411af17591ba83bdb264fc1edf97a27ffeed0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-plugs-admin/zipball/9ec411af17591ba83bdb264fc1edf97a27ffeed0", "reference": "9ec411af17591ba83bdb264fc1edf97a27ffeed0", "shasum": ""}, "require": {"ext-json": "*", "php": ">7.1", "topthink/framework": "^6.0|^8.0", "topthink/think-view": "^1.0|^2.0", "zoujingli/ip2region": "^1.0|^2.0|@dev", "zoujingli/think-install": "^1.0|@dev", "zoujingli/think-library": "^6.1|@dev", "zoujingli/think-plugs-static": "^1.0|@dev"}, "type": "think-admin-plugin", "extra": {"think": {"services": ["app\\admin\\Service"]}, "config": {"name": "系统后台管理", "type": "module", "document": "https://thinkadmin.top/plugin/think-plugs-admin.html", "description": "后台基础管理模块，系统账号及安全配置管理。"}, "plugin": {"copy": {"src": "!app/admin", "stc/database": "database/migrations"}, "clear": true}}, "autoload": {"psr-4": {"app\\admin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Admin Plugin for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"issues": "https://github.com/zoujingli/think-plugs-admin/issues", "source": "https://github.com/zoujingli/think-plugs-admin/tree/v1.0.71"}, "time": "2025-04-10T13:52:11+00:00"}, {"name": "zoujingli/think-plugs-center", "version": "v1.0.37", "source": {"type": "git", "url": "https://github.com/zoujingli/think-plugs-center.git", "reference": "9dbb850eb51b8f9c6b55ff760f2dcb7c116559f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-plugs-center/zipball/9dbb850eb51b8f9c6b55ff760f2dcb7c116559f9", "reference": "9dbb850eb51b8f9c6b55ff760f2dcb7c116559f9", "shasum": ""}, "require": {"ext-json": "*", "php": ">7.1", "zoujingli/think-install": "^1.0|@dev", "zoujingli/think-library": "^6.1|@dev"}, "type": "think-admin-plugin", "extra": {"think": {"services": ["plugin\\center\\Service"]}, "config": {"name": "插件应用管理", "type": "service", "document": "https://thinkadmin.top/plugin/think-plugs-center.html"}, "plugin": {"copy": {"stc/database": "database/migrations"}}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"plugin\\center\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Plugin Center for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"issues": "https://github.com/zoujingli/think-plugs-center/issues", "source": "https://github.com/zoujingli/think-plugs-center/tree/v1.0.37"}, "time": "2025-04-10T13:52:11+00:00"}, {"name": "zoujingli/think-plugs-payment", "version": "v1.0.15", "source": {"type": "git", "url": "https://github.com/zoujingli/think-plugs-payment.git", "reference": "eb15095d848d7f953b11ce7b9dd88adab98f6a36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-plugs-payment/zipball/eb15095d848d7f953b11ce7b9dd88adab98f6a36", "reference": "eb15095d848d7f953b11ce7b9dd88adab98f6a36", "shasum": ""}, "require": {"ext-json": "*", "php": ">7.1", "zoujingli/think-plugs-account": "^1.0|@dev", "zoujingli/think-plugs-admin": "^1.0|@dev"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "think-admin-plugin", "extra": {"think": {"services": ["plugin\\payment\\Service"]}, "config": {"name": "系统支付管理", "type": "plugin", "license": ["VIP"], "document": "https://thinkadmin.top/plugin/think-plugs-payment.html"}, "plugin": {"copy": {"stc/database": "database/migrations"}}}, "autoload": {"psr-4": {"plugin\\payment\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Payment Plugin for ThinkAdmin", "homepage": "https://thinkadmin.top", "support": {"issues": "https://github.com/zoujingli/think-plugs-payment/issues", "source": "https://github.com/zoujingli/think-plugs-payment/tree/v1.0.15"}, "time": "2025-04-10T13:52:11+00:00"}, {"name": "zoujingli/think-plugs-static", "version": "v1.0.132", "source": {"type": "git", "url": "https://github.com/zoujingli/think-plugs-static.git", "reference": "5b304f559c2142f3fbd73483277951bf5dc30d31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zoujingli/think-plugs-static/zipball/5b304f559c2142f3fbd73483277951bf5dc30d31", "reference": "5b304f559c2142f3fbd73483277951bf5dc30d31", "shasum": ""}, "require": {"php": ">=7.1", "zoujingli/think-install": "^1.0||dev-master"}, "type": "think-admin-plugin", "extra": {"plugin": {"copy": {"stc/think": "!think", "stc/public/index.php": "!public/index.php", "stc/public/static/plugs": "!public/static/plugs", "stc/public/static/theme": "!public/static/theme", "stc/public/static/admin.js": "!public/static/admin.js", "stc/public/static/login.js": "!public/static/login.js"}, "init": {"stc/.env.example": ".env.example", "stc/config/app.php": "config/app.php", "stc/config/log.php": "config/log.php", "stc/class/Index.php": "app/index/controller/Index.php", "stc/config/lang.php": "config/lang.php", "stc/config/view.php": "config/view.php", "stc/config/cache.php": "config/cache.php", "stc/config/phinx.php": "config/phinx.php", "stc/config/route.php": "config/route.php", "stc/public/.htaccess": "public/.htaccess", "stc/config/cookie.php": "config/cookie.php", "stc/public/robots.txt": "public/robots.txt", "stc/public/router.php": "public/router.php", "stc/config/session.php": "config/session.php", "stc/config/database.php": "config/database.php", "stc/public/static/extra/script.js": "public/static/extra/script.js", "stc/public/static/extra/style.css": "public/static/extra/style.css"}, "clear": true}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Static Files for ThinkAdmin", "support": {"issues": "https://github.com/zoujingli/think-plugs-static/issues", "source": "https://github.com/zoujingli/think-plugs-static/tree/v1.0.132"}, "time": "2025-07-30T00:35:32+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.1"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}