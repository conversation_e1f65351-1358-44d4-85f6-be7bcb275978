<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>{block name="title"}{$title|default=''}{if !empty($title)} · {/if}{:sysconf('site_name')}{/block}</title>
    <meta name="renderer" content="webkit">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
    <link rel="stylesheet" href="__ROOT__/static/theme/css/iconfont.css?at={:date('md')}">
    <link rel="stylesheet" href="__ROOT__/static/plugs/layui/css/layui.css?v={:date('ymd')}">
    {if file_exists(syspath("public/static/extra/icon/iconfont.css"))}
    <link rel="stylesheet" href="__ROOT__/static/extra/icon/iconfont.css?at={:date('md')}">
    {/if}
    <style>
        ::-webkit-input-placeholder {
            color: #aaa
        }

        ::-webkit-scrollbar {
            width: 3px;
            height: 3px
        }

        ::-webkit-scrollbar-track {
            background: #ccc
        }

        ::-webkit-scrollbar-thumb {
            background-color: #666
        }

        ::selection {
            background-color: #ec494e;
            color: #fff
        }

        ::-moz-selection {
            background-color: #ec494e;
            color: #fff
        }

        :-webkit-autofill {
            -webkit-box-shadow: 0 0 0 1000px white inset;
            -webkit-text-fill-color: #333
        }

        ul li {
            width: 20%;
            height: 65px;
            display: block;
            float: left;
            margin-right: -1px;
            margin-left: -2px;
            margin-bottom: -2px;
            cursor: pointer;
            font-size: 14px;
            overflow: hidden;
            text-align: center;
            padding: 15px 0 10px 0;
            border: 1px solid #e2e2e2;
            background-color: #efefef;
            user-select: none;
            -ms-user-select: none;
            -moz-user-select: none;
            -webkit-user-select: none;
            transition: all .2s linear;
            -o-transition: all .2s linear;
            -moz-transition: all .2s linear;
            -webkit-transition: all .2s linear
        }

        ul li:hover {
            color: #fff;
            background-color: #563d7c
        }

        ul li:hover i, ul li:hover div {
            color: #fff;
        }

        ul li i {
            color: #333;
            display: inline-block;
            font-size: 30px !important
        }

        ul li div {
            color: #333;
            height: 35px;
            font-size: 13px;
            line-height: 35px;
            white-space: nowrap
        }
    </style>
</head>
<body>

<ul>
    {foreach $extraIcons??[] as $icon}
    <li>
        <i class="iconfont {$icon}"></i>
        <div class="icon-title">{$icon}</div>
    </li>
    {/foreach}
    {foreach $layuiIcons??[] as $icon}
    <li>
        <i class="layui-icon {$icon}"></i>
        <div class="icon-title">{$icon}</div>
    </li>
    {/foreach}
    {foreach $thinkIcons??[] as $icon}
    <li>
        <i class="iconfont {$icon}"></i>
        <div class="icon-title">{$icon}</div>
    </li>
    {/foreach}
</ul>

<script src="__ROOT__/static/plugs/jquery/jquery.min.js" type="text/javascript"></script>
<script>
    $(function () {
        $('li').on('click', function (className) {
            if ((className = $(this).find('i').get(0).className)) {
                top.$('[name="{$field}"]').val(className).trigger('change');
                top.layer.close(top.layer.getFrameIndex(window.name));
            }
        });
    });
</script>
</body>
</html>