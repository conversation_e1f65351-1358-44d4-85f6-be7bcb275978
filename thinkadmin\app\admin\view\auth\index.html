{extend name='table'}

{block name="button"}
<!--{if auth("add")}-->
<button data-open='{:url("add")}' data-table-id="RoleTable" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('添加权限')}</button>
<!--{/if}-->

<!--{if auth("remove")}-->
<button data-action='{:url("remove")}' data-rule="id#{id}" data-table-id="RoleTable" data-confirm="{:lang('确定要批量删除权限吗？')}" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('批量删除')}</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="think-box-shadow">
    {include file='auth/index_search'}
    <table id="RoleTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 初始化表格组件
        $('#RoleTable').layTable({
            even: true, height: 'full',
            sort: {field: 'sort desc,id', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'sort', title: '{:lang("排序权重")}', align: 'center', width: 100, sort: true, templet: '#SortInputRoleTableTpl'},
                {field: 'title', title: '{:lang("权限名称")}', align: 'center', minWidth: 140},
                {field: 'desc', title: '{:lang("权限描述")}', align: 'center', minWidth: 110, templet: '<div>{{d.desc||"-"}}</div>'},
                {field: 'status', title: '{:lang("使用状态")}', align: 'center', minWidth: 110, templet: '#StatusSwitchRoleTableTpl'},
                {field: 'create_at', title: '{:lang("创建时间")}', align: 'center', minWidth: 170, sort: true},
                {toolbar: '#ToolbarRoleTableTpl', title: '{:lang("操作面板")}', align: 'center', minWidth: 210, fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitchRoleTable)', function (obj) {
            let data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#RoleTable').trigger('reload');
                });
                return false;
            }, false);
        });
    });

</script>

<!-- 列表排序权重模板 -->
<script type="text/html" id="SortInputRoleTableTpl">
    <input type="number" min="0" data-blur-number="0" data-action-blur="{:request()->url()}" data-value="id#{{d.id}};action#sort;sort#{value}" data-loading="false" value="{{d.sort}}" class="layui-input text-center">
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchRoleTableTpl">
    <!--{if auth("state")}-->
    <input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="{:lang('已激活')}|{:lang('已禁用')}" lay-filter="StatusSwitchRoleTable" {{-d.status>0?'checked':''}}>
    <!--{else}-->
    {{-d.status ? '<b class="color-green">{:lang("已启用")}</b>' : '<b class="color-red">{:lang("已禁用")}</b>'}}
    <!--{/if}-->
</script>

<!-- 数据操作工具条模板 -->
<script type="text/html" id="ToolbarRoleTableTpl">
    <!--{if auth('edit')}-->
    <a class="layui-btn layui-btn-primary layui-btn-sm" data-open='{:url("edit")}?id={{d.id}}'>{:lang("编 辑")}</a>
    <!--{/if}-->

    <!--{if auth("remove")}-->
    <a class="layui-btn layui-btn-danger layui-btn-sm" data-action="{:url('remove')}" data-value="id#{{d.id}}" data-confirm="{:lang('确定要删除权限吗?')}">{:lang("删 除")}</a>
    <!--{/if}-->
</script>
{/block}