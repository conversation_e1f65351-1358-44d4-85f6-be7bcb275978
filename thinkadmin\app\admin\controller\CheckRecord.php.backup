<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\CheckTask;
use app\admin\model\CheckDetail;
use app\admin\model\CheckApi;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 查重记录管理
 * @class CheckRecord
 * @package app\admin\controller
 */
class CheckRecord extends Controller
{
    /**
     * 查重记录管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        CheckRecordModel::mQuery()->layTable(function () {
            $this->title = '查重记录管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            $query->like('title,file_name')->equal('user_id,check_api_id,status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 查看查重记录详情
     * @auth true
     */public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $task = CheckTask::mk()->with(['checkApi'])->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('查重记录不存在！');
        }

        // 获取查重详情
        $details = CheckDetail::mk()
            ->where('task_id', $id)
            ->order('paragraph_index asc')
            ->select();

        // 获取段落统计
        $stats = CheckDetail::getParagraphStats($id);

        $this->assign('task', $task);
        $this->assign('details', $details);
        $this->assign('stats', $stats);
        $this->assign('title', '查重记录详情');
        
        return $this->fetch('check_record/view');
    }

    /**
     * 查看段落详情
     * @auth true
     */
    public function paragraph()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('段落ID不能为空！');
        }

        $detail = CheckDetail::mk()->with(['checkTask'])->findOrEmpty($id);
        if ($detail->isEmpty()) {
            $this->error('段落详情不存在！');
        }

        $this->assign('detail', $detail);
        $this->assign('title', '段落查重详情');
        
        return $this->fetch('check_record/paragraph');
    }

    /**
     * 查重报告预览
     * @auth true
     */
    public function report()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = CheckTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('查重任务不存在！');
        }

        if (!$task->hasReport()) {
            $this->error('该任务暂无查重报告！');
        }

        // 如果有在线报告URL，直接跳转
        if (!empty($task->report_url)) {
            return redirect($task->report_url);
        }

        // 如果有本地报告文件，显示文件内容
        $reportPath = $task->getReportPath();
        if (!empty($reportPath)) {
            $this->assign('reportPath', $reportPath);
            $this->assign('task', $task);
            return $this->fetch('check_record/report');
        }

        $this->error('报告文件不存在！');
    }

    /**
     * 下载查重报告
     * @auth true
     */
    public function download()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = CheckTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('查重任务不存在！');
        }

        $reportPath = $task->getReportPath();
        if (empty($reportPath)) {
            $this->error('报告文件不存在！');
        }

        // 设置下载文件名
        $fileName = $task->title . '_查重报告_' . date('YmdHis', strtotime($task->create_time)) . '.pdf';
        
        return download($reportPath, $fileName);
    }

    /**
     * 重新提交查重
     * @auth true
     */
    public function resubmit()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = CheckTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('查重任务不存在！');
        }

        if (!$task->canResubmit()) {
            $this->error('该任务当前状态不允许重新提交！');
        }

        // 重置任务状态
        $updateData = [
            'status' => 'pending',
            'similarity_rate' => null,
            'report_url' => null,
            'report_path' => null,
            'error_message' => null,
            'update_time' => date('Y-m-d H:i:s')
        ];

        if ($task->save($updateData)) {
            // 清除旧的查重详情
            CheckDetail::mk()->where('task_id', $id)->delete();
            
            // 这里应该触发重新提交的逻辑，比如调用n8n工作流
            $this->success('任务已重新提交查重！');
        } else {
            $this->error('重新提交失败！');
        }
    }

    /**
     * 删除查重记录
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $task = CheckTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('查重记录不存在！');
        }

        // 删除相关的查重详情
        CheckDetail::mk()->where('task_id', $id)->delete();
        
        // 删除本地报告文件
        $reportPath = $task->getReportPath();
        if (!empty($reportPath) && file_exists($reportPath)) {
            @unlink($reportPath);
        }
        
        // 删除查重任务
        if ($task->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 批量删除查重记录
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的记录！');
        }

        $idArray = explode(',', $ids);
        
        // 获取要删除的任务
        $tasks = CheckTask::mk()->whereIn('id', $idArray)->select();
        
        // 删除相关的查重详情
        CheckDetail::mk()->whereIn('task_id', $idArray)->delete();
        
        // 删除本地报告文件
        foreach ($tasks as $task) {
            $reportPath = $task->getReportPath();
            if (!empty($reportPath) && file_exists($reportPath)) {
                @unlink($reportPath);
            }
        }
        
        // 删除查重任务
        $result = CheckTask::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }

    /**
     * 导出查重记录
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的记录！');
        }

        $idArray = explode(',', $ids);
        $records = CheckTask::mk()
            ->with(['checkApi'])
            ->whereIn('id', $idArray)
            ->order('id desc')
            ->select();

        if ($records->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '任务标题', '用户ID', '文件名', '文件大小', '字数', '查重接口', '相似度', '状态', '费用', '创建时间'];
        
        foreach ($records as $record) {
            $exportData[] = [
                $record->id,
                $record->title,
                $record->user_id,
                $record->file_name,
                $record->file_size_text,
                $record->word_count,
                $record->checkApi->name ?? '未知',
                $record->similarity_rate ? $record->similarity_rate . '%' : '-',
                $record->status_text,
                $record->cost,
                $record->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 获取查重统计数据
     * @auth true
     */
    public function statistics()
    {
        // 总任务数
        $totalTasks = CheckTask::mk()->count();
        
        // 各状态任务数
        $statusStats = CheckTask::mk()
            ->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();

        // 今日任务数
        $todayTasks = CheckTask::mk()
            ->whereTime('create_time', 'today')
            ->count();

        // 本月任务数
        $monthTasks = CheckTask::mk()
            ->whereTime('create_time', 'month')
            ->count();

        // 平均相似度
        $avgSimilarity = CheckTask::mk()
            ->where('similarity_rate', 'not null')
            ->avg('similarity_rate');

        // 高相似度任务数（>30%）
        $highSimilarityTasks = CheckTask::mk()
            ->where('similarity_rate', '>', 30)
            ->count();

        $statistics = [
            'total_tasks' => $totalTasks,
            'today_tasks' => $todayTasks,
            'month_tasks' => $monthTasks,
            'status_stats' => $statusStats,
            'avg_similarity' => round($avgSimilarity, 2),
            'high_similarity_tasks' => $highSimilarityTasks
        ];

        return json($statistics);
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'CheckRecord管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
