<?php return array(
    'root' => array(
        'name' => 'zoujingli/thinkadmin',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v8.1.3',
            'version' => '8.1.3.0',
            'reference' => 'e4207e98b66f92d26097ed6efd535930cba90e8f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-container' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'b2df244be1e7399ad4c8be1ccc40ed57868f730a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-helper' => array(
            'pretty_version' => 'v3.1.11',
            'version' => '3.1.11.0',
            'reference' => '1d6ada9b9f3130046bf6922fe1bd159c8d88a33c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-migration' => array(
            'pretty_version' => 'v3.1.1',
            'version' => '3.1.1.0',
            'reference' => '22c44058e1454f3af1d346e7f6524fbe654de7fb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-migration',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-orm' => array(
            'pretty_version' => 'v3.0.34',
            'version' => '3.0.34.0',
            'reference' => '715e55da149fe32a12d68ef10e5b00e70bd3dbec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-orm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-template' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0b88bd449f0f7626dd75b05f557c8bc208c08b0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-validate' => array(
            'pretty_version' => 'v3.0.7',
            'version' => '3.0.7.0',
            'reference' => '85063f6d4ef8ed122f17a36179dc3e0949b30988',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-validate',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'topthink/think-view' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '*******',
            'reference' => 'b42009b98199b5a3833d3d6fd18c8a55aa511fad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../topthink/think-view',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/ip2region' => array(
            'pretty_version' => 'v2.0.6',
            'version' => '*******',
            'reference' => '66895178be204521e9f5ae9df0ea502893ee53b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zoujingli/ip2region',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-install' => array(
            'pretty_version' => 'v1.0.49',
            'version' => '********',
            'reference' => '3b8f2eb4fd1275cecdebe8d39d530bb4071d7421',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-install',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-library' => array(
            'pretty_version' => 'v6.1.86',
            'version' => '********',
            'reference' => '971fb35b01b40e89ae1fac1a75acea444e831aa9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zoujingli/think-library',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-plugs-account' => array(
            'pretty_version' => 'v1.0.22',
            'version' => '********',
            'reference' => '152b0df19ac2cdee9ce837544435762a5a282eff',
            'type' => 'think-admin-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-plugs-account',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-plugs-admin' => array(
            'pretty_version' => 'v1.0.71',
            'version' => '1.0.71.0',
            'reference' => '9ec411af17591ba83bdb264fc1edf97a27ffeed0',
            'type' => 'think-admin-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-plugs-admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-plugs-center' => array(
            'pretty_version' => 'v1.0.37',
            'version' => '1.0.37.0',
            'reference' => '9dbb850eb51b8f9c6b55ff760f2dcb7c116559f9',
            'type' => 'think-admin-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-plugs-center',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-plugs-payment' => array(
            'pretty_version' => 'v1.0.15',
            'version' => '1.0.15.0',
            'reference' => 'eb15095d848d7f953b11ce7b9dd88adab98f6a36',
            'type' => 'think-admin-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-plugs-payment',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/think-plugs-static' => array(
            'pretty_version' => 'v1.0.132',
            'version' => '1.0.132.0',
            'reference' => '5b304f559c2142f3fbd73483277951bf5dc30d31',
            'type' => 'think-admin-plugin',
            'install_path' => __DIR__ . '/../zoujingli/think-plugs-static',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/thinkadmin' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
