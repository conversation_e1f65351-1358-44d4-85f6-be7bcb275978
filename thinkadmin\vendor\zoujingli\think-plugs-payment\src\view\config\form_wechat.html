<label class="layui-form-item block relative">
    <span class="help-label"><b>绑定公众号</b>Bind WeChat Appid</span>
    <input class="layui-input" maxlength="18" name="wechat_appid" pattern="^wx[0-9a-z]{16}$" vali-name="公众号APPID" placeholder="请输入18位绑定公众号（必填）" required value="{$vo.content.wechat_appid|default=''}">
    <span class="help-block"><b>必填，</b>公众号APPID，微信商户绑定的 服务号APPID 或 小程序APPID</span>
</label>

<label class="layui-form-item block relative">
    <span class="help-label"><b>微信商户号</b>WeChat Payment Number</span>
    <input class="layui-input" name="wechat_mch_id" placeholder="请输入微信商户号（必填）" required vali-name="微信商户号" value="{$vo.content.wechat_mch_id|default=''}">
    <span class="help-block"><b>必填，</b>微信商户编号，需要在微信商户平台获取，微信商户号 与 公众号APPID 匹配</span>
</label>

<div class="layui-form-item block relative">
    <span class="help-label label-required-prev"><b>商户接口版本</b>WeChat Payment Version</span>
    <div class="help-checks layui-textarea">
        {empty name='vo.content.wechat_mch_ver'}{assign name='vo.content.wechat_mch_ver' value='v2'}{/empty}
        {foreach ['v2'=>'微信支付 V2 接口','v3'=>'微信支付 V3 接口'] as $k=>$v}
        <label class="pointer think-radio">
            {if isset($vo.content.wechat_mch_ver) and $vo.content.wechat_mch_ver eq $k}
            <input type="radio" name="wechat_mch_ver" value="{$k}" checked lay-ignore> {$v}
            {else}
            <input type="radio" name="wechat_mch_ver" value="{$k}" lay-ignore> {$v}
            {/if}
        </label>
        {/foreach}
    </div>
</div>

<label data-wechat-version="v2" class="layui-form-item relative layui-hide">
    <span class="help-label"><b>微信商户 V2 密钥</b>WeChat Payment Secret Key</span>
    <input class="layui-input font-code" maxlength="32" name="wechat_mch_key" pattern=".{32}" placeholder="请输入32位微信商户密钥（必填）" required vali-name="商户密钥" value="{$vo.content.wechat_mch_key|default=''}">
    <span class="help-block"><b>必填，</b>微信商户密钥，需要在微信商户平台操作设置密码并获取密钥，建议定期更换密钥。</span>
</label>

<label data-wechat-version="v3" class="layui-form-item relative layui-hide">
    <span class="help-label"><b>微信商户 V3 密钥</b>WeChat Payment Secret Key</span>
    <input class="layui-input font-code" maxlength="32" name="wechat_mch_v3_key" required vali-name="商户密钥" pattern=".{32}" placeholder="请输入32位微信商户密钥（必填）" value="{$vo.content.wechat_mch_v3_key|default=''}">
    <span class="help-block"><b>必填，</b>微信商户密钥，需要在微信商户平台操作设置密码并获取密钥，建议定期更换密钥。</span>
</label>

<fieldset class="layui-bg-gray">
    <legend><span class="layui-badge layui-bg-blue">微信商户证书</span></legend>
    <div>
        <label class="layui-form-item block relative">
            <span class="help-label"><b>商户证书公钥序号</b>WeChat Payment Serial</span>
            <input class="layui-input font-code" name="wechat_mch_cer_id" vali-name="支付公钥ID" placeholder="请输入商户证书公钥序号" value="{$vo.content.wechat_mch_cer_id|default=''}">
            <span class="help-block"><b>可选，</b>商户证书公钥序号，需要在微信商户平台生成商户证书时，可以获取到公钥序号。</span>
        </label>
        <label class="layui-form-item block relative">
            <span class="help-label"><b>商户证书公钥内容</b>（ 需要填写文件的全部内容 ）</span>
            <textarea class="layui-textarea font-code" name="wechat_mch_cer_text" required vali-name="证书内容" placeholder="请输入商户证书公钥内容">{$vo.content.wechat_mch_cer_text|default=''}</textarea>
            <span class="help-block"><b>必填，</b>从商户平台上下载支付证书，解压并取得其中的 apiclient_cert.pem 用记事本打开并复制文件内容填至此处。</span>
        </label>
        <label class="layui-form-item block relative">
            <span class="help-label"><b>微信商户密钥文件内容</b>（ 需要填写文件的全部内容 ）</span>
            <textarea class="layui-textarea font-code" name="wechat_mch_key_text" required vali-name="密钥内容" placeholder="请输入微信商户密钥文件内容">{$vo.content.wechat_mch_key_text|default=''}</textarea>
            <span class="help-block"><b>必填，</b>从商户平台上下载支付证书，解压并取得其中的 apiclient_key.pem 用记事本打开并复制文件内容填至此处。</span>
        </label>
    </div>
</fieldset>

<fieldset data-wechat-version="v3" class="layui-hide layui-bg-gray">
    <legend><span class="layui-badge layui-bg-blue">微信支付公钥</span></legend>
    <div>
        <label class="layui-form-item block relative">
            <span class="help-label"><b>微信支付公钥 ID</b>WeChat Payment CertId</span>
            <input class="layui-input font-code" name="wechat_mch_v3_paycer_id" vali-name="支付公钥ID" placeholder="请输入微信支付公钥ID" value="{$vo.content.wechat_mch_v3_paycer_id|default=''}">
            <span class="help-block"><b>可选，</b>微信商户密钥，需要在微信商户平台操作设置密码并获取密钥，建议定期更换密钥。</span>
        </label>
        <label class="layui-form-item block relative">
            <span class="help-label"><b>微信支付公钥内容</b>（ 需要填写文件的全部内容 ）</span>
            <textarea class="layui-textarea font-code" name="wechat_mch_v3_paycer_text" vali-name="支付公钥" placeholder="请输入微信支付公钥内容">{$vo.content.wechat_mch_v3_paycer_text|default=''}</textarea>
            <span class="help-block"><b>可选，</b>从商户平台上下载支付证书，解压并取得其中的 pub_key.pem 用记事本打开并复制文件内容填至此处。</span>
        </label>
    </div>
</fieldset>

<script>
    $(function () {
        $('input[name="wechat_mch_ver"]').off('change').on('change', function () {
            let ver = $('input[name=wechat_mch_ver]:checked').val();
            let chk = $('[data-wechat-version="' + ver + '"]').removeClass('layui-hide').addClass('block');
            $('[data-wechat-version]').not(chk).addClass('layui-hide').removeClass('block');
        }).trigger('change');
    });
</script>