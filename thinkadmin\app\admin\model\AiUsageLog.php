<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * AI使用统计模型
 * @class AiUsageLog
 * @package app\admin\model
 */
class AiUsageLog extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'ai_usage_log';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'ai_model_id' => 'integer',
        'task_id' => 'integer',
        'prompt_tokens' => 'integer',
        'completion_tokens' => 'integer',
        'total_tokens' => 'integer',
        'cost' => 'float',
        'response_time' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $statusMap = [
            'success' => '成功',
            'failed' => '失败',
            'timeout' => '超时',
            'error' => '错误'
        ];
        return $statusMap[$data['status'] ?? 'success'] ?? '未知';
    }

    /**
     * 获取任务类型文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getTaskTypeTextAttr($value, array $data): string
    {
        $typeMap = [
            'paper_writing' => '论文写作',
            'outline_generation' => '大纲生成',
            'content_rewrite' => '内容改写',
            'plagiarism_check' => '查重检测',
            'translation' => '翻译',
            'other' => '其他'
        ];
        return $typeMap[$data['task_type'] ?? 'other'] ?? '未知';
    }

    /**
     * 获取成本文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getCostTextAttr($value, array $data): string
    {
        $cost = $data['cost'] ?? 0;
        return '¥' . number_format($cost, 6);
    }

    /**
     * 获取响应时间文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getResponseTimeTextAttr($value, array $data): string
    {
        $time = $data['response_time'] ?? 0;
        if ($time < 1000) {
            return $time . 'ms';
        } else {
            return round($time / 1000, 2) . 's';
        }
    }

    /**
     * 关联AI模型
     * @return \think\model\relation\BelongsTo
     */
    public function aiModel()
    {
        return $this->belongsTo(AiModel::class, 'ai_model_id', 'id');
    }

    /**
     * 记录AI使用日志
     * @param array $data
     * @return bool
     */
    public static function logUsage(array $data): bool
    {
        $logData = [
            'user_id' => $data['user_id'] ?? 0,
            'ai_model_id' => $data['ai_model_id'] ?? 0,
            'task_type' => $data['task_type'] ?? 'other',
            'task_id' => $data['task_id'] ?? null,
            'prompt_tokens' => $data['prompt_tokens'] ?? 0,
            'completion_tokens' => $data['completion_tokens'] ?? 0,
            'total_tokens' => $data['total_tokens'] ?? 0,
            'cost' => $data['cost'] ?? 0.0,
            'response_time' => $data['response_time'] ?? 0,
            'status' => $data['status'] ?? 'success',
            'error_message' => $data['error_message'] ?? null,
            'create_time' => date('Y-m-d H:i:s'),
        ];

        return static::mk()->save($logData) > 0;
    }

    /**
     * 获取用户使用统计
     * @param int $userId
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getUserStats(int $userId, string $startDate = '', string $endDate = ''): array
    {
        $query = static::mk()->where(['user_id' => $userId]);
        
        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate);
        }
        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate);
        }
        
        $logs = $query->select();
        
        return [
            'total_requests' => $logs->count(),
            'success_requests' => $logs->where('status', 'success')->count(),
            'failed_requests' => $logs->where('status', 'failed')->count(),
            'total_tokens' => $logs->sum('total_tokens'),
            'total_cost' => $logs->sum('cost'),
            'avg_response_time' => $logs->avg('response_time'),
        ];
    }

    /**
     * 获取系统使用统计
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getSystemStats(string $startDate = '', string $endDate = ''): array
    {
        $query = static::mk();
        
        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate);
        }
        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate);
        }
        
        $logs = $query->select();
        
        // 按任务类型统计
        $taskTypeStats = [];
        foreach ($logs as $log) {
            $taskType = $log['task_type'];
            if (!isset($taskTypeStats[$taskType])) {
                $taskTypeStats[$taskType] = [
                    'count' => 0,
                    'tokens' => 0,
                    'cost' => 0,
                ];
            }
            $taskTypeStats[$taskType]['count']++;
            $taskTypeStats[$taskType]['tokens'] += $log['total_tokens'];
            $taskTypeStats[$taskType]['cost'] += $log['cost'];
        }

        // 按AI模型统计
        $modelStats = [];
        foreach ($logs as $log) {
            $modelId = $log['ai_model_id'];
            if (!isset($modelStats[$modelId])) {
                $modelStats[$modelId] = [
                    'count' => 0,
                    'tokens' => 0,
                    'cost' => 0,
                ];
            }
            $modelStats[$modelId]['count']++;
            $modelStats[$modelId]['tokens'] += $log['total_tokens'];
            $modelStats[$modelId]['cost'] += $log['cost'];
        }
        
        return [
            'total_requests' => $logs->count(),
            'success_requests' => $logs->where('status', 'success')->count(),
            'failed_requests' => $logs->where('status', 'failed')->count(),
            'total_tokens' => $logs->sum('total_tokens'),
            'total_cost' => $logs->sum('cost'),
            'avg_response_time' => $logs->avg('response_time'),
            'task_type_stats' => $taskTypeStats,
            'model_stats' => $modelStats,
        ];
    }
}
