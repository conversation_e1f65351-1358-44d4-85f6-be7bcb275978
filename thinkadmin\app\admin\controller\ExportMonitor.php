<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\User;
use app\admin\model\ExportTask;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 导出任务监控
 * @class ExportMonitor
 * @package app\admin\controller
 */
class ExportMonitor extends Controller
{
    /**
     * 导出任务监控
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        ExportTask::mQuery()->layTable(function () {
            $this->title = '导出任务监控';
            $this->statusOptions = ExportTask::getStatusOptions();
            $this->typeOptions = ExportTask::getTypeOptions();
        }, static function (QueryHelper $query) {
            $query->with(['user']);
            $query->like('task_name,file_name,user.username,user.nickname')->equal('status,export_type');
            $query->dateBetween('create_time,start_time,complete_time');
            $query->order('id desc');
        });
    }

    /**
     * 任务详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = ExportTask::mk()->with(['user'])->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }

        // 获取任务进度详情
        $progressDetails = $this->getTaskProgressDetails($task);
        
        // 获取任务日志
        $taskLogs = $this->getTaskLogs($task->id);

        $this->assign('task', $task);
        $this->assign('progressDetails', $progressDetails);
        $this->assign('taskLogs', $taskLogs);
        $this->assign('title', '任务详情');
        
        return $this->fetch('export_monitor/view');
    }

    /**
     * 重新执行任务
     * @auth true
     */
    public function retry()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = ExportTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }

        if (!in_array($task->status, ['failed', 'cancelled'])) {
            $this->error('只能重新执行失败或已取消的任务！');
        }

        // 重置任务状态
        $updateData = [
            'status' => 'pending',
            'progress' => 0,
            'error_message' => '',
            'retry_count' => ($task->retry_count ?: 0) + 1,
            'start_time' => null,
            'complete_time' => null,
            'update_time' => date('Y-m-d H:i:s')
        ];

        if ($task->save($updateData)) {
            // 这里应该触发任务重新执行的逻辑
            // 比如发送到队列系统
            $this->addTaskLog($task->id, 'info', '任务已重新提交执行');
            
            $this->success('任务重新执行成功！');
        } else {
            $this->error('任务重新执行失败！');
        }
    }

    /**
     * 取消任务
     * @auth true
     */
    public function cancel()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = ExportTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }

        if (!in_array($task->status, ['pending', 'processing'])) {
            $this->error('只能取消等待中或处理中的任务！');
        }

        $updateData = [
            'status' => 'cancelled',
            'complete_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        if ($task->save($updateData)) {
            $this->addTaskLog($task->id, 'warning', '任务已被管理员取消');
            $this->success('任务取消成功！');
        } else {
            $this->error('任务取消失败！');
        }
    }

    /**
     * 删除任务
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }

        $task = ExportTask::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }

        if (in_array($task->status, ['processing'])) {
            $this->error('正在处理中的任务不能删除！');
        }

        // 删除相关文件
        if (!empty($task->file_path) && file_exists($task->file_path)) {
            @unlink($task->file_path);
        }

        if ($task->delete()) {
            $this->success('任务删除成功！');
        } else {
            $this->error('任务删除失败！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $task = ExportTask::mk()->findOrEmpty($id);
            if ($task->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'retry':
                    if (in_array($task->status, ['failed', 'cancelled'])) {
                        $result = $task->save([
                            'status' => 'pending',
                            'progress' => 0,
                            'error_message' => '',
                            'retry_count' => ($task->retry_count ?: 0) + 1,
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                        if ($result) {
                            $this->addTaskLog($task->id, 'info', '任务已批量重新提交');
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'cancel':
                    if (in_array($task->status, ['pending', 'processing'])) {
                        $result = $task->save([
                            'status' => 'cancelled',
                            'complete_time' => date('Y-m-d H:i:s'),
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                        if ($result) {
                            $this->addTaskLog($task->id, 'warning', '任务已批量取消');
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                case 'delete':
                    if (!in_array($task->status, ['processing'])) {
                        // 删除文件
                        if (!empty($task->file_path) && file_exists($task->file_path)) {
                            @unlink($task->file_path);
                        }
                        if ($task->delete()) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                    } else {
                        $failCount++;
                    }
                    break;

                default:
                    $failCount++;
                    break;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 任务统计
     * @auth true
     */
    public function statistics()
    {
        // 总任务数
        $totalTasks = ExportTask::mk()->count();
        
        // 今日任务数
        $todayTasks = ExportTask::mk()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月任务数
        $monthTasks = ExportTask::mk()
            ->whereTime('create_time', 'month')
            ->count();
        
        // 各状态任务数
        $statusStats = ExportTask::mk()
            ->field('status, COUNT(*) as count')
            ->group('status')
            ->select()
            ->toArray();
        
        // 各类型任务数
        $typeStats = ExportTask::mk()
            ->field('export_type, COUNT(*) as count')
            ->group('export_type')
            ->select()
            ->toArray();
        
        // 成功率统计
        $completedTasks = ExportTask::mk()->where('status', 'completed')->count();
        $successRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;
        
        // 平均处理时间
        $avgProcessTime = ExportTask::mk()
            ->where('status', 'completed')
            ->whereNotNull('start_time')
            ->whereNotNull('complete_time')
            ->avg('TIMESTAMPDIFF(SECOND, start_time, complete_time)') ?: 0;
        
        // 最近7天任务趋势
        $weekTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $count = ExportTask::mk()
                ->whereTime('create_time', $date)
                ->count();
            $completed = ExportTask::mk()
                ->whereTime('create_time', $date)
                ->where('status', 'completed')
                ->count();
            
            $weekTrend[] = [
                'date' => $date,
                'total' => $count,
                'completed' => $completed
            ];
        }
        
        // 用户任务排行（前10）
        $userStats = ExportTask::mk()
            ->with(['user'])
            ->field('user_id, COUNT(*) as task_count')
            ->where('user_id', '>', 0)
            ->group('user_id')
            ->order('task_count desc')
            ->limit(10)
            ->select()
            ->toArray();

        $statistics = [
            'total_tasks' => $totalTasks,
            'today_tasks' => $todayTasks,
            'month_tasks' => $monthTasks,
            'success_rate' => $successRate,
            'avg_process_time' => round($avgProcessTime),
            'status_stats' => $statusStats,
            'type_stats' => $typeStats,
            'week_trend' => $weekTrend,
            'user_stats' => $userStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '任务统计');
        return $this->fetch('export_monitor/statistics');
    }

    /**
     * 实时监控
     * @auth true
     */
    public function realtime()
    {
        // 获取正在处理的任务
        $processingTasks = ExportTask::mk()
            ->with(['user'])
            ->where('status', 'processing')
            ->order('start_time asc')
            ->select();
        
        // 获取等待中的任务
        $pendingTasks = ExportTask::mk()
            ->with(['user'])
            ->where('status', 'pending')
            ->order('create_time asc')
            ->limit(20)
            ->select();
        
        // 系统资源使用情况（模拟数据）
        $systemStats = [
            'cpu_usage' => rand(20, 80),
            'memory_usage' => rand(30, 70),
            'disk_usage' => rand(40, 85),
            'queue_length' => $pendingTasks->count(),
            'active_workers' => rand(2, 8)
        ];

        if ($this->request->isAjax()) {
            return json([
                'processing_tasks' => $processingTasks->toArray(),
                'pending_tasks' => $pendingTasks->toArray(),
                'system_stats' => $systemStats
            ]);
        }

        $this->assign('processingTasks', $processingTasks);
        $this->assign('pendingTasks', $pendingTasks);
        $this->assign('systemStats', $systemStats);
        $this->assign('title', '实时监控');
        
        return $this->fetch('export_monitor/realtime');
    }

    /**
     * 获取任务进度详情
     * @param ExportTask $task
     * @return array
     */
    private function getTaskProgressDetails(ExportTask $task): array
    {
        $steps = [
            'init' => ['name' => '初始化', 'status' => 'completed'],
            'prepare' => ['name' => '准备数据', 'status' => 'completed'],
            'process' => ['name' => '处理数据', 'status' => 'processing'],
            'generate' => ['name' => '生成文件', 'status' => 'pending'],
            'upload' => ['name' => '上传文件', 'status' => 'pending'],
            'complete' => ['name' => '完成', 'status' => 'pending']
        ];

        // 根据任务状态和进度调整步骤状态
        if ($task->status == 'completed') {
            foreach ($steps as &$step) {
                $step['status'] = 'completed';
            }
        } elseif ($task->status == 'failed') {
            $currentStep = array_keys($steps)[floor($task->progress / 20)];
            foreach ($steps as $key => &$step) {
                if ($key == $currentStep) {
                    $step['status'] = 'failed';
                    break;
                } elseif ($step['status'] == 'pending') {
                    break;
                } else {
                    $step['status'] = 'completed';
                }
            }
        }

        return $steps;
    }

    /**
     * 获取任务日志
     * @param int $taskId
     * @return array
     */
    private function getTaskLogs(int $taskId): array
    {
        // 这里应该从实际的日志表中获取数据
        // 暂时返回模拟数据
        return [
            [
                'time' => date('Y-m-d H:i:s', strtotime('-10 minutes')),
                'level' => 'info',
                'message' => '任务开始执行'
            ],
            [
                'time' => date('Y-m-d H:i:s', strtotime('-8 minutes')),
                'level' => 'info',
                'message' => '数据准备完成'
            ],
            [
                'time' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                'level' => 'info',
                'message' => '开始处理数据'
            ],
            [
                'time' => date('Y-m-d H:i:s', strtotime('-2 minutes')),
                'level' => 'warning',
                'message' => '处理过程中遇到警告'
            ]
        ];
    }

    /**
     * 添加任务日志
     * @param int $taskId
     * @param string $level
     * @param string $message
     */
    private function addTaskLog(int $taskId, string $level, string $message): void
    {
        // 这里应该写入到实际的日志表
        // 暂时只是模拟
        $logData = [
            'task_id' => $taskId,
            'level' => $level,
            'message' => $message,
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 实际项目中应该保存到数据库
        // TaskLog::mk()->save($logData);
    }

    /**
     * 清理过期任务
     * @auth true
     */
    public function cleanup()
    {
        $days = $this->request->post('days', 30);
        
        if ($days < 1) {
            $this->error('清理天数必须大于0！');
        }

        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // 获取要清理的任务
        $expiredTasks = ExportTask::mk()
            ->where('status', 'in', ['completed', 'failed', 'cancelled'])
            ->where('create_time', '<', $expireDate)
            ->select();
        
        $cleanupCount = 0;
        foreach ($expiredTasks as $task) {
            // 删除文件
            if (!empty($task->file_path) && file_exists($task->file_path)) {
                @unlink($task->file_path);
            }
            
            // 删除任务记录
            if ($task->delete()) {
                $cleanupCount++;
            }
        }

        $this->success("清理完成！共清理了 {$cleanupCount} 个过期任务。");
    }

    /**
     * 导出任务列表
     * @auth true
     */
    public function export()
    {
        $startDate = $this->request->post('start_date', '');
        $endDate = $this->request->post('end_date', '');
        $status = $this->request->post('status', '');

        $query = ExportTask::mk()->with(['user']);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        $tasks = $query->order('id desc')->select();

        if ($tasks->isEmpty()) {
            $this->error('没有找到要导出的任务！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '任务名称', '用户', '导出类型', '文件名', '状态', '进度', '创建时间', '完成时间'];
        
        foreach ($tasks as $task) {
            $exportData[] = [
                $task->id,
                $task->task_name,
                $task->user->username ?? '',
                ExportTask::getTypeOptions()[$task->export_type] ?? $task->export_type,
                $task->file_name,
                ExportTask::getStatusOptions()[$task->status] ?? $task->status,
                $task->progress . '%',
                $task->create_time,
                $task->complete_time ?: ''
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
}
