<?php

/**
 * 检查paper_project表字段
 */

echo "=== 检查paper_project表字段 ===\n\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在\n";
        exit(1);
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
    
    // 检查paper_project表结构
    echo "1. paper_project表字段:\n";
    $stmt = $pdo->query("PRAGMA table_info(paper_project)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "  - {$column['name']} ({$column['type']})";
        if ($column['notnull']) echo " NOT NULL";
        if ($column['dflt_value'] !== null) echo " DEFAULT {$column['dflt_value']}";
        echo "\n";
    }
    
    // 检查实际数据
    echo "\n2. 检查实际数据:\n";
    $stmt = $pdo->query("SELECT * FROM paper_project LIMIT 1");
    $sample = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sample) {
        echo "  📊 样本数据字段:\n";
        foreach ($sample as $field => $value) {
            $displayValue = is_null($value) ? 'NULL' : (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
            echo "    {$field}: {$displayValue}\n";
        }
    } else {
        echo "  ⚠️  表中没有数据\n";
    }
    
    // 检查模板中使用的字段
    echo "\n3. 模板中使用的字段检查:\n";
    $templateFields = [
        'id', 'title', 'paper_type_id', 'draft_version', 'subject', 
        'keywords', 'word_count', 'create_time', 'update_time'
    ];
    
    $actualFields = array_column($columns, 'name');
    
    foreach ($templateFields as $field) {
        if (in_array($field, $actualFields)) {
            echo "  ✅ {$field} - 字段存在\n";
        } else {
            echo "  ❌ {$field} - 字段不存在\n";
            
            // 查找相似字段
            $similar = [];
            foreach ($actualFields as $actualField) {
                if (strpos($actualField, $field) !== false || strpos($field, $actualField) !== false) {
                    $similar[] = $actualField;
                }
            }
            
            if (!empty($similar)) {
                echo "    💡 相似字段: " . implode(', ', $similar) . "\n";
            }
        }
    }
    
    // 检查草稿相关数据
    echo "\n4. 草稿数据检查:\n";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM paper_project WHERE is_draft = 1");
        $draftCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 草稿数量: {$draftCount}\n";
        
        if ($draftCount > 0) {
            $stmt = $pdo->query("SELECT id, title, create_time, update_time FROM paper_project WHERE is_draft = 1 LIMIT 3");
            $drafts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "  📋 草稿样本:\n";
            foreach ($drafts as $draft) {
                echo "    ID: {$draft['id']}, 标题: {$draft['title']}, 创建: {$draft['create_time']}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "  ❌ 草稿查询失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
