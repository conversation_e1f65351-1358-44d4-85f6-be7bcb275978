{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">正文模板{if isset($vo.id) && $vo.id}编辑{else}添加{/if}</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="{:request()->url()}" data-auto="true" method="post" autocomplete="off">
            
            {if isset($vo['id'])}<input type='hidden' value='{$vo.id}' name='id'>{/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <input name="name" value="{$vo.name|default=''}" required lay-verify="required" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-block">
                    <select name="paper_type_id">
                        <option value="0">通用（适用所有类型）</option>
                        {foreach $paperTypes as $id=>$name}
                        <option value="{$id}" {if isset($vo['paper_type_id']) && $vo['paper_type_id'] eq $id}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板内容</label>
                <div class="layui-input-block">
                    <textarea name="template_content" required lay-verify="required" placeholder="请输入正文模板内容，支持HTML格式和变量替换" class="layui-textarea" style="min-height: 300px;">{$vo.template_content|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        支持HTML格式和变量替换，常用变量：{{title}}（标题）、{{content}}（内容）、{{author}}（作者）、{{date}}（日期）
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">样式配置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">字体</label>
                            <div class="layui-input-block">
                                <select name="style_config[font_family]">
                                    {foreach $styleConfigOptions.font_family as $key=>$name}
                                    <option value="{$key}" {if isset($vo['style_config']['font_family']) && $vo['style_config']['font_family'] eq $key}selected{/if}>{$name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">字号</label>
                            <div class="layui-input-block">
                                <select name="style_config[font_size]">
                                    {foreach $styleConfigOptions.font_size as $key=>$name}
                                    <option value="{$key}" {if isset($vo['style_config']['font_size']) && $vo['style_config']['font_size'] eq $key}selected{/if}>{$name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">行距</label>
                            <div class="layui-input-block">
                                <select name="style_config[line_spacing]">
                                    {foreach $styleConfigOptions.line_spacing as $key=>$name}
                                    <option value="{$key}" {if isset($vo['style_config']['line_spacing']) && $vo['style_config']['line_spacing'] eq $key}selected{/if}>{$name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">页边距设置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <label class="layui-form-label">上边距(cm)</label>
                            <div class="layui-input-block">
                                <input name="style_config[margin_top]" value="{$vo.style_config.margin_top|default=2.5}" type="number" step="0.1" placeholder="2.5" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">下边距(cm)</label>
                            <div class="layui-input-block">
                                <input name="style_config[margin_bottom]" value="{$vo.style_config.margin_bottom|default=2.5}" type="number" step="0.1" placeholder="2.5" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">左边距(cm)</label>
                            <div class="layui-input-block">
                                <input name="style_config[margin_left]" value="{$vo.style_config.margin_left|default=3}" type="number" step="0.1" placeholder="3" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">右边距(cm)</label>
                            <div class="layui-input-block">
                                <input name="style_config[margin_right]" value="{$vo.style_config.margin_right|default=3}" type="number" step="0.1" placeholder="3" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">段落设置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <label class="layui-form-label">首行缩进(字符)</label>
                            <div class="layui-input-block">
                                <input name="style_config[text_indent]" value="{$vo.style_config.text_indent|default=2}" type="number" min="0" placeholder="2" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <label class="layui-form-label">段前距(磅)</label>
                            <div class="layui-input-block">
                                <input name="style_config[paragraph_spacing]" value="{$vo.style_config.paragraph_spacing|default=0}" type="number" min="0" placeholder="0" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">描述说明</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入模板描述说明" class="layui-textarea">{$vo.description|default=''}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_default" value="1" title="是" {if isset($vo['is_default']) && $vo['is_default'] eq 1}checked{/if}>
                    <input type="radio" name="is_default" value="0" title="否" {if !isset($vo['is_default']) || $vo['is_default'] eq 0}checked{/if}>
                    <div class="layui-form-mid layui-word-aux">
                        设为默认后，同论文类型的其他模板将自动取消默认状态
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset($vo['status']) || $vo['status'] eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset($vo['status']) && $vo['status'] eq 0}checked{/if}>
                </div>
            </div>
            
            <div class="hr-line-dashed"></div>
            
            <div class="layui-form-item text-center">
                <button class="layui-btn" type='submit'>保存数据</button>
                <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
            </div>
            
        </form>
    </div>
</div>
{/block}
