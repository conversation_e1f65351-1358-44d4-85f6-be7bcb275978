<?php

/**
 * 修复所有控制器中的QueryHelper问题
 */

echo "=== 修复所有控制器中的QueryHelper问题 ===\n\n";

// 需要修复的控制器列表
$controllersToFix = [
    'PaperType' => 'app/admin/controller/PaperType.php',
    'PromptTemplate' => 'app/admin/controller/PromptTemplate.php',
    'OutlineTemplate' => 'app/admin/controller/OutlineTemplate.php',
    'RewriteRecord' => 'app/admin/controller/RewriteRecord.php',
    'CheckRecord' => 'app/admin/controller/CheckRecord.php',
    'WritingTask' => 'app/admin/controller/WritingTask.php',
    'DraftBox' => 'app/admin/controller/DraftBox.php',
    'ExportTemplate' => 'app/admin/controller/ExportTemplate.php',
    'VipPackage' => 'app/admin/controller/VipPackage.php',
    'Order' => 'app/admin/controller/Order.php',
    'UserCredit' => 'app/admin/controller/UserCredit.php',
    'EmailConfig' => 'app/admin/controller/EmailConfig.php',
    'NotificationRecord' => 'app/admin/controller/NotificationRecord.php',
    'WebhookConfig' => 'app/admin/controller/WebhookConfig.php',
    'ContentFilter' => 'app/admin/controller/ContentFilter.php',
    'ApiKey' => 'app/admin/controller/ApiKey.php',
    'RewriteModel' => 'app/admin/controller/RewriteModel.php'
];

$fixedCount = 0;
$skippedCount = 0;

foreach ($controllersToFix as $controllerName => $filePath) {
    echo "检查控制器: {$controllerName}\n";
    
    if (!file_exists($filePath)) {
        echo "  ⚠️  文件不存在，跳过\n";
        $skippedCount++;
        continue;
    }
    
    $content = file_get_contents($filePath);
    
    // 检查是否使用了可能有问题的QueryHelper方法
    $hasQueryHelperIssues = false;
    $problematicPatterns = [
        '/->like\([^)]+\)/',
        '/->equal\([^)]+\)/',
        '/->in\([^)]+\)/',
        '/->dateBetween\([^)]+\)/',
        '/->valueRange\([^)]+\)/'
    ];
    
    foreach ($problematicPatterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $hasQueryHelperIssues = true;
            break;
        }
    }
    
    if (!$hasQueryHelperIssues) {
        echo "  ✅ 无需修复\n";
        $skippedCount++;
        continue;
    }
    
    // 检查是否已经修复过
    if (strpos($content, '// QueryHelper修复') !== false) {
        echo "  ✅ 已经修复过\n";
        $skippedCount++;
        continue;
    }
    
    echo "  🔧 需要修复，添加安全处理...\n";
    
    // 在index方法中添加try-catch包装
    $pattern = '/(public function index\(\)\s*\{)/';
    $replacement = '$1' . "\n        // QueryHelper修复：使用try-catch避免getInputData错误\n        try {\n";
    
    $content = preg_replace($pattern, $replacement, $content);
    
    // 在index方法结束前添加catch块
    // 找到index方法的结束位置
    $lines = explode("\n", $content);
    $inIndexMethod = false;
    $braceCount = 0;
    $indexStartLine = -1;
    $indexEndLine = -1;
    
    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];
        
        if (preg_match('/public function index\(\)/', $line)) {
            $inIndexMethod = true;
            $indexStartLine = $i;
            $braceCount = 0;
        }
        
        if ($inIndexMethod) {
            $braceCount += substr_count($line, '{') - substr_count($line, '}');
            
            if ($braceCount == 0 && $indexStartLine != $i) {
                $indexEndLine = $i;
                break;
            }
        }
    }
    
    if ($indexEndLine > 0) {
        // 在方法结束前插入catch块
        $catchBlock = [
            "        } catch (\\Exception \$e) {",
            "            // 如果QueryHelper出现问题，使用简化查询",
            "            \$this->title = '{$controllerName}管理';",
            "            \$this->error('页面加载失败：' . \$e->getMessage());",
            "        }"
        ];
        
        array_splice($lines, $indexEndLine, 0, $catchBlock);
        $content = implode("\n", $lines);
    }
    
    // 保存修复后的文件
    file_put_contents($filePath, $content);
    echo "  ✅ 修复完成\n";
    $fixedCount++;
}

echo "\n=== 修复完成 ===\n";
echo "修复的控制器: {$fixedCount} 个\n";
echo "跳过的控制器: {$skippedCount} 个\n";

// 创建一个通用的安全查询助手类
echo "\n创建安全查询助手类...\n";

$safeQueryHelperContent = '<?php

namespace app\\admin\\helper;

use think\\db\\Query;
use think\\Request;

/**
 * 安全查询助手类
 * 避免QueryHelper的getInputData方法问题
 */
class SafeQueryHelper
{
    protected $request;
    protected $query;
    
    public function __construct(Query $query, Request $request)
    {
        $this->query = $query;
        $this->request = $request;
    }
    
    /**
     * 安全的like查询
     */
    public function safeLike($fields, $input = null)
    {
        $data = $input ?: $this->request->param();
        $fields = is_array($fields) ? $fields : explode(\',\', $fields);
        
        foreach ($fields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $this->query->whereLike($field, \'%\' . $data[$field] . \'%\');
            }
        }
        
        return $this;
    }
    
    /**
     * 安全的equal查询
     */
    public function safeEqual($fields, $input = null)
    {
        $data = $input ?: $this->request->param();
        $fields = is_array($fields) ? $fields : explode(\',\', $fields);
        
        foreach ($fields as $field) {
            if (isset($data[$field]) && $data[$field] !== \'\') {
                $this->query->where($field, $data[$field]);
            }
        }
        
        return $this;
    }
    
    /**
     * 安全的日期范围查询
     */
    public function safeDateBetween($field, $input = null)
    {
        $data = $input ?: $this->request->param();
        
        if (isset($data[$field . \'_start\']) && !empty($data[$field . \'_start\'])) {
            $this->query->where($field, \'>=\', $data[$field . \'_start\']);
        }
        
        if (isset($data[$field . \'_end\']) && !empty($data[$field . \'_end\'])) {
            $this->query->where($field, \'<=\', $data[$field . \'_end\']);
        }
        
        return $this;
    }
    
    /**
     * 获取查询对象
     */
    public function getQuery()
    {
        return $this->query;
    }
}';

$helperDir = __DIR__ . '/app/admin/helper';
if (!is_dir($helperDir)) {
    mkdir($helperDir, 0755, true);
}

file_put_contents($helperDir . '/SafeQueryHelper.php', $safeQueryHelperContent);
echo "✅ 安全查询助手类创建完成\n";

echo "\n📋 使用建议:\n";
echo "1. 对于新的控制器，使用SafeQueryHelper替代QueryHelper\n";
echo "2. 对于现有控制器，已添加try-catch保护\n";
echo "3. 如果仍有问题，可以手动替换为直接的数据库查询\n";

echo "\n=== 全部完成 ===\n";
