<?php

/**
 * 测试简化版正文模板管理
 */

echo "=== 测试简化版正文模板管理 ===\n\n";

// 1. 检查简化控制器语法
echo "1. 检查简化控制器语法:\n";
$output = [];
$returnCode = 0;
exec("php -l app/admin/controller/ContentTemplateSimple.php 2>&1", $output, $returnCode);

if ($returnCode === 0) {
    echo "  ✅ ContentTemplateSimple.php - 语法正确\n";
} else {
    echo "  ❌ ContentTemplateSimple.php - 语法错误: " . implode(' ', $output) . "\n";
    exit(1);
}

// 2. 检查是否完全避免了ThinkAdmin方法
echo "\n2. 检查是否完全避免了ThinkAdmin方法:\n";
$content = file_get_contents('app/admin/controller/ContentTemplateSimple.php');

$thinkAdminMethods = ['::mQuery', '::mForm', '::mSave', '::mDelete', 'QueryHelper'];
$foundThinkAdmin = false;

foreach ($thinkAdminMethods as $method) {
    if (strpos($content, $method) !== false) {
        echo "  ❌ 仍在使用ThinkAdmin方法: {$method}\n";
        $foundThinkAdmin = true;
    }
}

if (!$foundThinkAdmin) {
    echo "  ✅ 完全避免了所有ThinkAdmin特有方法\n";
}

// 检查继承的基类
if (strpos($content, 'extends Controller') !== false) {
    echo "  ✅ 继承标准ThinkPHP Controller\n";
} else if (strpos($content, 'extends think\\admin\\Controller') !== false) {
    echo "  ⚠️  继承ThinkAdmin Controller，可能仍有风险\n";
} else {
    echo "  ❓ 继承其他基类\n";
}

// 3. 数据库连接测试
echo "\n3. 数据库连接测试:\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "  ✅ 数据库连接成功\n";
        
        // 测试查询逻辑
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_template WHERE type = 'content'");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 正文模板数量: {$count}\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM paper_type WHERE status = 1");
        $typeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ 启用的论文类型: {$typeCount}\n";
        
    } else {
        echo "  ❌ 数据库文件不存在\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 4. 模拟控制器执行
echo "\n4. 模拟控制器执行:\n";

// 创建模拟测试
$testScript = '<?php
// 模拟基本环境
define("ROOT_PATH", __DIR__ . "/");

// 模拟简化控制器的核心逻辑
try {
    $dbPath = __DIR__ . "/database/sqlite.db";
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 模拟查询逻辑
        $map = ["type" => "content"];
        
        // 构建SQL
        $sql = "SELECT * FROM document_template WHERE type = ? ORDER BY is_default DESC, usage_count DESC, id DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(["content"]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ 简化控制器查询测试成功\\n";
        echo "✅ 返回记录数: " . count($results) . "\\n";
        
        // 模拟分页逻辑
        $total = count($results);
        $pageSize = 20;
        $totalPages = ceil($total / $pageSize);
        
        echo "✅ 分页信息: 总记录 {$total}，每页 {$pageSize}，共 {$totalPages} 页\\n";
        
        // 模拟论文类型查询
        $stmt = $pdo->query("SELECT id, name FROM paper_type WHERE status = 1");
        $paperTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ 论文类型查询成功，共 " . count($paperTypes) . " 个类型\\n";
        
    } else {
        echo "❌ 数据库文件不存在\\n";
    }
    
} catch (Exception $e) {
    echo "❌ 模拟执行失败: " . $e->getMessage() . "\\n";
}
';

file_put_contents('temp_simple_test.php', $testScript);

$output = [];
$returnCode = 0;
exec("php temp_simple_test.php 2>&1", $output, $returnCode);

echo "  " . implode("\n  ", $output) . "\n";

// 清理临时文件
if (file_exists('temp_simple_test.php')) {
    unlink('temp_simple_test.php');
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "🎯 简化版本特点:\n";
echo "✅ 1. 完全避免ThinkAdmin的所有辅助方法\n";
echo "✅ 2. 继承标准ThinkPHP Controller而非ThinkAdmin Controller\n";
echo "✅ 3. 自己渲染HTML，不依赖ThinkAdmin模板系统\n";
echo "✅ 4. 直接使用ThinkPHP的数据库查询和分页\n";
echo "✅ 5. 包含完整的异常处理机制\n";

echo "\n💡 使用方法:\n";
echo "1. 访问 /admin/content_template_simple/index 来测试简化版本\n";
echo "2. 如果简化版本正常工作，说明问题确实在ThinkAdmin的QueryHelper\n";
echo "3. 可以逐步将简化版本的方法应用到原控制器中\n";

echo "\n🔧 如果简化版本仍有问题:\n";
echo "1. 问题可能在ThinkPHP框架本身\n";
echo "2. 检查PHP版本和扩展兼容性\n";
echo "3. 检查数据库连接配置\n";
echo "4. 查看服务器错误日志\n";

echo "\n=== 测试完成 ===\n";
