<?php

declare (strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemConfig;

/**
 * 内容风控规则
 * @class ContentFilter
 * @package app\admin\controller
 */
class ContentFilter extends Controller
{
    /**
     * 内容风控规则
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        ContentFilterModel::mQuery()->layTable(function () {
            $this->title = '内容过滤管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示内容风控相关的配置
            $query->where('type', 'like', 'filter_%');
            $query->like('name,title,description')->equal('type,status');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加风控规则
     * @auth true
     */public function add()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 编辑风控规则
     * @auth true
     */
    public function edit()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = $this->getFilterTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            $this->levelOptions = $this->getRiskLevels();
            $this->actionOptions = $this->getFilterActions();
            
            // 如果是编辑，解析配置值
            if (!empty($vo['id']) && !empty($vo['value'])) {
                $config = json_decode($vo['value'], true);
                if (is_array($config)) {
                    $vo = array_merge($vo, $config);
                    // 处理数组字段
                    if (isset($config['keywords']) && is_array($config['keywords'])) {
                        $vo['keywords_text'] = implode("\n", $config['keywords']);
                    }
                    if (isset($config['whitelist']) && is_array($config['whitelist'])) {
                        $vo['whitelist_text'] = implode("\n", $config['whitelist']);
                    }
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('规则名称不能为空！');
            }
            
            if (empty($vo['title'])) {
                $this->error('规则标题不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择规则类型！');
            }
            
            // 确保type以filter_开头
            if (strpos($vo['type'], 'filter_') !== 0) {
                $vo['type'] = 'filter_' . $vo['type'];
            }
            
            // 构建配置值
            $configValue = [];
            
            // 基础配置
            $configValue['risk_level'] = $vo['risk_level'] ?? 'medium';
            $configValue['action'] = $vo['action'] ?? 'block';
            $configValue['threshold'] = floatval($vo['threshold'] ?? 0.8);
            $configValue['enabled_scenes'] = $vo['enabled_scenes'] ?? [];
            
            // 根据不同规则类型处理
            switch ($vo['type']) {
                case 'filter_keyword':
                    // 关键词过滤
                    $keywords = [];
                    if (!empty($vo['keywords_text'])) {
                        $keywords = array_filter(explode("\n", $vo['keywords_text']));
                        $keywords = array_map('trim', $keywords);
                    }
                    $configValue['keywords'] = $keywords;
                    $configValue['match_mode'] = $vo['match_mode'] ?? 'exact'; // exact, fuzzy, regex
                    $configValue['case_sensitive'] = intval($vo['case_sensitive'] ?? 0);
                    break;
                    
                case 'filter_sensitive':
                    // 敏感内容检测
                    $configValue['api_provider'] = $vo['api_provider'] ?? 'baidu';
                    $configValue['api_key'] = $vo['api_key'] ?? '';
                    $configValue['api_secret'] = $vo['api_secret'] ?? '';
                    $configValue['check_types'] = $vo['check_types'] ?? ['political', 'porn', 'ad'];
                    break;
                    
                case 'filter_length':
                    // 内容长度限制
                    $configValue['min_length'] = intval($vo['min_length'] ?? 0);
                    $configValue['max_length'] = intval($vo['max_length'] ?? 10000);
                    $configValue['count_type'] = $vo['count_type'] ?? 'char'; // char, word
                    break;
                    
                case 'filter_format':
                    // 格式检查
                    $configValue['allowed_formats'] = $vo['allowed_formats'] ?? ['txt', 'doc', 'docx', 'pdf'];
                    $configValue['max_file_size'] = intval($vo['max_file_size'] ?? 10485760); // 10MB
                    $configValue['check_encoding'] = intval($vo['check_encoding'] ?? 1);
                    break;
                    
                case 'filter_frequency':
                    // 频率限制
                    $configValue['max_requests'] = intval($vo['max_requests'] ?? 100);
                    $configValue['time_window'] = intval($vo['time_window'] ?? 3600); // 1小时
                    $configValue['limit_type'] = $vo['limit_type'] ?? 'user'; // user, ip, global
                    break;
                    
                default:
                    $configValue['custom_rules'] = $vo['custom_rules'] ?? [];
                    break;
            }
            
            // 白名单配置
            $whitelist = [];
            if (!empty($vo['whitelist_text'])) {
                $whitelist = array_filter(explode("\n", $vo['whitelist_text']));
                $whitelist = array_map('trim', $whitelist);
            }
            $configValue['whitelist'] = $whitelist;
            
            // 验证配置
            if ($vo['type'] === 'filter_keyword' && empty($configValue['keywords'])) {
                $this->error('关键词列表不能为空！');
            }
            
            if ($vo['type'] === 'filter_sensitive' && empty($configValue['api_key'])) {
                $this->error('API密钥不能为空！');
            }
            
            if ($vo['type'] === 'filter_length') {
                if ($configValue['min_length'] < 0) {
                    $this->error('最小长度不能小于0！');
                }
                if ($configValue['max_length'] <= $configValue['min_length']) {
                    $this->error('最大长度必须大于最小长度！');
                }
            }
            
            $vo['value'] = json_encode($configValue, JSON_UNESCAPED_UNICODE);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('风控规则保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除风控规则
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('规则ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('规则不存在！');
        }

        if ($config->delete()) {
            $this->success('风控规则删除成功！');
        } else {
            $this->error('风控规则删除失败！');
        }
    }

    /**
     * 修改规则状态
     * @auth true
     */
    public function state()
    {
        SystemConfig::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 测试风控规则
     * @auth true
     */
    public function test()
    {
        $id = $this->request->post('id', 0);
        $testContent = $this->request->post('test_content', '');
        
        if (empty($id)) {
            $this->error('规则ID不能为空！');
        }

        if (empty($testContent)) {
            $this->error('测试内容不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('规则不存在！');
        }            $configValue = json_decode($config->value, true);
            if (!is_array($configValue)) {
                $this->error('规则配置格式错误！');
            }

            // 执行风控测试
            $testResult = $this->performContentFilter($config->type, $configValue, $testContent);
            
            if ($testResult['passed']) {
                $this->success('内容检测通过！风险等级：' . $testResult['risk_level']);
            } else {
                $this->error('内容检测未通过！原因：' . $testResult['reason'] . '，风险等级：' . $testResult['risk_level']);
            }}

    /**
     * 执行内容过滤
     * @param string $type
     * @param array $config
     * @param string $content
     * @return array
     */
    private function performContentFilter(string $type, array $config, string $content): array
    {            // 模拟检测过程
            usleep(rand(200000, 1000000)); // 200ms-1s
            
            $riskLevel = 'low';
            $passed = true;
            $reason = '';
            
            switch ($type) {
                case 'filter_keyword':
                    // 关键词检测
                    $keywords = $config['keywords'] ?? [];
                    foreach ($keywords as $keyword) {
                        if (stripos($content, $keyword) !== false) {
                            $passed = false;
                            $reason = "包含敏感关键词：{$keyword}";
                            $riskLevel = 'high';
                            break;
                        }
                    }
                    break;
                    
                case 'filter_sensitive':
                    // 敏感内容检测（模拟API调用）
                    $sensitiveWords = ['政治', '色情', '暴力', '赌博', '毒品'];
                    foreach ($sensitiveWords as $word) {
                        if (stripos($content, $word) !== false) {
                            $passed = false;
                            $reason = "包含敏感内容：{$word}";
                            $riskLevel = 'high';
                            break;
                        }
                    }
                    break;
                    
                case 'filter_length':
                    // 长度检测
                    $minLength = $config['min_length'] ?? 0;
                    $maxLength = $config['max_length'] ?? 10000;
                    $contentLength = mb_strlen($content);
                    
                    if ($contentLength < $minLength) {
                        $passed = false;
                        $reason = "内容长度不足，最少需要{$minLength}字符";
                        $riskLevel = 'medium';
                    } elseif ($contentLength > $maxLength) {
                        $passed = false;
                        $reason = "内容长度超限，最多允许{$maxLength}字符";
                        $riskLevel = 'medium';
                    }
                    break;
                    
                case 'filter_format':
                    // 格式检测
                    if (empty(trim($content))) {
                        $passed = false;
                        $reason = "内容为空";
                        $riskLevel = 'medium';
                    }
                    break;
                    
                case 'filter_frequency':
                    // 频率检测（模拟）
                    $randomCheck = rand(1, 10);
                    if ($randomCheck <= 2) { // 20%概率触发频率限制
                        $passed = false;
                        $reason = "请求频率过高，请稍后再试";
                        $riskLevel = 'medium';
                    }
                    break;
                    
                default:
                    // 默认通过
                    break;
            }
            
            // 检查白名单
            $whitelist = $config['whitelist'] ?? [];
            if (!$passed && !empty($whitelist)) {
                foreach ($whitelist as $whiteItem) {
                    if (stripos($content, $whiteItem) !== false) {
                        $passed = true;
                        $reason = "命中白名单：{$whiteItem}";
                        $riskLevel = 'low';
                        break;
                    }
                }
            }
            
            return [
                'passed' => $passed,
                'reason' => $reason,
                'risk_level' => $riskLevel,
                'confidence' => rand(70, 99) / 100
            ];}

    /**
     * 获取过滤器类型选项
     * @return array
     */
    private function getFilterTypes(): array
    {
        return [
            'filter_keyword' => '关键词过滤',
            'filter_sensitive' => '敏感内容检测',
            'filter_length' => '内容长度限制',
            'filter_format' => '格式检查',
            'filter_frequency' => '频率限制',
            'filter_custom' => '自定义规则'
        ];
    }

    /**
     * 获取风险等级选项
     * @return array
     */
    private function getRiskLevels(): array
    {
        return [
            'low' => '低风险',
            'medium' => '中风险',
            'high' => '高风险',
            'critical' => '严重风险'
        ];
    }

    /**
     * 获取过滤动作选项
     * @return array
     */
    private function getFilterActions(): array
    {
        return [
            'block' => '阻止',
            'warn' => '警告',
            'review' => '人工审核',
            'replace' => '替换',
            'log' => '仅记录'
        ];
    }

    /**
     * 复制风控规则
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('规则ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('规则不存在！');
        }

        // 创建副本
        $copyData = $config->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['title'] = $copyData['title'] . '_副本';
        $copyData['status'] = 0; // 默认禁用
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $result = SystemConfig::mk()->save($copyData);
        if ($result) {
            $this->success('风控规则复制成功！');
        } else {
            $this->error('风控规则复制失败！');
        }
    }

    /**
     * 风控统计
     * @auth true
     */
    public function statistics()
    {
        // 总规则数
        $totalRules = SystemConfig::mk()
            ->where('type', 'like', 'filter_%')
            ->count();
        
        // 启用的规则数
        $activeRules = SystemConfig::mk()
            ->where('type', 'like', 'filter_%')
            ->where('status', 1)
            ->count();
        
        // 各类型规则数量统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'filter_%')
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getFilterTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
        }
        
        // 今日新增规则
        $todayRules = SystemConfig::mk()
            ->where('type', 'like', 'filter_%')
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月新增规则
        $monthRules = SystemConfig::mk()
            ->where('type', 'like', 'filter_%')
            ->whereTime('create_time', 'month')
            ->count();
        
        // 模拟风控统计
        $filterStats = [
            'today_checks' => rand(500, 5000),
            'today_blocked' => rand(50, 500),
            'today_warned' => rand(20, 200),
            'month_checks' => rand(10000, 100000),
            'month_blocked' => rand(1000, 10000),
            'month_warned' => rand(500, 5000),
            'block_rate' => rand(5, 15) / 100,
            'false_positive_rate' => rand(1, 5) / 100
        ];

        $statistics = [
            'total_rules' => $totalRules,
            'active_rules' => $activeRules,
            'today_rules' => $todayRules,
            'month_rules' => $monthRules,
            'type_stats' => $typeStats,
            'filter_stats' => $filterStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '风控统计');
        return $this->fetch('content_filter/statistics');
    }

    /**
     * 风控日志
     * @auth true
     */
    public function logs()
    {
        // 模拟风控日志
        $logs = [];
        for ($i = 0; $i < 20; $i++) {
            $logs[] = [
                'id' => $i + 1,
                'rule_name' => 'filter_' . rand(1, 5),
                'content_type' => ['paper', 'comment', 'message'][rand(0, 2)],
                'user_id' => rand(1, 100),
                'action' => ['block', 'warn', 'review'][rand(0, 2)],
                'risk_level' => ['low', 'medium', 'high'][rand(0, 2)],
                'reason' => ['包含敏感关键词', '内容长度超限', '频率过高'][rand(0, 2)],
                'confidence' => rand(70, 99) / 100,
                'create_time' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 1440) . ' minutes'))
            ];
        }

        if ($this->request->isAjax()) {
            return json(['data' => $logs]);
        }

        $this->assign('logs', $logs);
        $this->assign('title', '风控日志');
        return $this->fetch('content_filter/logs');
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'enable':
                    $result = $config->save(['status' => 1]);
                    break;
                case 'disable':
                    $result = $config->save(['status' => 0]);
                    break;
                case 'delete':
                    $result = $config->delete();
                    break;
                default:
                    $result = false;
                    break;
            }

            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出风控规则
     * @auth true
     */
    public function export()
    {
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = SystemConfig::mk()->where('type', 'like', 'filter_%');

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $configs = $query->order('sort asc, id desc')->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要导出的规则！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '规则名称', '规则标题', '规则类型', '风险等级', '状态', '创建时间'];
        
        foreach ($configs as $config) {
            $configValue = json_decode($config->value, true);
            $riskLevel = $configValue['risk_level'] ?? 'medium';
            
            $exportData[] = [
                $config->id,
                $config->name,
                $config->title,
                $this->getFilterTypes()[$config->type] ?? $config->type,
                $this->getRiskLevels()[$riskLevel] ?? $riskLevel,
                $config->status ? '启用' : '禁用',
                $config->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 规则优先级调整
     * @auth true
     */
    public function priority()
    {
        if ($this->request->isGet()) {
            $rules = SystemConfig::mk()
                ->where('type', 'like', 'filter_%')
                ->where('status', 1)
                ->order('sort asc, id desc')
                ->select();
            
            $this->assign('rules', $rules);
            $this->assign('title', '规则优先级');
            return $this->fetch('content_filter/priority');
        }

        $priorities = $this->request->post('priorities', []);
        
        if (empty($priorities)) {
            $this->error('优先级数据不能为空！');
        }

        $successCount = 0;
        $failCount = 0;

        foreach ($priorities as $id => $sort) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            if ($config->save(['sort' => intval($sort)])) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("优先级调整完成！成功：{$successCount}，失败：{$failCount}");
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'ContentFilter管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
