{extend name='main'}

{block name='content'}
<form action="{:sysuri()}" class="layui-form layui-card" data-auto="true" method="post">
    <div class="layui-card-body ta-pl-40" style="max-width:800px">

        <div class="layui-form-item">
            <span class="help-label label-required-prev"><b>支付方式</b>Payment Type</span>
            {empty name='vo.type'}
            <select name="type" class="layui-select" lay-filter="payment-type" lay-search>
                {foreach $payments as $k=>$v}{if isset($vo.type) and $vo.type eq $k}
                <option selected value="{$k}">{$v.name} ( {$v.allow} )</option>
                {else}
                <option value="{$k}">{$v.name} ( {$v.allow} )</option>
                {/if}{/foreach}
            </select>
            {else}
            <select name="type" class="layui-select" disabled lay-filter="payment-type">
                {foreach $payments as $k=>$v}{if isset($vo.type) and $vo.type eq $k}
                <option selected value="{$k}">{$v.name} ( {$v.allow} )</option>
                {else}
                <option value="{$k}">{$v.name} ( {$v.allow} )</option>
                {/if}{/foreach}
            </select>
            <input name="type" type="hidden" value="{$vo.type}">
            {/empty}
            <span class="help-block"><b>必选，</b>请选择预置的支付方式，支付方式创建之后不能修改。</span>
        </div>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>支付名称</b>Payment Name</span>
            <input class="layui-input" maxlength="50" name="name" vali-name="支付名称" placeholder="请输入支付名称" required value="{$vo.name|default=''}"/>
            <span class="help-block"><b>必填，</b>请填写支付名称，支付名称尽量不要重复，建议字符长度为 4-8 个汉字。</span>
        </label>

        <div class="layui-form-item label-required-prev">
            <span class="help-label"><b>支付图标</b>Payment Image</span>
            <div class="relative block label-required-null">
                <input class="layui-input think-bg-gray" data-tips-hover data-tips-image pattern="url" name="cover" vali-name="支付图标" placeholder="请上传支付图标" required value='{$vo.cover|default=""}'>
                <a class="layui-icon layui-icon-upload input-right-icon" data-field="cover" data-file data-type="png,jpg,gif,jpeg"></a>
                <script>$('[name="cover"]').uploadOneImage()</script>
            </div>
        </div>

        <div class="layui-hide" data-payment-type="wechat">{include file='config/form_wechat'}</div>
        <div class="layui-hide" data-payment-type="alipay">{include file='config/form_alipay'}</div>
        <div class="layui-hide" data-payment-type="joinpay">{include file='config/form_joinpay'}</div>
        <div class="layui-hide" data-payment-type="voucher">{include file='config/form_voucher'}</div>

        <label class="layui-form-item relative layui-hide">
            <span class="help-label"><b>支付描述</b>Payment Remark</span>
            <textarea class="layui-textarea" name="remark" placeholder="请输入支付描述">{$vo.remark|default=''}</textarea>
        </label>

        <div class="hr-line-dashed"></div>
        {notempty name='vo.id'}<input name='id' type='hidden' value='{$vo.id}'>{/notempty}
        {notempty name='vo.code'}<input name='code' type='hidden' value='{$vo.code}'>{/notempty}

        <div class="layui-form-item text-center">
            <button class="layui-btn" type='submit'>保存数据</button>
            <button class="layui-btn layui-btn-danger" data-confirm="确定要取消编辑吗？" data-close type='button'>取消编辑</button>
        </div>

    </div>

</form>
{/block}

{block name='script'}
<script>
    (function (apply) {
        apply({value: $('select[name=type]').val()});
        layui.form.on('select(payment-type)', apply);
    })(function (data) {
        if (data.value.indexOf('wechat') > -1) {
            $('[data-payment-type]').not($('[data-payment-type="wechat"]').removeClass('layui-hide')).addClass('layui-hide');
        } else if (data.value.indexOf('alipay') > -1) {
            $('[data-payment-type]').not($('[data-payment-type="alipay"]').removeClass('layui-hide')).addClass('layui-hide');
        } else if (data.value.indexOf('joinpay') > -1) {
            $('[data-payment-type]').not($('[data-payment-type="joinpay"]').removeClass('layui-hide')).addClass('layui-hide');
        } else if (data.value.indexOf('voucher') > -1) {
            $('[data-payment-type]').not($('[data-payment-type="voucher"]').removeClass('layui-hide')).addClass('layui-hide');
        } else {
            $('[data-payment-type]').addClass('layui-hide');
        }
    });
</script>
{/block}