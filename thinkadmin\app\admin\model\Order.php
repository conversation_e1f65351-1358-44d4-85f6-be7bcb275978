<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;
use think\model\relation\BelongsTo;

/**
 * 订单模型
 * @class Order
 * @package app\admin\model
 */
class Order extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'order';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'package_id' => 'integer',
        'original_price' => 'float',
        'discount_amount' => 'float',
        'final_price' => 'float',
        'coupon_id' => 'integer'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化支付时间
     * @param mixed $value
     * @return string
     */
    public function getPaidTimeAttr($value): string
    {
        return $value ? format_datetime($value) : '';
    }

    /**
     * 格式化过期时间
     * @param mixed $value
     * @return string
     */
    public function getExpiredTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取支付状态选项
     * @return array
     */
    public static function getPaymentStatusOptions(): array
    {
        return [
            'pending' => '待支付',
            'paid' => '已支付',
            'failed' => '支付失败',
            'refunded' => '已退款',
            'cancelled' => '已取消'
        ];
    }

    /**
     * 获取支付方式选项
     * @return array
     */
    public static function getPaymentMethodOptions(): array
    {
        return [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'bank' => '银行卡',
            'balance' => '余额支付'
        ];
    }

    /**
     * 获取支付状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPaymentStatusTextAttr($value, $data)
    {
        $statusMap = static::getPaymentStatusOptions();
        return $statusMap[$data['payment_status']] ?? $data['payment_status'];
    }

    /**
     * 获取支付方式文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getPaymentMethodTextAttr($value, $data)
    {
        if (empty($data['payment_method'])) {
            return '未选择';
        }
        $methodMap = static::getPaymentMethodOptions();
        return $methodMap[$data['payment_method']] ?? $data['payment_method'];
    }

    /**
     * 获取订单状态颜色
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusColorAttr($value, $data)
    {
        $colorMap = [
            'pending' => 'orange',
            'paid' => 'green',
            'failed' => 'red',
            'refunded' => 'blue',
            'cancelled' => 'gray'
        ];
        return $colorMap[$data['payment_status']] ?? 'gray';
    }

    /**
     * 关联用户
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联套餐
     * @return BelongsTo
     */
    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class, 'package_id', 'id');
    }

    /**
     * 关联优惠券
     * @return BelongsTo
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class, 'coupon_id', 'id');
    }

    /**
     * 检查订单是否已支付
     * @return bool
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * 检查订单是否待支付
     * @return bool
     */
    public function isPending(): bool
    {
        return $this->payment_status === 'pending';
    }

    /**
     * 检查订单是否已过期
     * @return bool
     */
    public function isExpired(): bool
    {
        return strtotime($this->expired_time) < time();
    }

    /**
     * 检查订单是否可以取消
     * @return bool
     */
    public function canCancel(): bool
    {
        return in_array($this->payment_status, ['pending']) && !$this->isExpired();
    }

    /**
     * 检查订单是否可以退款
     * @return bool
     */
    public function canRefund(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * 生成订单号
     * @return string
     */
    public static function generateOrderNo(): string
    {
        return 'BXW' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 计算订单过期时间
     * @param int $minutes 过期分钟数，默认30分钟
     * @return string
     */
    public static function calculateExpiredTime(int $minutes = 30): string
    {
        return date('Y-m-d H:i:s', time() + $minutes * 60);
    }

    /**
     * 获取订单统计数据
     * @param array $conditions 查询条件
     * @return array
     */
    public static function getOrderStatistics(array $conditions = []): array
    {
        $query = static::mk();
        
        // 应用查询条件
        if (!empty($conditions['start_date'])) {
            $query->where('createtime', '>=', $conditions['start_date']);
        }
        if (!empty($conditions['end_date'])) {
            $query->where('createtime', '<=', $conditions['end_date']);
        }
        if (!empty($conditions['payment_status'])) {
            $query->where('payment_status', $conditions['payment_status']);
        }

        // 总订单数
        $totalOrders = $query->count();
        
        // 已支付订单数
        $paidOrders = (clone $query)->where('payment_status', 'paid')->count();
        
        // 总销售额
        $totalAmount = (clone $query)->where('payment_status', 'paid')->sum('final_price');
        
        // 平均订单金额
        $avgAmount = $paidOrders > 0 ? $totalAmount / $paidOrders : 0;
        
        // 支付成功率
        $paymentRate = $totalOrders > 0 ? ($paidOrders / $totalOrders) * 100 : 0;

        return [
            'total_orders' => $totalOrders,
            'paid_orders' => $paidOrders,
            'total_amount' => round($totalAmount, 2),
            'avg_amount' => round($avgAmount, 2),
            'payment_rate' => round($paymentRate, 2)
        ];
    }

    /**
     * 获取热门套餐统计
     * @param int $limit
     * @return array
     */
    public static function getPopularPackages(int $limit = 10): array
    {
        return static::mk()
            ->field('package_id, package_name, COUNT(*) as order_count, SUM(final_price) as total_amount')
            ->where('payment_status', 'paid')
            ->group('package_id')
            ->order('order_count desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }

    /**
     * 获取每日订单统计
     * @param int $days 统计天数
     * @return array
     */
    public static function getDailyOrderStats(int $days = 30): array
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        $endDate = date('Y-m-d');
        
        return static::mk()
            ->field('DATE(createtime) as date, COUNT(*) as order_count, SUM(CASE WHEN payment_status="paid" THEN final_price ELSE 0 END) as amount')
            ->where('createtime', '>=', $startDate)
            ->where('createtime', '<=', $endDate . ' 23:59:59')
            ->group('DATE(createtime)')
            ->order('date asc')
            ->select()
            ->toArray();
    }

    /**
     * 更新订单状态
     * @param string $status
     * @param array $extraData
     * @return bool
     */
    public function updatePaymentStatus(string $status, array $extraData = []): bool
    {
        $updateData = ['payment_status' => $status];
        
        if ($status === 'paid' && empty($this->paid_time)) {
            $updateData['paid_time'] = date('Y-m-d H:i:s');
        }
        
        // 合并额外数据
        $updateData = array_merge($updateData, $extraData);
        
        return $this->save($updateData);
    }

    /**
     * 验证订单数据
     * @param array $data
     * @return array
     */
    public static function validateOrderData(array $data): array
    {
        $errors = [];
        
        // 验证价格
        if ($data['original_price'] < 0) {
            $errors[] = '原价不能为负数';
        }
        
        if ($data['final_price'] < 0) {
            $errors[] = '实付金额不能为负数';
        }
        
        if ($data['discount_amount'] < 0) {
            $errors[] = '优惠金额不能为负数';
        }
        
        if ($data['final_price'] > $data['original_price']) {
            $errors[] = '实付金额不能高于原价';
        }
        
        // 验证订单号唯一性
        if (!empty($data['order_no'])) {
            $exists = static::mk()->where('order_no', $data['order_no']);
            if (!empty($data['id'])) {
                $exists->where('id', '<>', $data['id']);
            }
            if ($exists->count() > 0) {
                $errors[] = '订单号已存在';
            }
        }
        
        return $errors;
    }
}
