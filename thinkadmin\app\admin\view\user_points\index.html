{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        用户积分管理
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-inline">
                    <input name="user_id" value="{$get.user_id|default=''}" placeholder="请输入用户ID" class="layui-input">
                </div>
            </div>            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">变动说明</label>
                <div class="layui-input-inline">
                    <input name="description" value="{$get.description|default=''}" placeholder="请输入变动说明" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">全部状态</option>
                        <option value="1" {if isset($get.status) and $get.status eq '1'}selected{/if}>启用</option>
                        <option value="0" {if isset($get.status) and $get.status eq '0'}selected{/if}>禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <!-- 操作按钮 -->
        <div class="layui-row" style="margin-bottom: 15px;">
            <div class="layui-col-md12">
                {if auth("add")}
                <button data-modal='{:url("add")}' data-title="添加用户积分管理" class='layui-btn layui-btn-sm layui-btn-primary'>
                    <i class='layui-icon layui-icon-add-1'></i> 添加用户积分管理
                </button>
                {/if}
                {if auth("remove")}
                <button data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                    <i class='layui-icon layui-icon-delete'></i> 批量删除
                </button>
                {/if}
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>ID</th>
                        <th class='text-left nowrap'>用户ID</th>
                        <th class='text-left nowrap'>积分余额</th>
                        <th class='text-left nowrap'>变动类型</th>
                        <th class='text-left nowrap'>变动数量</th>
                        <th class='text-left nowrap'>创建时间</th>

                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>{$vo.id|default=''}</td>
                        <td class='text-left nowrap'>{$vo.user_id|default=''}</td>
                        <td class='text-left nowrap'>{$vo.points|default=''}</td>
                        <td class='text-left nowrap'>{$vo.change_type|default=''}</td>
                        <td class='text-left nowrap'>{$vo.change_amount|default=''}</td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>

                        <td class='text-left nowrap'>
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-xs' data-modal='{:url("edit")}?id={$vo.id}' data-title="编辑用户积分管理">编辑</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-xs layui-btn-danger' data-confirm="确定要删除吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                
                {if empty($list)}
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <p class="help-block text-center well" style="padding: 20px; text-align: center; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><br>
                            暂 无 数 据！
                        </p>
                    </div>
                </div>
                {/if}
                
                <!-- 分页 -->
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) {
                    $.msg.error(ret.info, 3, function () {
                        location.reload();
                    });
                } else {
                    $.msg.success(ret.info);
                }
                return false;
            }, false);
        });
    });
</script>
{/block}