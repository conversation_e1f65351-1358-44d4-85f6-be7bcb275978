<?php

// +----------------------------------------------------------------------
// | Rewrite Task Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\RewriteTask as RewriteTaskModel;
use app\admin\model\AiModel;

/**
 * 降重任务管理
 * @class RewriteTask
 * @package app\admin\controller
 */
class RewriteTask extends Controller
{
    /**
     * 降重任务管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        RewriteTaskModel::mQuery()->layTable(function () {
            $this->title = '降重任务管理';
            $this->aiModels = AiModel::mk()->where(['status' => 1])->column('name', 'id');
        }, static function (QueryHelper $query) {
            $query->like('title,rewrite_mode')->equal('user_id,ai_model_id,status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加降重任务
     * @auth true
     */
    public function add()
    {
        RewriteTaskModel::mForm('rewrite_task/form');
    }

    /**
     * 编辑降重任务
     * @auth true
     */
    public function edit()
    {
        RewriteTaskModel::mForm('rewrite_task/form');
    }

    /**
     * 查看降重任务详情
     * @auth true
     */
    public function view()
    {
        RewriteTaskModel::mForm('rewrite_task/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 获取AI模型列表
            $this->aiModels = AiModel::mk()->where(['status' => 1])->column('name', 'id');
            
            // 降重模式选项
            $this->modeOptions = [
                'standard' => '标准模式',
                'conservative' => '保守模式',
                'aggressive' => '激进模式',
                'academic' => '学术模式'
            ];
            
            // 状态选项
            $this->statusOptions = [
                'pending' => '待处理',
                'processing' => '处理中',
                'completed' => '已完成',
                'failed' => '失败',
                'cancelled' => '已取消'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['status'] = 'pending';
                $data['progress'] = 0;
                
                // 计算原始字数
                if (!empty($data['original_text'])) {
                    $data['original_word_count'] = mb_strlen(strip_tags($data['original_text']));
                }
            }
            $data['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 修改任务状态
     * @auth true
     */
    public function state()
    {
        RewriteTaskModel::mSave($this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除降重任务
     * @auth true
     */
    public function remove()
    {
        RewriteTaskModel::mDelete();
    }

    /**
     * 开始降重任务
     * @auth true
     */
    public function startTask()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }
        
        $task = RewriteTaskModel::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }
        
        if ($task['status'] !== 'pending') {
            $this->error('任务状态不正确！');
        }
        
        // 这里可以调用n8n工作流来开始降重任务
        // 暂时返回成功状态
        $task->save([
            'status' => 'processing',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('降重任务已启动！');
    }

    /**
     * 取消降重任务
     * @auth true
     */
    public function cancelTask()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }
        
        $task = RewriteTaskModel::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }
        
        if (!in_array($task['status'], ['pending', 'processing'])) {
            $this->error('任务状态不允许取消！');
        }
        
        $task->save([
            'status' => 'cancelled',
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('任务已取消！');
    }

    /**
     * 重新执行任务
     * @auth true
     */
    public function retryTask()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('任务ID不能为空！');
        }
        
        $task = RewriteTaskModel::mk()->findOrEmpty($id);
        if ($task->isEmpty()) {
            $this->error('任务不存在！');
        }
        
        if ($task['status'] !== 'failed') {
            $this->error('只有失败的任务才能重新执行！');
        }
        
        $task->save([
            'status' => 'pending',
            'progress' => 0,
            'error_message' => null,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        $this->success('任务已重置，可以重新执行！');
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', []);
        
        if (empty($action) || empty($ids)) {
            $this->error('参数错误！');
        }
        
        $count = 0;
        foreach ($ids as $id) {
            $task = RewriteTaskModel::mk()->findOrEmpty($id);
            if ($task->isEmpty()) continue;
            
            switch ($action) {
                case 'start':
                    if ($task['status'] === 'pending') {
                        $task->save(['status' => 'processing', 'update_time' => date('Y-m-d H:i:s')]);
                        $count++;
                    }
                    break;
                case 'cancel':
                    if (in_array($task['status'], ['pending', 'processing'])) {
                        $task->save(['status' => 'cancelled', 'update_time' => date('Y-m-d H:i:s')]);
                        $count++;
                    }
                    break;
                case 'delete':
                    $task->delete();
                    $count++;
                    break;
            }
        }
        
        $this->success("批量操作完成，处理了 {$count} 个任务！");
    }
}
