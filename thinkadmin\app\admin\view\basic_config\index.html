{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        基础参数设置
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">网站名称</label>
                <div class="layui-input-block">
                    <input type="text" name="site_name" value="{$configs.site_name}" placeholder="请输入网站名称" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">网站Logo</label>
                <div class="layui-input-block">
                    <input type="text" name="site_logo" value="{$configs.site_logo}" placeholder="请输入网站Logo" class="layui-input">
                </div>
            </div>            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">网站描述</label>
                <div class="layui-input-block">
                    <textarea name="site_description" placeholder="请输入网站描述" class="layui-textarea">{$configs.site_description}</textarea>
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">默认论文类型</label>
                <div class="layui-input-block">
                    <input type="text" name="default_paper_type" value="{$configs.default_paper_type}" placeholder="请输入默认论文类型" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">论文最大字数</label>
                <div class="layui-input-block">
                    <input type="text" name="max_paper_length" value="{$configs.max_paper_length}" placeholder="请输入论文最大字数" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">论文最小字数</label>
                <div class="layui-input-block">
                    <input type="text" name="min_paper_length" value="{$configs.min_paper_length}" placeholder="请输入论文最小字数" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">自动保存间隔(秒)</label>
                <div class="layui-input-block">
                    <input type="text" name="auto_save_interval" value="{$configs.auto_save_interval}" placeholder="请输入自动保存间隔(秒)" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">启用AI写作</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="enable_ai_writing" value="1" lay-skin="switch" lay-text="启用|禁用" {if $configs.enable_ai_writing}checked{/if}>
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">启用查重功能</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="enable_plagiarism_check" value="1" lay-skin="switch" lay-text="启用|禁用" {if $configs.enable_plagiarism_check}checked{/if}>
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">启用降重功能</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="enable_rewrite" value="1" lay-skin="switch" lay-text="启用|禁用" {if $configs.enable_rewrite}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info);
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}