<?php

require_once 'vendor/autoload.php';

use think\facade\Db;

$app = new \think\App();
$app->initialize();

// 查找写作中心菜单
$writingCenter = Db::name('system_menu')->where('title', '写作中心')->find();
if ($writingCenter) {
    echo '写作中心菜单ID: ' . $writingCenter['id'] . PHP_EOL;
    
    // 查看写作中心下的子菜单
    $subMenus = Db::name('system_menu')->where('pid', $writingCenter['id'])->order('sort asc')->select();
    echo '写作中心子菜单:' . PHP_EOL;
    foreach ($subMenus as $menu) {
        echo '- ' . $menu['title'] . ' (' . $menu['node'] . ')' . PHP_EOL;
    }
    
    // 检查是否已有正文模板管理菜单
    $contentTemplateMenu = Db::name('system_menu')
        ->where('pid', $writingCenter['id'])
        ->where('node', 'admin/content_template/index')
        ->find();
    
    if ($contentTemplateMenu) {
        echo PHP_EOL . '正文模板管理菜单已存在，ID: ' . $contentTemplateMenu['id'] . PHP_EOL;
    } else {
        echo PHP_EOL . '正文模板管理菜单不存在，需要添加' . PHP_EOL;
        
        // 添加正文模板管理菜单
        $data = [
            'pid' => $writingCenter['id'],
            'title' => '正文模板管理',
            'icon' => 'layui-icon layui-icon-template-1',
            'node' => 'admin/content_template/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 240,
            'status' => 1,
            'create_at' => date('Y-m-d H:i:s')
        ];
        
        $result = Db::name('system_menu')->insert($data);
        if ($result) {
            echo '正文模板管理菜单添加成功！' . PHP_EOL;
        } else {
            echo '正文模板管理菜单添加失败！' . PHP_EOL;
        }
    }
} else {
    echo '未找到写作中心菜单' . PHP_EOL;
}
