<?php

namespace app\admin\controller;

use app\admin\model\WebhookConfigModel;
use think\admin\Controller;

/**
 * Webhook配置管理
 * @class WebhookConfig
 * @package app\admin\controller
 */
class WebhookConfig extends Controller
{
    /**
     * Webhook配置管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        WebhookConfigModel::mQuery($this)->layPage(function () {
            $this->title = 'Webhook配置管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加Webhook配置管理
     * @auth true
     */
    public function add()
    {
        WebhookConfigModel::mForm('webhook_config/form');
    }

    /**
     * 编辑Webhook配置管理
     * @auth true
     */
    public function edit()
    {
        WebhookConfigModel::mForm('webhook_config/form');
    }

    /**
     * 删除Webhook配置管理
     * @auth true
     */
    public function remove()
    {
        WebhookConfigModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        WebhookConfigModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}