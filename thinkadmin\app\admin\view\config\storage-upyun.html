<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card">
    <div class="layui-card-body padding-top-20">

        <div class="color-text layui-code text-center layui-bg-gray" style="border-left-width:1px;margin:0 0 15px 40px">
            <p class="margin-bottom-5 font-w7">文件将上传到 <a target="_blank" href="https://console.upyun.com/register/?invite=PN1cRmjRb">又拍云</a> USS 存储，需要配置 USS 公开访问及跨域策略</p>
            <p>配置跨域访问 CORS 规则，设置来源 Origin 为 *，允许 Methods 为 POST，允许 Headers 为 *</p>
        </div>

        {include file='config/storage-0'}

        <div class="layui-form-item">
            <label class="layui-form-label label-required">
                <b class="color-green">访问协议</b><br><span class="nowrap color-desc">Protocol</span>
            </label>
            <div class="layui-input-block">
                {if !sysconf('storage.upyun_http_protocol')}{php}sysconf('storage.upyun_http_protocol','http');{/php}{/if}
                <div class="layui-input help-checks">
                    {foreach ['http'=>'HTTP','https'=>'HTTPS','auto'=>"AUTO"] as $protocol=>$remark}
                    <label class="think-radio">
                        {if sysconf('storage.upyun_http_protocol') eq $protocol}
                        <input checked type="radio" name="storage.upyun_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {else}
                        <input type="radio" name="storage.upyun_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {/if}
                    </label>
                    {/foreach}
                </div>
                <p class="help-block">又拍云存储访问协议，其中 HTTPS 需要配置证书才能使用（AUTO 为相对协议）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.upyun_bucket">
                <b class="color-green">空间名称</b><br><span class="nowrap color-desc">Bucket</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.upyun_bucket" name="storage.upyun_bucket" value="{:sysconf('storage.upyun_bucket')}" required vali-name="空间名称" placeholder="请输入又拍云存储 Bucket (空间名称)" class="layui-input">
                <p class="help-block">填写又拍云存储空间名称，如：think-admin-uss（需要是全区唯一的值，不存在时会自动创建）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.upyun_http_domain">
                <b class="color-green">访问域名</b><br><span class="nowrap color-desc">Domain</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.upyun_http_domain" name="storage.upyun_http_domain" value="{:sysconf('storage.upyun_http_domain')}" required vali-name="访问域名" placeholder="请输入又拍云存储 Domain (访问域名)" class="layui-input">
                <p class="help-block">填写又拍云存储外部访问域名，不需要填写访问协议，如：static.uss.thinkadmin.top</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.upyun_access_key">
                <b class="color-green">操作账号</b><br><span class="nowrap color-desc">Username</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.upyun_access_key" name="storage.upyun_access_key" value="{:sysconf('storage.upyun_access_key')}" maxlength="100" required vali-name="操作员账号" placeholder="请输入又拍云存储 Username (操作员账号)" class="layui-input">
                <p class="help-block">可以在 [ 账户管理 > 操作员 ] 设置操作员账号并将空间给予授权。</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.upyun_secret_key">
                <b class="color-green">操作密码</b><br><span class="nowrap color-desc">Password</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.upyun_secret_key" name="storage.upyun_secret_key" value="{:sysconf('storage.upyun_secret_key')}" maxlength="100" required vali-name="操作员密码" placeholder="请输入又拍云存储 Password (操作员密码)" class="layui-input">
                <p class="help-block">可以在 [ 账户管理 > 操作员 ] 设置操作员密码并将空间给予授权</p>
            </div>
        </div>

        <div class="hr-line-dashed margin-left-40"></div>
        <input type="hidden" name="storage.type" value="upyun">

        <div class="layui-form-item text-center padding-left-40">
            <button class="layui-btn" type="submit">保存配置</button>
            <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消修改吗？" data-close>取消修改</button>
        </div>

    </div>
</form>