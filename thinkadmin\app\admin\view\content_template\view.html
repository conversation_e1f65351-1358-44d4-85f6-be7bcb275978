{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">正文模板详情</div>
    <div class="layui-card-body">
        <div class="layui-form layui-form-pane">
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.name|default=''}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$paperTypes[$vo.paper_type_id]|default='通用'}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">模板内容</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="white-space: pre-wrap; background: #f8f8f8; padding: 15px; border-radius: 4px; min-height: 300px; max-height: 500px; overflow-y: auto;">
{$vo.template_content|default=''}
                    </div>
                </div>
            </div>
            
            {if !empty($vo.style_config)}
            <div class="layui-form-item">
                <label class="layui-form-label">样式配置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <label class="layui-form-label">字体</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.font_family|default='宋体'}</div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">字号</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.font_size|default='12pt'}</div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <label class="layui-form-label">行距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.line_spacing|default='1.5倍行距'}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">页边距设置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <label class="layui-form-label">上边距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.margin_top|default=2.5}cm</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">下边距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.margin_bottom|default=2.5}cm</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">左边距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.margin_left|default=3}cm</div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <label class="layui-form-label">右边距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.margin_right|default=3}cm</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">段落设置</label>
                <div class="layui-input-block">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <label class="layui-form-label">首行缩进</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.text_indent|default=2}字符</div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <label class="layui-form-label">段前距</label>
                            <div class="layui-input-block">
                                <div class="layui-form-mid">{$vo.style_config.paragraph_spacing|default=0}磅</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/if}
            
            {if !empty($vo.description)}
            <div class="layui-form-item">
                <label class="layui-form-label">描述说明</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid" style="white-space: pre-wrap;">{$vo.description|default=''}</div>
                </div>
            </div>
            {/if}
            
            <div class="layui-form-item">
                <label class="layui-form-label">使用次数</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.usage_count|default=0} 次</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $vo.is_default eq 1}
                        <span class="layui-badge layui-bg-green">是</span>
                        {else}
                        <span class="layui-badge">否</span>
                        {/if}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">
                        {if $vo.status eq 1}
                        <span class="layui-badge layui-bg-green">启用</span>
                        {else}
                        <span class="layui-badge">禁用</span>
                        {/if}
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.create_time|format_datetime}</div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">更新时间</label>
                <div class="layui-input-block">
                    <div class="layui-form-mid">{$vo.update_time|format_datetime}</div>
                </div>
            </div>
            
            <div class="hr-line-dashed"></div>
            
            <div class="layui-form-item text-center">
                {if auth("edit")}
                <a class="layui-btn" href="{:url('edit')}?id={$vo.id}">编辑模板</a>
                {/if}
                <button class="layui-btn layui-btn-danger" type='button' data-close>关闭窗口</button>
            </div>
            
        </div>
    </div>
</div>
{/block}
