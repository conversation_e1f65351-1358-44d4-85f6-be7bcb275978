<?php

namespace app\admin\controller;

use app\admin\model\OutlineTemplate as OutlineTemplateModel;
use think\admin\Controller;

/**
 * 大纲模板管理
 * @class OutlineTemplate
 * @package app\admin\controller
 */
class OutlineTemplate extends Controller
{
    /**
     * 大纲模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        try {
            $this->title = '大纲模板管理';

            // 构建查询条件
            $map = [];
            $params = $this->request->param();

            // 处理搜索条件
            if (!empty($params['name'])) {
                $map[] = ['name', 'like', '%' . $params['name'] . '%'];
            }

            if (!empty($params['title'])) {
                $map[] = ['title', 'like', '%' . $params['title'] . '%'];
            }

            if (!empty($params['description'])) {
                $map[] = ['description', 'like', '%' . $params['description'] . '%'];
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $map['status'] = $params['status'];
            }

            // 时间范围查询
            if (!empty($params['create_time_start'])) {
                $map[] = ['create_time', '>=', strtotime($params['create_time_start'])];
            }
            if (!empty($params['create_time_end'])) {
                $map[] = ['create_time', '<=', strtotime($params['create_time_end']) + 86399];
            }

            // 执行查询
            $list = OutlineTemplateModel::where($map)
                ->order('id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => $params
                ]);

            $this->assign([
                'title' => '大纲模板管理',
                'list' => $list,
                'pagehtml' => $list->render(),
                'get' => $params
            ]);

            return $this->fetch();

        } catch (\Exception $e) {
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 添加大纲模板管理
     * @auth true
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name' => 'require|max:100',
                'title' => 'require|max:200',
                'content' => 'require',
                'status' => 'in:0,1'
            ];

            $this->validate($data, $validate, [
                'name.require' => '模板名称不能为空',
                'name.max' => '模板名称不能超过100个字符',
                'title.require' => '模板标题不能为空',
                'title.max' => '模板标题不能超过200个字符',
                'content.require' => '模板内容不能为空',
                'status.in' => '状态值范围异常'
            ]);

            $data['create_time'] = time();
            $data['update_time'] = time();

            if (OutlineTemplateModel::create($data)) {
                $this->success('添加成功！');
            } else {
                $this->error('添加失败！');
            }
        }

        return $this->fetch('outline_template/form');
    }

    /**
     * 编辑大纲模板管理
     * @auth true
     */
    public function edit()
    {
        $id = $this->request->param('id', 0);
        $model = OutlineTemplateModel::findOrEmpty($id);

        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name' => 'require|max:100',
                'title' => 'require|max:200',
                'content' => 'require',
                'status' => 'in:0,1'
            ];

            $this->validate($data, $validate, [
                'name.require' => '模板名称不能为空',
                'name.max' => '模板名称不能超过100个字符',
                'title.require' => '模板标题不能为空',
                'title.max' => '模板标题不能超过200个字符',
                'content.require' => '模板内容不能为空',
                'status.in' => '状态值范围异常'
            ]);

            $data['update_time'] = time();

            if ($model->save($data)) {
                $this->success('编辑成功！');
            } else {
                $this->error('编辑失败！');
            }
        }

        $this->assign('vo', $model);
        return $this->fetch('outline_template/form');
    }

    /**
     * 删除大纲模板管理
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);

        if (empty($id)) {
            $this->error('参数错误！');
        }

        $model = OutlineTemplateModel::findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($model->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        $id = $this->request->post('id', 0);
        $status = $this->request->post('status', 0);

        if (empty($id)) {
            $this->error('参数错误！');
        }

        if (!in_array($status, [0, 1])) {
            $this->error('状态值范围异常！');
        }

        $model = OutlineTemplateModel::findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($model->save(['status' => $status, 'update_time' => time()])) {
            $this->success('状态修改成功！');
        } else {
            $this->error('状态修改失败！');
        }
    }
}