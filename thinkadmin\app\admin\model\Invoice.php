<?php

// +----------------------------------------------------------------------
// | Invoice Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 发票模型
 * @class Invoice
 * @package app\admin\model
 */
class Invoice extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'invoice';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'order_id' => 'integer',
        'amount' => 'float'
    ];

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '开具中',
            'issued' => '已开具',
            'failed' => '开具失败',
            'cancelled' => '已取消'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取发票类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getInvoiceTypeTextAttr($value, $data)
    {
        $typeMap = [
            'personal' => '个人发票',
            'company' => '企业发票'
        ];
        return $typeMap[$data['invoice_type']] ?? $data['invoice_type'];
    }

    /**
     * 获取格式化金额
     * @param $value
     * @param $data
     * @return string
     */
    public function getAmountFormatAttr($value, $data)
    {
        return '¥' . number_format($data['amount'], 2);
    }

    /**
     * 搜索器 - 发票抬头
     * @param $query
     * @param $value
     */
    public function searchInvoiceTitleAttr($query, $value)
    {
        $query->whereLike('invoice_title', "%{$value}%");
    }

    /**
     * 搜索器 - 税号
     * @param $query
     * @param $value
     */
    public function searchTaxNumberAttr($query, $value)
    {
        $query->whereLike('tax_number', "%{$value}%");
    }

    /**
     * 搜索器 - 发票代码
     * @param $query
     * @param $value
     */
    public function searchInvoiceCodeAttr($query, $value)
    {
        $query->whereLike('invoice_code', "%{$value}%");
    }

    /**
     * 搜索器 - 发票号码
     * @param $query
     * @param $value
     */
    public function searchInvoiceNumberAttr($query, $value)
    {
        $query->whereLike('invoice_number', "%{$value}%");
    }

    /**
     * 搜索器 - 用户ID
     * @param $query
     * @param $value
     */
    public function searchUserIdAttr($query, $value)
    {
        $query->where('user_id', $value);
    }

    /**
     * 搜索器 - 订单ID
     * @param $query
     * @param $value
     */
    public function searchOrderIdAttr($query, $value)
    {
        $query->where('order_id', $value);
    }

    /**
     * 搜索器 - 发票类型
     * @param $query
     * @param $value
     */
    public function searchInvoiceTypeAttr($query, $value)
    {
        $query->where('invoice_type', $value);
    }

    /**
     * 搜索器 - 状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }
}
