{extend name='admin@public/layout'}

{block name="button"}
<!--{if auth("add")}-->
<button data-table-id="OutlineTemplateTable" data-modal='{:url("add")}' class='layui-btn layui-btn-sm layui-btn-primary'>添加大纲模板</button>
<!--{/if}-->

<!--{if auth("remove")}-->
<button data-table-id="OutlineTemplateTable" data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除大纲模板吗？" class='layui-btn layui-btn-sm layui-btn-primary'>批量删除</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-inline">
                    <input name="name" value="{$get.name|default=''}" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-inline">
                    <select name="paper_type_id" class="layui-select">
                        <option value="">全部类型</option>
                        <option value="0" {if isset($get.paper_type_id) and $get.paper_type_id eq '0'}selected{/if}>通用模板</option>
                        {volist name="paperTypes" id="v" key="k"}
                        {if isset($get.paper_type_id) and $get.paper_type_id eq $k}
                        <option selected value="{$k}">{$v}</option>
                        {else}
                        <option value="{$k}">{$v}</option>
                        {/if}
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit">搜 索</button>
            </div>
        </form>
        <table id="OutlineTemplateTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
    </div>
</div>
{/block}

{block name='script'}
<script>
    $(function () {
        // 初始化表格组件
        $('#OutlineTemplateTable').layTable({
            even: true, height: 'full',
            sort: {field: 'is_default desc,usage_count desc,id', type: 'desc'},
            cols: [[
                {checkbox: true, fixed: true},
                {field: 'name', title: '模板名称', width: '20%', align: 'left'},
                {field: 'paper_type_name', title: '适用类型', width: 120, align: 'center'},
                {field: 'is_default_text', title: '默认模板', width: 100, align: 'center', templet: '#DefaultTpl'},
                {field: 'usage_count', title: '使用次数', width: 100, align: 'center'},
                {field: 'status', title: '状态', width: 100, align: 'center', templet: '#StatusSwitchTpl'},
                {field: 'create_time', title: '创建时间', width: 170, align: 'center', sort: true},
                {toolbar: '#toolbar', align: 'center', width: 200, title: '操作', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (obj) {
            var data = {id: obj.value, status: obj.elem.checked > 0 ? 1 : 0};
            $.form.load("{:url('state')}", data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#OutlineTemplateTable').trigger('reload');
                });
                return false;
            }, false);
        });
    });
</script>

<!-- 默认模板显示模板 -->
<script type="text/html" id="DefaultTpl">
    {{# if(d.is_default == 1) { }}
        <span class="layui-badge layui-bg-green">是</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">否</span>
    {{# } }}
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchTpl">
    <!--{if auth("state")}-->
    <input type="checkbox" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="StatusSwitch" {{-d.status>0?'checked':''}}>
    <!--{else}-->
    {{-d.status ? '<b class="color-green">启用</b>' : '<b class="color-red">禁用</b>'}}
    <!--{/if}-->
</script>

<!-- 数据操作工具条模板 -->
<script type="text/html" id="toolbar">
    <!--{if auth('edit')}-->
    <a class="layui-btn layui-btn-primary layui-btn-sm" data-event-dbclick data-title="编辑模板" data-modal='{:url("edit")}?id={{d.id}}'>编辑</a>
    <!--{/if}-->
    
    <!--{if auth('setDefault')}-->
    {{# if(d.is_default != 1) { }}
    <a class="layui-btn layui-btn-sm" data-confirm="确定要设为默认模板吗？" data-action="{:url('setDefault')}" data-value="id#{{d.id}}">设为默认</a>
    {{# } }}
    <!--{/if}-->
    
    <!--{if auth('copy')}-->
    <a class="layui-btn layui-btn-warm layui-btn-sm" data-confirm="确定要复制该模板吗？" data-action="{:url('copy')}" data-value="id#{{d.id}}">复制</a>
    <!--{/if}-->

    <!--{if auth("remove")}-->
    <a class="layui-btn layui-btn-danger layui-btn-sm" data-confirm="确定要删除该模板吗?" data-action="{:url('remove')}" data-value="id#{{d.id}}">删除</a>
    <!--{/if}-->
</script>
{/block}
