@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

fieldset {
  margin: 0 0 10px 0;
  border: 1px solid @BoxBorderColor;
  padding: 10px 20px;
  background: #fff;
  border-radius: @BoxBorderRadius;

  legend {
    color: #666;
    padding: 0 10px;
    font-size: 12px;
    letter-spacing: 1px;

    .layui-badge:last-child {
      margin-right: 0;
    }
  }

  .layui-form-item {
    margin-bottom: 10px;
  }
}

form.layui-form fieldset {
  padding: 15px 25px;
}

.layui-tab,
.layui-card {
  border-radius: @BoxBorderRadius;
}

.layui-tab {
  .layui-tab-title {
    border-top-left-radius: @BoxBorderRadius;
    border-top-right-radius: @BoxBorderRadius;

    > li:first-child {
      padding: 0 15px 0 17px;
      margin-left: 0 !important;
      border-top-left-radius: @BoxBorderRadius;

      &:after {
        border-left: none
      }
    }
  }

  .layui-tab-content {
    border-bottom-left-radius: @BoxBorderRadius;
    border-bottom-right-radius: @BoxBorderRadius;
  }

  > .layui-tab-content {
    padding: 20px;
    background: #fff;
  }
}

.layui-btn {
  border: 1px solid #16baaa;

  &:hover:not(.layui-btn-disabled) {
    filter: alpha(opacity=100);
    opacity: 1;
  }

  &-warm {
    border: 1px solid #FFB800;
  }

  &-danger {
    border: 1px solid #FF5722;
  }

  &-normal {
    border: 1px solid #1E9FFF;
  }

  &-disabled {
    border: 1px solid @BoxBorderColor;
  }

  &-primary {
    background: #fff;
  }

  &-group {
    border: 1px solid #098;
    overflow: hidden;
    background: #009688;
    line-height: 30px;
    border-radius: 2px;

    + .layui-btn {
      margin-left: 8px;
    }

    .layui-btn {
      height: 30px;
      line-height: 32px;
      border-width: 0 !important;
      border-radius: 0 !important;

      + .layui-btn {
        margin-left: 1px !important;
      }

      &-primary:hover {
        border-color: #009688;
      }
    }
  }

  & + .layui-btn {
    margin-left: 8px;
  }
}

.layui-code {
  border-radius: @BoxBorderRadius;
}

.layui-badge {
  margin-right: 5px;

  &-middle {
    width: 1em;
    height: auto;
    padding: 5px;
    line-height: 16px;
    white-space: normal;
    box-sizing: content-box;
    vertical-align: middle;
  }
}

.layui-input,
.layui-select {
  line-height: 38px;
  border-color: @InputBorderNormalColor;

  &:hover, &:active, &:focus {
    border-color: @InputBorderActiveColor;
  }
}

.layui-select {
  appearance: revert;
  -moz-appearance: revert;
  -webkit-appearance: revert;
}

.layui-disabled,
.layui-disabled:hover {
  color: #333 !important;
  background: #EEE !important;
}

.layui-nav {
  .layui-nav-item {
    .layui-elip {
      padding-right: 35px !important;
    }

    .layui-nav-more {
      right: 15px;
      font-size: 14px !important;
    }
  }
}

.layui-tags {
  display: flex;
  flex-wrap: wrap;
  vertical-align: middle;

  .layui-tag {
    color: #FFF;
    height: 38px;
    margin: 3px 4px 3px 0;
    padding: 0 4px 0 10px;
    display: inline-block;
    font-size: 14px;
    line-height: 38px;
    border-radius: 2px;
    white-space: nowrap;
    background: #1E9FFF !important;
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;

    .layui-icon {
      font-size: 14px;
      font-weight: 700;
      margin-left: 5px;

      &:hover {
        cursor: pointer;
        color: #FF5722;
      }
    }

    &-input {
      width: 100px;
      resize: none;
      margin: 3px 8px 3px 0;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}

.layui-table {
  td, th {
    font-size: 12px;
  }

  td.list-table-check-td + td,
  th.list-table-check-td + th {
    padding-left: 5px;
  }

  input.layui-input,
  .layui-btn.layui-btn-sm {
    height: 26px;
    box-sizing: border-box;
    line-height: 26px;
  }

  .input-right-icon {
    width: 28px;
    height: 26px;
    line-height: 26px;
  }

  &[lay-size="lg"] {
    input.layui-input,
    .layui-btn.layui-btn-sm {
      height: 38px;
      box-sizing: border-box;
      line-height: 38px;
    }

    .input-right-icon {
      width: 38px;
      height: 36px;
      line-height: 36px;
    }
  }

  .list-table-sort-td {
    width: 10px !important;
    text-align: center !important;
    padding-left: 5px !important;
    padding-right: 5px !important;

    button {
      width: 56px;
      background: #009688
    }

    input {
      width: 50px;
      color: #666;
      border: 1px solid @InputBorderNormalColor;
      padding: 2px;
      font-size: 9pt;
      text-align: center;
      line-height: 18px
    }
  }

  .list-table-check-td {
    width: 10px !important;
    text-align: center !important;
    padding-left: 15px !important;
    padding-right: 15px !important;

    input {
      margin: 0 !important;
      vertical-align: middle
    }
  }
}

.tableSelect {
  .layui-table-view {
    margin: 10px 0;
  }
}

.layui-table-view {
  margin: 0;

  .layui-form-switch {
    margin-top: -3px;
  }

  .layui-table-page {
    .layui-laypage {
      .layui-laypage-prev,
      .layui-laypage-next {
        padding: 0 8px;
        border-radius: 3px;
      }

      .layui-laypage-prev {
        margin-left: 0 !important;
      }

      .layui-laypage-next {
        margin-right: 6px !important;
      }
    }
  }
}

/* 默认弹层列表样式 */
.layui-layer-page {
  .laytable-pagination {
    padding: 15px;
    position: relative;
    min-height: 60vh;
  }
}

.layui-layer-tips {
  &.layui-layer-image {
    width: auto !important;

    .layui-layer-content {
      width: auto !important;
      padding: 4px !important;

      img {
        z-index: 2;
        position: relative;
      }

      i.layui-layer-TipsR {
        z-index: 1;
      }
    }
  }
}

.layui-layer-content {
  .layui-form.layui-card {
    margin: 0;
    box-shadow: none !important;

    > .layui-card-body {
      padding: 20px 40px 0 0;
    }
  }
}

.layui-layer-dialog {
  .layui-layer-content {
    .layui-layer-ico {
      top: 50% !important;
      margin-top: -15px !important
    }
  }
}

.laydate-footer-btns {
  span {
    line-height: 24px !important
  }
}

.layui-form-item {
  margin-bottom: 10px;

  .layui-form-checkbox {
    margin: 4px;
  }

  .layui-input.text-center:not([type=number]) {
    padding-left: 0;
  }
}

.layui-form-select {
  .layui-edge {
    top: 50%;
    width: unset;
    height: unset;
    border: none;
    color: #c2c2c2;
    margin-top: -7px;
    line-height: 15px;
    #iconLayout(15px);

    &:before {
      content: "\e61a"
    }
  }

  dl {
    top: 37px;
    padding: 0;
    border-color: @InputBorderActiveColor;

    dd.layui-this {
      font-weight: normal;
    }
  }

  &ed dl {
    min-height: fit-content;
  }
}

.layui-form-radio {
  margin-top: 0;
}

.layui-form-switch {
  padding: 0 10px 0 5px;

  em {
    white-space: nowrap;
    vertical-align: middle;
  }
}

.layui-form-onswitch {
  padding: 0 5px 0 10px;

  em {
    margin-left: 0;
  }
}

.layui-form-checkbox.layui-form-checked i {
  border-color: #5FB878;
}