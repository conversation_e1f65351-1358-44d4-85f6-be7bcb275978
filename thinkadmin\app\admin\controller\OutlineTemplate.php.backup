<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\OutlineTemplate as OutlineTemplateModel;
use app\admin\model\PaperType;

/**
 * 大纲模板管理
 * @class OutlineTemplate
 * @package app\admin\controller
 */
class OutlineTemplate extends Controller
{
    /**
     * 大纲模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        OutlineTemplateModel::mQuery()->layTable(function () {
            $this->title = '大纲模板管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            $query->where(['status' => 1]);
            $query->like('name,description')->equal('paper_type_id,status,is_default');
            $query->order('is_default desc,usage_count desc,id desc');
        });
    }

    /**
     * 添加大纲模板
     * @auth true
     */public function add()
    {
        OutlineTemplateModel::mForm('outline_template/form');
    }

    /**
     * 编辑大纲模板
     * @auth true
     */
    public function edit()
    {
        OutlineTemplateModel::mForm('outline_template/form');
    }

    /**
     * 查看大纲模板
     * @auth true
     */
    public function view()
    {
        OutlineTemplateModel::mForm('outline_template/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 获取论文类型列表
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
            $this->paperTypes[0] = '通用模板';
        } else {
            // POST请求时的数据验证
            $map = [];
            $map[] = ['name', '=', $data['name']];
            $map[] = ['paper_type_id', '=', $data['paper_type_id'] ?? 0];
            $map[] = ['id', '<>', $data['id'] ?? 0];
            if (OutlineTemplateModel::mk()->where($map)->count() > 0) {
                $this->error("该论文类型下已存在同名模板！");
            }
            
            // 如果设置为默认模板，需要取消其他默认模板
            if (!empty($data['is_default'])) {
                OutlineTemplateModel::mk()->where([
                    'paper_type_id' => $data['paper_type_id'] ?? 0,
                    'is_default' => 1
                ])->update(['is_default' => 0]);
            }
            
            // 设置创建和更新时间
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['usage_count'] = 0;
            }
            $data['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 修改模板状态
     * @auth true
     */
    public function state()
    {
        OutlineTemplateModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除大纲模板
     * @auth true
     */
    public function remove()
    {
        OutlineTemplateModel::mDelete();
    }

    /**
     * 设置默认模板
     * @auth true
     */
    public function setDefault()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = OutlineTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        // 取消同类型的其他默认模板
        OutlineTemplateModel::mk()->where([
            'paper_type_id' => $template['paper_type_id'],
            'is_default' => 1
        ])->update(['is_default' => 0]);
        
        // 设置当前模板为默认
        $template->save(['is_default' => 1, 'update_time' => date('Y-m-d H:i:s')]);
        
        $this->success('设置默认模板成功！');
    }

    /**
     * 复制模板
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = OutlineTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        $newTemplate = $template->toArray();
        unset($newTemplate['id']);
        $newTemplate['name'] = $newTemplate['name'] . '_副本';
        $newTemplate['is_default'] = 0;
        $newTemplate['usage_count'] = 0;
        $newTemplate['create_time'] = date('Y-m-d H:i:s');
        $newTemplate['update_time'] = date('Y-m-d H:i:s');
        
        OutlineTemplateModel::mk()->save($newTemplate);
        
        $this->success('复制模板成功！');
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'OutlineTemplate管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
