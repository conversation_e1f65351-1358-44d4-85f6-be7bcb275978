{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        订单管理
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">订单号</label>
                <div class="layui-input-inline">
                    <input name="order_no" value="{$get.order_no|default=''}" placeholder="请输入订单号" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">全部状态</option>
                        <option value="pending" {if isset($get.status) and $get.status eq 'pending'}selected{/if}>待支付</option>
                        <option value="paid" {if isset($get.status) and $get.status eq 'paid'}selected{/if}>已支付</option>
                        <option value="cancelled" {if isset($get.status) and $get.status eq 'cancelled'}selected{/if}>已取消</option>
                        <option value="refunded" {if isset($get.status) and $get.status eq 'refunded'}selected{/if}>已退款</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <!-- 操作按钮 -->
        <div class="layui-row" style="margin-bottom: 15px;">
            <div class="layui-col-md12">
                {if auth("add")}
                <button data-modal='{:url("add")}' data-title="添加订单" class='layui-btn layui-btn-sm layui-btn-primary'>
                    <i class='layui-icon layui-icon-add-1'></i> 添加订单
                </button>
                {/if}
                {if auth("remove")}
                <button data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                    <i class='layui-icon layui-icon-delete'></i> 批量删除
                </button>
                {/if}
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>订单号</th>
                        <th class='text-left nowrap'>用户</th>
                        <th class='text-left nowrap'>商品</th>
                        <th class='text-left nowrap'>金额</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>{$vo.order_no|default=''}</td>
                        <td class='text-left nowrap'>{$vo.user_name|default=''}</td>
                        <td class='text-left nowrap'>{$vo.product_name|default=''}</td>
                        <td class='text-left nowrap'>¥{$vo.amount|default='0.00'}</td>
                        <td class='text-left nowrap'>
                            {switch $vo.status}
                                {case 'pending'}<span class="layui-badge layui-bg-orange">待支付</span>{/case}
                                {case 'paid'}<span class="layui-badge layui-bg-green">已支付</span>{/case}
                                {case 'cancelled'}<span class="layui-badge layui-bg-gray">已取消</span>{/case}
                                {case 'refunded'}<span class="layui-badge layui-bg-red">已退款</span>{/case}
                                {default}<span class="layui-badge">未知</span>{/switch}
                        </td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-xs' data-modal='{:url("edit")}?id={$vo.id}' data-title="编辑订单">编辑</a>
                            {/if}
                            {if auth("view")}
                            <a class='layui-btn layui-btn-xs layui-btn-normal' data-modal='{:url("view")}?id={$vo.id}' data-title="查看订单">查看</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-xs layui-btn-danger' data-confirm="确定要删除吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                
                {if empty($list)}
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <p class="help-block text-center well" style="padding: 20px; text-align: center; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><br>
                            暂 无 数 据！
                        </p>
                    </div>
                </div>
                {/if}
                
                <!-- 分页 -->
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
