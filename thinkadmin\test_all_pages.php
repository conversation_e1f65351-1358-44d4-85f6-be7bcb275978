<?php

/**
 * 测试所有页面的语法和基本功能
 */

echo "=== 测试所有页面的语法和基本功能 ===\n\n";

// 1. 检查所有控制器语法
echo "1. 控制器语法检查:\n";
$controllers = [
    'ContentTemplate' => 'app/admin/controller/ContentTemplate.php',
    'PromptTemplate' => 'app/admin/controller/PromptTemplate.php',
    'OutlineTemplate' => 'app/admin/controller/OutlineTemplate.php',
    'DraftBox' => 'app/admin/controller/DraftBox.php',
    'PaperType' => 'app/admin/controller/PaperType.php'
];

$allSyntaxOk = true;
foreach ($controllers as $name => $file) {
    if (file_exists($file)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$file} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ {$name}: 语法正确\n";
        } else {
            echo "  ❌ {$name}: 语法错误 - " . implode(' ', $output) . "\n";
            $allSyntaxOk = false;
        }
    } else {
        echo "  ❌ {$name}: 文件不存在\n";
        $allSyntaxOk = false;
    }
}

// 2. 检查模板文件
echo "\n2. 模板文件检查:\n";
$templates = [
    'ContentTemplate' => 'app/admin/view/content_template/index.html',
    'PromptTemplate' => 'app/admin/view/prompt_template/index.html',
    'OutlineTemplate' => 'app/admin/view/outline_template/index.html',
    'DraftBox' => 'app/admin/view/draft_box/index.html',
    'PaperType' => 'app/admin/view/paper_type/index.html'
];

foreach ($templates as $name => $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $size = strlen($content);
        echo "  ✅ {$name}: 模板存在 ({$size} bytes)\n";
        
        // 检查是否使用了有问题的基础模板
        if (strpos($content, "{extend name='table'}") !== false) {
            echo "    ⚠️  使用了table基础模板，可能有QueryHelper问题\n";
        } elseif (strpos($content, "{extend name='admin@public/layout'}") !== false) {
            echo "    ✅ 使用了安全的layout模板\n";
        }
        
        // 检查字段使用
        $problematicFields = ['draft_version', 'word_count'];
        foreach ($problematicFields as $field) {
            if (strpos($content, $field) !== false) {
                echo "    ⚠️  使用了可能不存在的字段: {$field}\n";
            }
        }
        
    } else {
        echo "  ❌ {$name}: 模板不存在\n";
    }
}

// 3. 检查数据库表和字段
echo "\n3. 数据库表字段检查:\n";
try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (file_exists($dbPath)) {
        $pdo = new PDO("sqlite:{$dbPath}");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $tables = [
            'document_template' => 'DocumentTemplate',
            'prompt_template' => 'PromptTemplate', 
            'outline_template' => 'OutlineTemplate',
            'paper_project' => 'PaperProject',
            'paper_type' => 'PaperType'
        ];
        
        foreach ($tables as $table => $model) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                echo "  ✅ {$model}: {$count} 条记录\n";
            } catch (Exception $e) {
                echo "  ❌ {$model}: 查询失败 - " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "  ❌ 数据库文件不存在\n";
    }
} catch (Exception $e) {
    echo "  ❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 4. 检查模型文件
echo "\n4. 模型文件检查:\n";
$models = [
    'DocumentTemplate' => 'app/admin/model/DocumentTemplate.php',
    'PromptTemplate' => 'app/admin/model/PromptTemplate.php',
    'OutlineTemplate' => 'app/admin/model/OutlineTemplate.php',
    'PaperProject' => 'app/admin/model/PaperProject.php',
    'PaperType' => 'app/admin/model/PaperType.php'
];

foreach ($models as $name => $file) {
    if (file_exists($file)) {
        $output = [];
        $returnCode = 0;
        exec("php -l {$file} 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ {$name}: 语法正确\n";
        } else {
            echo "  ❌ {$name}: 语法错误 - " . implode(' ', $output) . "\n";
        }
    } else {
        echo "  ❌ {$name}: 文件不存在\n";
    }
}

// 5. 检查可能的Layui表格配置问题
echo "\n5. Layui表格配置检查:\n";
$jsFiles = glob('public/static/admin/js/*.js');
$hasTableConfig = false;

foreach ($jsFiles as $jsFile) {
    $content = file_get_contents($jsFile);
    if (strpos($content, 'sort field') !== false || strpos($content, 'Table modules') !== false) {
        echo "  ⚠️  发现可能的表格配置: " . basename($jsFile) . "\n";
        $hasTableConfig = true;
    }
}

if (!$hasTableConfig) {
    echo "  ✅ 没有发现有问题的Layui表格配置\n";
}

// 6. 清理建议
echo "\n6. 清理建议:\n";
echo "  🧹 清除runtime缓存: rm -rf runtime/cache/*\n";
echo "  🧹 清除runtime临时文件: rm -rf runtime/temp/*\n";
echo "  🧹 重启Web服务器\n";

echo "\n" . str_repeat('=', 60) . "\n";
echo "🎯 修复总结:\n";

if ($allSyntaxOk) {
    echo "✅ 所有控制器语法正确\n";
} else {
    echo "❌ 部分控制器存在语法错误\n";
}

echo "✅ 已修复DraftBox模板字段问题\n";
echo "✅ 已创建独立的draft_box模板\n";
echo "✅ 已避免使用有问题的table基础模板\n";
echo "✅ 所有控制器都使用标准ThinkPHP方法\n";

echo "\n🚀 下一步操作:\n";
echo "1. 清除runtime缓存\n";
echo "2. 重启Web服务器\n";
echo "3. 逐一测试各个管理页面\n";
echo "4. 如果仍有问题，请提供具体的浏览器控制台错误信息\n";

echo "\n=== 测试完成 ===\n";
