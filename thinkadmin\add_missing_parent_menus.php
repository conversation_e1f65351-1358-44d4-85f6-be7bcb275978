<?php

// 添加缺失的一级菜单

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "数据库文件不存在: $dbPath\n";
        exit(1);
    }

    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取当前最大ID
    $stmt = $pdo->query("SELECT MAX(id) as max_id FROM system_menu");
    $maxId = $stmt->fetch(PDO::FETCH_ASSOC)['max_id'] ?? 0;
    $currentId = $maxId + 1;

    // 要添加的一级菜单
    $parentMenusToAdd = [
        [
            'pid' => 0,
            'title' => '文档导出',
            'icon' => 'layui-icon layui-icon-export',
            'node' => '',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 300,
            'status' => 1
        ],
        [
            'pid' => 0,
            'title' => '系统设置',
            'icon' => 'layui-icon layui-icon-set',
            'node' => '',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 700,
            'status' => 1
        ]
    ];

    // 准备插入语句
    $insertSql = "INSERT INTO system_menu (id, pid, title, icon, node, url, params, target, sort, status, create_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($insertSql);

    $addedCount = 0;
    $createTime = date('Y-m-d H:i:s');

    foreach ($parentMenusToAdd as $menu) {
        // 检查菜单是否已存在
        $checkStmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = 0");
        $checkStmt->execute([$menu['title']]);
        if ($checkStmt->fetch()) {
            echo "一级菜单 '{$menu['title']}' 已存在，跳过\n";
            continue;
        }

        // 插入菜单
        $result = $stmt->execute([
            $currentId,
            $menu['pid'],
            $menu['title'],
            $menu['icon'],
            $menu['node'],
            $menu['url'],
            $menu['params'],
            $menu['target'],
            $menu['sort'],
            $menu['status'],
            $createTime
        ]);

        if ($result) {
            echo "✅ 成功添加一级菜单: {$menu['title']} (ID: $currentId)\n";
            $addedCount++;
            $currentId++;
        } else {
            echo "❌ 添加一级菜单失败: {$menu['title']}\n";
        }
    }

    echo "\n总计添加了 $addedCount 个一级菜单\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
