@charset "UTF-8";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

// 默认盒边框样式
@BoxBottomLine: 1px solid rgba(0, 0, 0, 0.10);
@BoxBorderColor: #EEE;
@BoxBorderRadius: 3px;

// 输入边线颜色
@RadioBorderNormalColor: #CCC;
@InputBorderNormalColor: #EEE;
@InputBorderActiveColor: #DDD;

// 基础阴影配置
@ShadowOuterMin: 0 0 4px 0 rgba(0, 0, 0, 0.20);
@ShadowOuterMax: 0 0 6px 1px rgba(0, 0, 0, 0.20);
@ShadowBodyTop: 0 1px 6px 1px rgba(0, 21, 41, 0.35);
@ShadowBodyLeft: 1px 0 6px 1px rgba(0, 21, 41, 0.35);
@ShadowBodyRight: 0 1px 6px 1px rgba(0, 21, 41, 0.35);
@ShadowInset: 0 1px 20px 0 rgba(0, 0, 0, 0.10) inset;

// 导航条颜色配置
@TopHeaderTextColor: #333;
@TopHeaderBackColor: #FFF;
@TopHeaderNavNormalTextColor: @TopHeaderTextColor;
@TopHeaderNavNormalBackColor: @TopHeaderBackColor;

// 导航条按钮颜色
@TopHeaderNavHoverTextColor: #000;
@TopHeaderNavHoverBackColor: rgba(0, 0, 0, 0.05);
@TopHeaderNavActiveTextColor: #000;
@TopHeaderNavActiveBackColor: rgba(0, 0, 0, 0.10);

// 布局尺寸设置
@LayoutLeftMiniSize: 55px;
@LayoutLeftDefaSize: 200px;
@LayoutLeftNavHeight: 50px;
@LayoutHeadTopHeight: 55px;
@LayoutBodyHeadHeight: 50px;

// 左侧菜单布局颜色
@LeftMainBackColor: #20222A !important;

@LeftMainNavHoverBackColor: rgba(99, 99, 99, 0.2);
@LeftMainNavHoverTextColor: #FFF;

@LeftMainNavNormalBackColor: none;
@LeftMainNavNormalTextColor: #EEE;

@LeftMainNavActiveBackColor: #098;
@LeftMainNavActiveTextColor: #FFF;

// 加载页面背景颜色
@LoadBackColor: #EFEFEF;

// 窗口页面背景颜色
@BodyMainBackColor: #EFEFEF;

// 最小滚动样式
#defaScrollbar() {
  height: 100%;
  display: block;
  overflow: auto;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    width: 3px !important;

    &-track {
      background: #666 !important
    }

    &-thumb {
      background-color: #999 !important
    }
  }
}

// 通用延时动态
#defaTransition(@time:0.2s,@type:all) {
  transition: @type @time linear;
  -o-transition: @type @time linear;
  -moz-transition: @type @time linear;
  -webkit-transition: @type @time linear;
}

// 内容主体布局
#bodyLayout() {
  > form.layui-form.layui-card {
    margin: 0;
    box-shadow: @ShadowOuterMax;

    > .layui-card-body {
      padding: 40px 40px 10px 40px;
    }
  }
}

// 图片背景容器
#imageBgCover() {
  overflow: hidden;
  position: relative;
  background: none no-repeat center center;
  vertical-align: middle;
  background-size: cover;
}

// Layui 字体图标
#iconLayout(@size:15px) {
  font-family: layui-icon !important;
  font-size: @size;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 禁止选中内容
#notSelect() {
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none
}

// Flex 基础定位
#flex() {
  display: -moz-box;
  display: -moz-flex;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

// Flex 换行方式
#flexWrap(@type:wrap) {
  -ms-flex-wrap: @type;
  -webkit-flex-wrap: @type;
  flex-wrap: @type;
}

// Flex 垂直方向
#flexAlign(@type) {
  -ms-flex-align: @type;
  -webkit-box-align: @type;
  -webkit-align-items: @type;
  align-items: @type;
}

//  Flex 横向布局
#flexJustify(@type) {
  -ms-flex-pack: @type;
  -webkit-box-pack: @type;
  -webkit-justify-content: @type;
  justify-content: @type;
}

// 主轴方向
#flexDirection(@type:row) {
  -webkit-box-orient: vertical;
  -ms-flex-direction: @type;
  -webkit-flex-direction: @type;
  flex-direction: @type;
}