<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card">
    <div class="layui-card-body padding-top-20">

        <div class="color-text layui-code text-center layui-bg-gray" style="border-left-width:1px;margin:0 0 15px 40px">
            <p class="margin-bottom-5 font-w7">文件将上传到 <a target="_blank" href="https://www.aliyun.com/minisite/goods?taskCode=shareNew2205&recordId=3646641&userCode=ztlqlu4v">阿里云</a> OSS 存储，需要配置 OSS 公开访问及跨域策略</p>
            <p>配置跨域访问 CORS 规则，设置：来源 Origin 为 *，允许 Methods 为 POST，允许 Headers 为 *</p>
        </div>

        {include file='config/storage-0'}

        <div class="layui-form-item">
            <label class="layui-form-label label-required">
                <b class="color-green">访问协议</b><br><span class="nowrap color-desc">Protocol</span>
            </label>
            <div class="layui-input-block">
                {if !sysconf('storage.alioss_http_protocol')}{php}sysconf('storage.alioss_http_protocol','http');{/php}{/if}
                <div class="layui-input help-checks">
                    {foreach ['http'=>'HTTP','https'=>'HTTPS','auto'=>"AUTO"] as $protocol=>$remark}
                    <label class="think-radio">
                        {if sysconf('storage.alioss_http_protocol') eq $protocol}
                        <input checked type="radio" name="storage.alioss_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {else}
                        <input type="radio" name="storage.alioss_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {/if}
                    </label>
                    {/foreach}
                </div>
                <p class="help-block">阿里云OSS存储访问协议，其中 HTTPS 需要配置证书才能使用（AUTO 为相对协议）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">
                <b class="color-green">存储区域</b><br><span class="nowrap color-desc label-required">Region</span>
            </label>
            <div class="layui-input-block">
                <select class="layui-select" name="storage.alioss_point" lay-search>
                    {foreach $points as $point => $title}
                    {if sysconf('storage.alioss_point') eq $point}
                    <option selected value="{$point}">{$title}（ {$point} ）</option>
                    {else}
                    <option value="{$point}">{$title}（ {$point} ）</option>
                    {/if}{/foreach}
                </select>
                <p class="help-block">阿里云OSS存储空间所在区域，需要严格对应储存所在区域才能上传文件</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alioss_bucket">
                <b class="color-green">空间名称</b><br><span class="nowrap color-desc">Bucket</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alioss_bucket" type="text" name="storage.alioss_bucket" value="{:sysconf('storage.alioss_bucket')}" required vali-name="空间名称" placeholder="请输入阿里云OSS存储 Bucket (空间名称)" class="layui-input">
                <p class="help-block">填写阿里云OSS存储空间名称，如：think-admin-oss（需要是全区唯一的值，不存在时会自动创建）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alioss_http_domain">
                <b class="color-green">访问域名</b><br><span class="nowrap color-desc">Domain</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alioss_http_domain" type="text" name="storage.alioss_http_domain" value="{:sysconf('storage.alioss_http_domain')}" required vali-name="访问域名" placeholder="请输入阿里云OSS存储 Domain (访问域名)" class="layui-input">
                <p class="help-block">填写阿里云OSS存储外部访问域名，不需要填写访问协议，如：static.alioss.thinkadmin.top</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alioss_access_key">
                <b class="color-green">访问密钥</b><br><span class="nowrap color-desc">AccessKey</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alioss_access_key" type="text" name="storage.alioss_access_key" value="{:sysconf('storage.alioss_access_key')}" required vali-name="访问密钥" placeholder="请输入阿里云OSS存储 AccessKey (访问密钥)" class="layui-input">
                <p class="help-block">可以在 [ 阿里云 > 个人中心 ] 设置并获取到访问密钥</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alioss_secret_key">
                <b class="color-green">安全密钥</b><br><span class="nowrap color-desc">SecretKey</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alioss_secret_key" type="text" name="storage.alioss_secret_key" value="{:sysconf('storage.alioss_secret_key')}" maxlength="43" required vali-name="安全密钥" placeholder="请输入阿里云OSS存储 SecretKey (安全密钥)" class="layui-input">
                <p class="help-block">可以在 [ 阿里云 > 个人中心 ] 设置并获取到安全密钥</p>
            </div>
        </div>

        <div class="hr-line-dashed margin-left-40"></div>
        <input type="hidden" name="storage.type" value="alioss">

        <div class="layui-form-item text-center padding-left-40">
            <button class="layui-btn" type="submit">保存配置</button>
            <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消修改吗？" data-close>取消修改</button>
        </div>

    </div>
</form>