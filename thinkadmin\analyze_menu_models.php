<?php

/**
 * 分析当前模块的模型和菜单结构
 * 更新菜单的链接和权限节点信息
 */

$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 当前菜单结构分析 ===\n\n";
    
    // 获取所有菜单数据
    $stmt = $pdo->query("SELECT id, pid, title, node, url, icon, sort, status FROM system_menu ORDER BY sort, id");
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 分析控制器和模型的对应关系
    $controllerDir = __DIR__ . '/app/admin/controller';
    $modelDir = __DIR__ . '/app/admin/model';
    
    $controllers = [];
    $models = [];
    
    // 扫描控制器文件
    if (is_dir($controllerDir)) {
        $controllerFiles = glob($controllerDir . '/*.php');
        foreach ($controllerFiles as $file) {
            $basename = basename($file, '.php');
            $controllers[] = $basename;
        }
    }
    
    // 扫描模型文件
    if (is_dir($modelDir)) {
        $modelFiles = glob($modelDir . '/*.php');
        foreach ($modelFiles as $file) {
            $basename = basename($file, '.php');
            $models[] = $basename;
        }
    }
    
    echo "发现的控制器: " . count($controllers) . " 个\n";
    echo "发现的模型: " . count($models) . " 个\n\n";
    
    // 定义完整的菜单结构和对应关系
    $menuStructure = [
        '写作中心' => [
            'icon' => 'layui-icon layui-icon-edit',
            'sort' => 100,
            'children' => [
                '论文类型管理' => [
                    'controller' => 'PaperType',
                    'model' => 'PaperType',
                    'node' => 'admin/paper_type/index',
                    'icon' => 'layui-icon layui-icon-template-1'
                ],
                '大纲模板管理' => [
                    'controller' => 'OutlineTemplate',
                    'model' => 'OutlineTemplate',
                    'node' => 'admin/outline_template/index',
                    'icon' => 'layui-icon layui-icon-list'
                ],
                '提示词模板管理' => [
                    'controller' => 'PromptTemplate',
                    'model' => 'PromptTemplate',
                    'node' => 'admin/prompt_template/index',
                    'icon' => 'layui-icon layui-icon-dialogue'
                ],
                '正文模板管理' => [
                    'controller' => 'ContentTemplate',
                    'model' => 'DocumentTemplate',
                    'node' => 'admin/content_template/index',
                    'icon' => 'layui-icon layui-icon-file-b'
                ],
                '写作任务管理' => [
                    'controller' => 'PaperProject',
                    'model' => 'PaperProject',
                    'node' => 'admin/paper_project/index',
                    'icon' => 'layui-icon layui-icon-file'
                ],
                '草稿箱管理' => [
                    'controller' => 'PaperProject',
                    'model' => 'PaperProject',
                    'node' => 'admin/paper_project/draft',
                    'icon' => 'layui-icon layui-icon-survey'
                ]
            ]
        ],
        '降重与查重' => [
            'icon' => 'layui-icon layui-icon-refresh-3',
            'sort' => 200,
            'children' => [
                '降重记录管理' => [
                    'controller' => 'RewriteRecord',
                    'model' => 'RewriteResult',
                    'node' => 'admin/rewrite_record/index',
                    'icon' => 'layui-icon layui-icon-log'
                ],
                '降重模型配置' => [
                    'controller' => 'RewriteModel',
                    'model' => 'AiModel',
                    'node' => 'admin/rewrite_model/index',
                    'icon' => 'layui-icon layui-icon-engine'
                ],
                '查重记录管理' => [
                    'controller' => 'CheckRecord',
                    'model' => 'CheckTask',
                    'node' => 'admin/check_record/index',
                    'icon' => 'layui-icon layui-icon-search'
                ],
                '降重任务管理' => [
                    'controller' => 'RewriteTask',
                    'model' => 'RewriteTask',
                    'node' => 'admin/rewrite_task/index',
                    'icon' => 'layui-icon layui-icon-component'
                ]
            ]
        ],
        '文档导出' => [
            'icon' => 'layui-icon layui-icon-export',
            'sort' => 300,
            'children' => [
                '导出样式模板' => [
                    'controller' => 'ExportTemplate',
                    'model' => 'DocumentTemplate',
                    'node' => 'admin/export_template/index',
                    'icon' => 'layui-icon layui-icon-template'
                ],
                '导出任务监控' => [
                    'controller' => 'ExportMonitor',
                    'model' => null,
                    'node' => 'admin/export_monitor/index',
                    'icon' => 'layui-icon layui-icon-chart'
                ],
                '发票管理' => [
                    'controller' => 'Invoice',
                    'model' => 'Invoice',
                    'node' => 'admin/invoice/index',
                    'icon' => 'layui-icon layui-icon-file-b'
                ]
            ]
        ],
        '用户中心' => [
            'icon' => 'layui-icon layui-icon-user',
            'sort' => 400,
            'children' => [
                '用户列表' => [
                    'controller' => 'User',
                    'model' => null,
                    'node' => 'admin/user/index',
                    'icon' => 'layui-icon layui-icon-username'
                ],
                'VIP套餐管理' => [
                    'controller' => 'VipPackage',
                    'model' => 'Package',
                    'node' => 'admin/vip_package/index',
                    'icon' => 'layui-icon layui-icon-diamond'
                ],
                '用户积分管理' => [
                    'controller' => 'UserPoints',
                    'model' => null,
                    'node' => 'admin/user_points/index',
                    'icon' => 'layui-icon layui-icon-star-fill'
                ]
            ]
        ],
        '收费系统' => [
            'icon' => 'layui-icon layui-icon-rmb',
            'sort' => 500,
            'children' => [
                '订单管理' => [
                    'controller' => 'Order',
                    'model' => 'Order',
                    'node' => 'admin/order/index',
                    'icon' => 'layui-icon layui-icon-cart-simple'
                ],
                '套餐配置' => [
                    'controller' => 'PackageConfig',
                    'model' => 'Package',
                    'node' => 'admin/package_config/index',
                    'icon' => 'layui-icon layui-icon-set'
                ],
                '充值记录' => [
                    'controller' => 'RechargeRecord',
                    'model' => 'Order',
                    'node' => 'admin/recharge_record/index',
                    'icon' => 'layui-icon layui-icon-dollar'
                ]
            ]
        ],
        '通知与消息' => [
            'icon' => 'layui-icon layui-icon-notice',
            'sort' => 600,
            'children' => [
                '系统通知记录' => [
                    'controller' => 'SystemNotice',
                    'model' => null,
                    'node' => 'admin/system_notice/index',
                    'icon' => 'layui-icon layui-icon-speaker'
                ],
                '消息模板管理' => [
                    'controller' => 'MessageTemplate',
                    'model' => 'MessageTemplate',
                    'node' => 'admin/message_template/index',
                    'icon' => 'layui-icon layui-icon-template-1'
                ],
                '邮件配置' => [
                    'controller' => 'EmailConfig',
                    'model' => null,
                    'node' => 'admin/email_config/index',
                    'icon' => 'layui-icon layui-icon-email'
                ],
                '通知记录' => [
                    'controller' => 'NotificationLog',
                    'model' => null,
                    'node' => 'admin/notification_log/index',
                    'icon' => 'layui-icon layui-icon-log'
                ]
            ]
        ],
        '系统设置' => [
            'icon' => 'layui-icon layui-icon-set',
            'sort' => 700,
            'children' => [
                'AI模型配置' => [
                    'controller' => 'AiModel',
                    'model' => 'AiModel',
                    'node' => 'admin/ai_model/index',
                    'icon' => 'layui-icon layui-icon-engine'
                ],
                'n8n工作流管理' => [
                    'controller' => 'N8nWorkflow',
                    'model' => 'N8nWorkflow',
                    'node' => 'admin/n8n_workflow/index',
                    'icon' => 'layui-icon layui-icon-component'
                ],
                '接口密钥管理' => [
                    'controller' => 'ApiKey',
                    'model' => null,
                    'node' => 'admin/api_key/index',
                    'icon' => 'layui-icon layui-icon-key'
                ],
                'Webhook配置' => [
                    'controller' => 'WebhookConfig',
                    'model' => null,
                    'node' => 'admin/webhook_config/index',
                    'icon' => 'layui-icon layui-icon-link'
                ],
                '内容风控规则' => [
                    'controller' => 'ContentFilter',
                    'model' => null,
                    'node' => 'admin/content_filter/index',
                    'icon' => 'layui-icon layui-icon-vercode'
                ],
                '基础参数设置' => [
                    'controller' => 'BasicConfig',
                    'model' => null,
                    'node' => 'admin/basic_config/index',
                    'icon' => 'layui-icon layui-icon-slider'
                ]
            ]
        ]
    ];
    
    // 分析当前菜单状态
    echo "=== 菜单实现状态分析 ===\n\n";
    
    $totalMenus = 0;
    $implementedMenus = 0;
    $missingControllers = [];
    $missingModels = [];
    
    foreach ($menuStructure as $sectionName => $section) {
        echo "## {$sectionName}\n";
        
        foreach ($section['children'] as $menuName => $menuInfo) {
            $totalMenus++;
            
            $controllerExists = in_array($menuInfo['controller'], $controllers);
            $modelExists = $menuInfo['model'] ? in_array($menuInfo['model'], $models) : true;
            
            $status = '✅';
            if (!$controllerExists) {
                $status = '❌';
                $missingControllers[] = $menuInfo['controller'];
            } elseif (!$modelExists) {
                $status = '⚠️';
                $missingModels[] = $menuInfo['model'];
            } else {
                $implementedMenus++;
            }
            
            echo "  {$status} {$menuName}\n";
            echo "    - 控制器: {$menuInfo['controller']}.php " . ($controllerExists ? '✅' : '❌') . "\n";
            echo "    - 模型: " . ($menuInfo['model'] ? $menuInfo['model'] . '.php' : '无需模型') . " " . ($modelExists ? '✅' : '❌') . "\n";
            echo "    - 节点: {$menuInfo['node']}\n";
            echo "    - 图标: {$menuInfo['icon']}\n\n";
        }
    }
    
    // 统计结果
    $completionRate = round(($implementedMenus / $totalMenus) * 100, 2);
    
    echo "=== 实现状态统计 ===\n";
    echo "总菜单数: {$totalMenus}\n";
    echo "已实现: {$implementedMenus}\n";
    echo "完成率: {$completionRate}%\n\n";
    
    if (!empty($missingControllers)) {
        echo "缺失的控制器:\n";
        foreach (array_unique($missingControllers) as $controller) {
            echo "- {$controller}.php\n";
        }
        echo "\n";
    }
    
    if (!empty($missingModels)) {
        echo "缺失的模型:\n";
        foreach (array_unique($missingModels) as $model) {
            echo "- {$model}.php\n";
        }
        echo "\n";
    }
    
    // 检查当前数据库中的菜单
    echo "=== 当前数据库菜单检查 ===\n";
    
    $menuMap = [];
    foreach ($menus as $menu) {
        if ($menu['pid'] == 0) {
            $menuMap[$menu['title']] = [
                'id' => $menu['id'],
                'info' => $menu,
                'children' => []
            ];
        }
    }
    
    foreach ($menus as $menu) {
        if ($menu['pid'] > 0) {
            foreach ($menuMap as $parentTitle => &$parent) {
                if ($parent['id'] == $menu['pid']) {
                    $parent['children'][] = $menu;
                    break;
                }
            }
        }
    }
    
    foreach ($menuMap as $title => $menuData) {
        echo "\n{$title} (ID: {$menuData['id']})\n";
        foreach ($menuData['children'] as $child) {
            echo "  - {$child['title']} -> {$child['node']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
