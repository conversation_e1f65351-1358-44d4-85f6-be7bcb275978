{extend name='form'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-box" action="{:request()->url()}" data-auto="true" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">模型名称</label>
                <div class="layui-input-block">
                    <input name="name" value="{$vo.name|default=''}" required lay-verify="required" placeholder="请输入模型名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">提供商</label>
                <div class="layui-input-block">
                    <select name="provider" lay-verify="required">
                        <option value="">请选择提供商</option>
                        <option value="openai" {if isset($vo.provider) and $vo.provider eq 'openai'}selected{/if}>OpenAI</option>
                        <option value="baidu" {if isset($vo.provider) and $vo.provider eq 'baidu'}selected{/if}>百度</option>
                        <option value="aliyun" {if isset($vo.provider) and $vo.provider eq 'aliyun'}selected{/if}>阿里云</option>
                        <option value="tencent" {if isset($vo.provider) and $vo.provider eq 'tencent'}selected{/if}>腾讯云</option>
                        <option value="zhipu" {if isset($vo.provider) and $vo.provider eq 'zhipu'}selected{/if}>智谱AI</option>
                        <option value="moonshot" {if isset($vo.provider) and $vo.provider eq 'moonshot'}selected{/if}>Moonshot</option>
                        <option value="other" {if isset($vo.provider) and $vo.provider eq 'other'}selected{/if}>其他</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">模型代码</label>
                <div class="layui-input-block">
                    <input name="model_code" value="{$vo.model_code|default=''}" required lay-verify="required" placeholder="请输入模型代码，如：gpt-3.5-turbo" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">API调用时使用的模型标识</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">API地址</label>
                <div class="layui-input-block">
                    <input name="api_url" value="{$vo.api_url|default=''}" required lay-verify="required" placeholder="请输入API地址" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">API密钥</label>
                <div class="layui-input-block">
                    <input name="api_key" value="{$vo.api_key|default=''}" required lay-verify="required" placeholder="请输入API密钥" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">最大Token</label>
                <div class="layui-input-block">
                    <input name="max_tokens" value="{$vo.max_tokens|default='4096'}" required lay-verify="required|number" placeholder="请输入最大Token数" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">模型支持的最大Token数量</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">输入成本</label>
                <div class="layui-input-block">
                    <input name="input_cost" value="{$vo.input_cost|default='0.000000'}" required lay-verify="required" placeholder="请输入每1K Token的输入成本" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">每1000个输入Token的成本（元）</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">输出成本</label>
                <div class="layui-input-block">
                    <input name="output_cost" value="{$vo.output_cost|default='0.000000'}" required lay-verify="required" placeholder="请输入每1K Token的输出成本" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">每1000个输出Token的成本（元）</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">优先级</label>
                <div class="layui-input-block">
                    <input name="priority" value="{$vo.priority|default='0'}" required lay-verify="required|number" placeholder="请输入优先级" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">数值越大优先级越高</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">超时时间</label>
                <div class="layui-input-block">
                    <input name="timeout" value="{$vo.timeout|default='30'}" required lay-verify="required|number" placeholder="请输入超时时间" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">API请求超时时间（秒）</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">重试次数</label>
                <div class="layui-input-block">
                    <input name="retry_count" value="{$vo.retry_count|default='3'}" required lay-verify="required|number" placeholder="请输入重试次数" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">请求失败时的重试次数</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">模型描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入模型描述" class="layui-textarea">{$vo.description|default=''}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">配置参数</label>
                <div class="layui-input-block">
                    <textarea name="config" placeholder="请输入JSON格式的配置参数" class="layui-textarea">{$vo.config|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">JSON格式的额外配置参数，如：{"temperature": 0.7, "top_p": 1}</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset($vo.status) or $vo.status eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset($vo.status) and $vo.status eq 0}checked{/if}>
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-form-item text-center">
                {if empty($vo.id)}
                <button class="layui-btn" type='submit'>添加模型</button>
                {else}
                <button class="layui-btn" type='submit'>编辑模型</button>
                {/if}
                <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
            </div>

        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.use(['form'], function () {
        var form = layui.form;
        
        // 监听提供商选择
        form.on('select(provider)', function(data){
            var provider = data.value;
            var apiUrlInput = $('input[name="api_url"]');
            var modelCodeInput = $('input[name="model_code"]');
            
            // 根据提供商设置默认值
            switch(provider) {
                case 'openai':
                    apiUrlInput.val('https://api.openai.com/v1/chat/completions');
                    modelCodeInput.val('gpt-3.5-turbo');
                    break;
                case 'baidu':
                    apiUrlInput.val('https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions');
                    modelCodeInput.val('ernie-bot-turbo');
                    break;
                case 'zhipu':
                    apiUrlInput.val('https://open.bigmodel.cn/api/paas/v4/chat/completions');
                    modelCodeInput.val('glm-4');
                    break;
                case 'moonshot':
                    apiUrlInput.val('https://api.moonshot.cn/v1/chat/completions');
                    modelCodeInput.val('moonshot-v1-8k');
                    break;
            }
        });
    });
</script>
{/block}
