<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\User;
use app\admin\model\UserCredit;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 用户积分管理
 * @class UserPoints
 * @package app\admin\controller
 */
class UserPoints extends Controller
{
    /**
     * 用户积分管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        UserCredit::mQuery()->layTable(function () {
            $this->title = '用户积分管理';
            $this->typeOptions = UserCredit::getTypeOptions();
            $this->statusOptions = UserCredit::getStatusOptions();
        }, static function (QueryHelper $query) {
            $query->with(['user']);
            $query->like('user.username,user.nickname,description')->equal('type,status');
            $query->dateBetween('create_time,update_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加积分记录
     * @auth true
     */
    public function add()
    {
        UserCredit::mForm('form');
    }

    /**
     * 编辑积分记录
     * @auth true
     */
    public function edit()
    {
        UserCredit::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = UserCredit::getTypeOptions();
            $this->statusOptions = UserCredit::getStatusOptions();
            
            // 获取用户列表
            $this->userOptions = User::mk()
                ->where('status', 1)
                ->field('id,username,nickname')
                ->order('id desc')
                ->limit(100)
                ->select()
                ->toArray();
        } else {
            // 验证必要字段
            if (empty($vo['user_id'])) {
                $this->error('请选择用户！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择积分类型！');
            }
            
            if (!isset($vo['amount']) || $vo['amount'] == 0) {
                $this->error('积分数量不能为0！');
            }
            
            // 验证用户是否存在
            $user = User::mk()->findOrEmpty($vo['user_id']);
            if ($user->isEmpty()) {
                $this->error('用户不存在！');
            }
            
            // 如果是扣减积分，检查用户余额
            if ($vo['amount'] < 0) {
                $currentBalance = $user->credits ?: 0;
                if ($currentBalance + $vo['amount'] < 0) {
                    $this->error('用户积分余额不足！当前余额：' . $currentBalance);
                }
            }
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            // 更新用户积分余额
            $creditRecord = UserCredit::mk()->order('id desc')->findOrEmpty();
            if (!$creditRecord->isEmpty()) {
                $user = User::mk()->findOrEmpty($creditRecord->user_id);
                if (!$user->isEmpty()) {
                    $newBalance = ($user->credits ?: 0) + $creditRecord->amount;
                    $user->save(['credits' => $newBalance]);
                }
            }
            
            $this->success('积分记录保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除积分记录
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('记录ID不能为空！');
        }

        $record = UserCredit::mk()->findOrEmpty($id);
        if ($record->isEmpty()) {
            $this->error('记录不存在！');
        }

        // 检查记录状态
        if ($record->status == 'processed') {
            $this->error('已处理的记录不能删除！');
        }

        if ($record->delete()) {
            $this->success('记录删除成功！');
        } else {
            $this->error('记录删除失败！');
        }
    }

    /**
     * 修改记录状态
     * @auth true
     */
    public function state()
    {
        UserCredit::mSave($this->_vali([
            'status.in:pending,processed,cancelled'  => '状态值范围异常！',
        ]));
    }

    /**
     * 批量充值积分
     * @auth true
     */
    public function batchRecharge()
    {
        if ($this->request->isGet()) {
            $this->title = '批量充值积分';
            return $this->fetch('user_points/batch_recharge');
        }

        $userIds = $this->request->post('user_ids', '');
        $amount = $this->request->post('amount', 0);
        $description = $this->request->post('description', '');

        if (empty($userIds)) {
            $this->error('请选择用户！');
        }

        if ($amount <= 0) {
            $this->error('充值金额必须大于0！');
        }

        $userIdArray = explode(',', $userIds);
        $successCount = 0;
        $failCount = 0;

        foreach ($userIdArray as $userId) {
            $user = User::mk()->findOrEmpty($userId);
            if ($user->isEmpty()) {
                $failCount++;
                continue;
            }

            // 创建积分记录
            $creditData = [
                'user_id' => $userId,
                'type' => 'recharge',
                'amount' => $amount,
                'description' => $description ?: '批量充值',
                'status' => 'processed',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $result = UserCredit::mk()->save($creditData);
            if ($result) {
                // 更新用户积分
                $newBalance = ($user->credits ?: 0) + $amount;
                $user->save(['credits' => $newBalance]);
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量充值完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 积分统计
     * @auth true
     */
    public function statistics()
    {
        // 总积分记录数
        $totalRecords = UserCredit::mk()->count();
        
        // 今日积分记录数
        $todayRecords = UserCredit::mk()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月积分记录数
        $monthRecords = UserCredit::mk()
            ->whereTime('create_time', 'month')
            ->count();
        
        // 总充值积分
        $totalRecharge = UserCredit::mk()
            ->where('type', 'recharge')
            ->where('amount', '>', 0)
            ->sum('amount') ?: 0;
        
        // 总消费积分
        $totalConsume = UserCredit::mk()
            ->where('type', 'consume')
            ->where('amount', '<', 0)
            ->sum('amount') ?: 0;
        $totalConsume = abs($totalConsume);
        
        // 今日充值积分
        $todayRecharge = UserCredit::mk()
            ->where('type', 'recharge')
            ->where('amount', '>', 0)
            ->whereTime('create_time', 'today')
            ->sum('amount') ?: 0;
        
        // 今日消费积分
        $todayConsume = UserCredit::mk()
            ->where('type', 'consume')
            ->where('amount', '<', 0)
            ->whereTime('create_time', 'today')
            ->sum('amount') ?: 0;
        $todayConsume = abs($todayConsume);
        
        // 积分类型分布
        $typeStats = UserCredit::mk()
            ->field('type, COUNT(*) as count, SUM(amount) as total_amount')
            ->group('type')
            ->select()
            ->toArray();
        
        // 用户积分排行榜（前10）
        $topUsers = User::mk()
            ->field('id,username,nickname,credits')
            ->where('credits', '>', 0)
            ->order('credits desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 最近7天积分趋势
        $weekTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $recharge = UserCredit::mk()
                ->where('type', 'recharge')
                ->whereTime('create_time', $date)
                ->sum('amount') ?: 0;
            $consume = UserCredit::mk()
                ->where('type', 'consume')
                ->whereTime('create_time', $date)
                ->sum('amount') ?: 0;
            $consume = abs($consume);
            
            $weekTrend[] = [
                'date' => $date,
                'recharge' => $recharge,
                'consume' => $consume
            ];
        }

        $statistics = [
            'total_records' => $totalRecords,
            'today_records' => $todayRecords,
            'month_records' => $monthRecords,
            'total_recharge' => $totalRecharge,
            'total_consume' => $totalConsume,
            'today_recharge' => $todayRecharge,
            'today_consume' => $todayConsume,
            'type_stats' => $typeStats,
            'top_users' => $topUsers,
            'week_trend' => $weekTrend
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '积分统计');
        return $this->fetch('user_points/statistics');
    }

    /**
     * 用户积分详情
     * @auth true
     */
    public function userDetail()
    {
        $userId = $this->request->get('user_id', 0);
        if (empty($userId)) {
            $this->error('用户ID不能为空！');
        }

        $user = User::mk()->findOrEmpty($userId);
        if ($user->isEmpty()) {
            $this->error('用户不存在！');
        }

        // 获取用户积分记录
        $records = UserCredit::mk()
            ->where('user_id', $userId)
            ->order('id desc')
            ->paginate(20);

        // 用户积分统计
        $userStats = [
            'current_balance' => $user->credits ?: 0,
            'total_recharge' => UserCredit::mk()
                ->where('user_id', $userId)
                ->where('type', 'recharge')
                ->sum('amount') ?: 0,
            'total_consume' => abs(UserCredit::mk()
                ->where('user_id', $userId)
                ->where('type', 'consume')
                ->sum('amount') ?: 0),
            'record_count' => UserCredit::mk()
                ->where('user_id', $userId)
                ->count()
        ];

        $this->assign('user', $user);
        $this->assign('records', $records);
        $this->assign('userStats', $userStats);
        $this->assign('title', '用户积分详情');
        
        return $this->fetch('user_points/user_detail');
    }

    /**
     * 积分调整
     * @auth true
     */
    public function adjust()
    {
        if ($this->request->isGet()) {
            $userId = $this->request->get('user_id', 0);
            if (!empty($userId)) {
                $user = User::mk()->findOrEmpty($userId);
                $this->assign('user', $user);
            }
            
            $this->title = '积分调整';
            return $this->fetch('user_points/adjust');
        }

        $userId = $this->request->post('user_id', 0);
        $adjustType = $this->request->post('adjust_type', '');
        $amount = $this->request->post('amount', 0);
        $reason = $this->request->post('reason', '');

        if (empty($userId)) {
            $this->error('请选择用户！');
        }

        if (empty($adjustType) || !in_array($adjustType, ['increase', 'decrease'])) {
            $this->error('请选择调整类型！');
        }

        if ($amount <= 0) {
            $this->error('调整金额必须大于0！');
        }

        if (empty($reason)) {
            $this->error('请填写调整原因！');
        }

        $user = User::mk()->findOrEmpty($userId);
        if ($user->isEmpty()) {
            $this->error('用户不存在！');
        }

        // 计算调整后的积分
        $currentBalance = $user->credits ?: 0;
        $adjustAmount = $adjustType == 'increase' ? $amount : -$amount;
        $newBalance = $currentBalance + $adjustAmount;

        if ($newBalance < 0) {
            $this->error('调整后积分不能为负数！');
        }

        // 创建积分记录
        $creditData = [
            'user_id' => $userId,
            'type' => $adjustType == 'increase' ? 'adjust_increase' : 'adjust_decrease',
            'amount' => $adjustAmount,
            'description' => '管理员调整：' . $reason,
            'status' => 'processed',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];

        $result = UserCredit::mk()->save($creditData);
        if ($result) {
            // 更新用户积分
            $user->save(['credits' => $newBalance]);
            $this->success('积分调整成功！');
        } else {
            $this->error('积分调整失败！');
        }
    }

    /**
     * 导出积分记录
     * @auth true
     */
    public function export()
    {
        $startDate = $this->request->post('start_date', '');
        $endDate = $this->request->post('end_date', '');
        $type = $this->request->post('type', '');

        $query = UserCredit::mk()->with(['user']);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }

        if (!empty($type)) {
            $query->where('type', $type);
        }

        $records = $query->order('id desc')->select();

        if ($records->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '用户名', '用户昵称', '积分类型', '积分数量', '描述', '状态', '创建时间'];
        
        foreach ($records as $record) {
            $exportData[] = [
                $record->id,
                $record->user->username ?? '',
                $record->user->nickname ?? '',
                UserCredit::getTypeOptions()[$record->type] ?? $record->type,
                $record->amount,
                $record->description,
                UserCredit::getStatusOptions()[$record->status] ?? $record->status,
                $record->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
}
