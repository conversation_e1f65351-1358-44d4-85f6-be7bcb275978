<?php

/**
 * 批量修复模板语法错误
 */

echo "=== 批量修复模板语法 ===\n\n";

// 需要修复的文件列表
$filesToFix = [
    'app/admin/view/content_template/index.html',
    'app/admin/view/prompt_template/index.html',
    'app/admin/view/check_record/index.html',
    'app/admin/view/rewrite_record/index.html',
    'app/admin/view/content_template/form.html',
    'app/admin/view/prompt_template/form.html'
];

$fixedCount = 0;
$errorCount = 0;

foreach ($filesToFix as $file) {
    $fullPath = __DIR__ . '/' . $file;
    
    if (!file_exists($fullPath)) {
        echo "❌ 文件不存在: {$file}\n";
        $errorCount++;
        continue;
    }
    
    $content = file_get_contents($fullPath);
    $originalContent = $content;
    
    // 修复各种语法错误
    $patterns = [
        // 修复 ?? '' 语法 - 在ThinkPHP中应该使用 isset() 或 empty() 判断
        '/\$get\.([a-zA-Z_]+)\s*\?\?\s*\'\'/' => 'isset($get.$1) ? $get.$1 : \'\'',
        
        // 修复条件判断语法
        '/\{if\s+\$get\.([a-zA-Z_]+)\s*\?\?\s*\'\'\s+eq\s+\'([^\']*)\'\}/' => '{if isset($get.$1) && $get.$1 eq \'$2\'}',
        '/\{if\s+\$get\.([a-zA-Z_]+)\s*\?\?\s*\'\'\s+eq\s+([^}]+)\}/' => '{if isset($get.$1) && $get.$1 eq $2}',
        
        // 修复三元运算符
        '/\$vo\.([a-zA-Z_]+)\s*\?\?\s*\'\'\s*\?\s*/' => 'isset($vo.$1) && $vo.$1 ? ',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // 如果内容有变化，保存文件
    if ($content !== $originalContent) {
        if (file_put_contents($fullPath, $content)) {
            echo "✅ 修复成功: {$file}\n";
            $fixedCount++;
        } else {
            echo "❌ 修复失败: {$file}\n";
            $errorCount++;
        }
    } else {
        echo "⚠️  无需修复: {$file}\n";
    }
}

echo "\n=== 修复完成 ===\n";
echo "成功修复: {$fixedCount} 个文件\n";
echo "修复失败: {$errorCount} 个文件\n";

// 清理模板缓存
$tempDir = __DIR__ . '/runtime/temp';
if (is_dir($tempDir)) {
    $files = glob($tempDir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    echo "\n✅ 已清理模板缓存\n";
}

echo "\n建议：如果仍有语法错误，请检查ThinkPHP版本兼容性\n";
