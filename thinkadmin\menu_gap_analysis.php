<?php

// 对比 menu.md 与当前实现的差距分析
echo "=== menu.md 与当前实现对比分析 ===\n\n";

// menu.md 中定义的完整模块结构
$menuMdStructure = [
    '写作中心' => [
        '论文类型管理' => 'admin/paper_type/index',
        '大纲模板管理' => 'admin/outline_template/index', 
        '提示词模板管理' => 'admin/prompt_template/index',
        '正文模板管理' => 'admin/content_template/index',  // 缺失
        '写作任务管理' => 'admin/paper_project/index',
        '草稿箱管理' => 'admin/paper_project/draft'
    ],
    '降重与查重' => [
        '降重记录管理' => 'admin/rewrite_record/index',    // 缺失
        '降重模型配置' => 'admin/rewrite_model/index',     // 缺失
        '查重记录管理' => 'admin/check_record/index',      // 缺失
        '查重接口配置' => 'admin/check_api/index',         // 缺失
        '降重任务管理' => 'admin/rewrite_task/index'       // 已存在
    ],
    '文档导出' => [
        '导出样式模板' => 'admin/export_template/index',   // 缺失
        '下载记录管理' => 'admin/download_record/index',   // 缺失
        '导出任务监控' => 'admin/export_task/index'        // 缺失
    ],
    '用户中心' => [
        '用户列表' => 'admin/user/index',                  // 已存在
        'VIP套餐管理' => 'admin/vip_package/index',        // 缺失
        '用户积分管理' => 'admin/user_credit/index'         // 缺失
    ],
    '收费系统' => [
        '订单管理' => 'admin/order/index',                 // 缺失
        '套餐配置' => 'admin/package_config/index',        // 缺失
        '充值记录' => 'admin/recharge_record/index',       // 缺失
        '发票管理' => 'admin/invoice/index'                // 已存在
    ],
    '通知与消息' => [
        '系统通知记录' => 'admin/system_notice/index',     // 缺失
        '消息模板管理' => 'admin/message_template/index',  // 已存在
        '邮件配置' => 'admin/email_config/index',          // 缺失
        '通知记录' => 'admin/notification_log/index'       // 缺失
    ],
    '写作系统相关设置' => [
        'AI模型配置' => 'admin/ai_model/index',            // 已存在
        '接口密钥管理' => 'admin/api_key/index',           // 缺失
        'Webhook配置' => 'admin/webhook_config/index',     // 缺失
        '内容风控规则' => 'admin/content_filter/index',    // 缺失
        '基础参数设置' => 'admin/system_config/index',     // 缺失
        'n8n工作流管理' => 'admin/n8n_workflow/index'      // 已存在
    ]
];

// 当前已存在的控制器
$existingControllers = [
    'AiModel', 'Auth', 'Base', 'Config', 'File', 'Index', 'Invoice', 
    'Login', 'Menu', 'MessageTemplate', 'N8nWorkflow', 'Oplog', 
    'OutlineTemplate', 'PaperProject', 'PaperType', 'PromptTemplate', 
    'Queue', 'RewriteTask', 'User'
];

echo "📋 缺失模块分析：\n\n";

$missingModules = [];
$existingModules = [];

foreach ($menuMdStructure as $category => $modules) {
    echo "## {$category}\n";
    foreach ($modules as $moduleName => $route) {
        $controllerName = ucfirst(str_replace('_', '', ucwords(explode('/', $route)[1], '_')));
        
        if (in_array($controllerName, $existingControllers)) {
            echo "✅ {$moduleName} -> {$controllerName}.php (已存在)\n";
            $existingModules[] = $moduleName;
        } else {
            echo "❌ {$moduleName} -> {$controllerName}.php (缺失)\n";
            $missingModules[] = [
                'name' => $moduleName,
                'controller' => $controllerName,
                'route' => $route,
                'category' => $category
            ];
        }
    }
    echo "\n";
}

echo "📊 统计结果：\n";
echo "- 已存在模块：" . count($existingModules) . " 个\n";
echo "- 缺失模块：" . count($missingModules) . " 个\n\n";

echo "🔧 需要创建的控制器：\n";
foreach ($missingModules as $module) {
    echo "- {$module['controller']}.php ({$module['name']})\n";
}

echo "\n📝 优先级建议：\n";
echo "1. 高优先级（写作中心）：\n";
echo "   - ContentTemplate.php (正文模板管理)\n\n";

echo "2. 中优先级（核心业务）：\n";
echo "   - RewriteRecord.php (降重记录管理)\n";
echo "   - CheckRecord.php (查重记录管理)\n";
echo "   - ExportTemplate.php (导出样式模板)\n";
echo "   - VipPackage.php (VIP套餐管理)\n";
echo "   - Order.php (订单管理)\n\n";

echo "3. 低优先级（配置类）：\n";
echo "   - ApiKey.php (接口密钥管理)\n";
echo "   - WebhookConfig.php (Webhook配置)\n";
echo "   - ContentFilter.php (内容风控规则)\n";
echo "   - SystemConfig.php (基础参数设置)\n";
