<?php

// 添加缺失的菜单项到系统菜单

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "数据库文件不存在: $dbPath\n";
        exit(1);
    }

    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取当前最大ID
    $stmt = $pdo->query("SELECT MAX(id) as max_id FROM system_menu");
    $maxId = $stmt->fetch(PDO::FETCH_ASSOC)['max_id'] ?? 0;
    $currentId = $maxId + 1;

    // 查找各个父级菜单ID
    $parentMenus = [
        '降重与查重' => null,
        '文档导出' => null,
        '用户中心' => null,
        '收费系统' => null,
        '通知与消息' => null,
        '系统设置' => null
    ];

    foreach ($parentMenus as $title => $id) {
        $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = 0");
        $stmt->execute([$title]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $parentMenus[$title] = $result['id'];
        }
    }

    // 要添加的菜单项
    $menusToAdd = [
        // 降重与查重模块
        [
            'pid' => $parentMenus['降重与查重'],
            'title' => '降重记录管理',
            'icon' => 'layui-icon layui-icon-file',
            'node' => 'admin/rewrite_record/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 102,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['降重与查重'],
            'title' => '查重记录管理',
            'icon' => 'layui-icon layui-icon-search',
            'node' => 'admin/check_record/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 104,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['降重与查重'],
            'title' => '降重模型配置',
            'icon' => 'layui-icon layui-icon-set',
            'node' => 'admin/rewrite_model/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 106,
            'status' => 1
        ],
        
        // 文档导出模块
        [
            'pid' => $parentMenus['文档导出'],
            'title' => '导出任务监控',
            'icon' => 'layui-icon layui-icon-chart',
            'node' => 'admin/export_monitor/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 302,
            'status' => 1
        ],
        
        // 用户中心模块
        [
            'pid' => $parentMenus['用户中心'],
            'title' => 'VIP套餐管理',
            'icon' => 'layui-icon layui-icon-vip',
            'node' => 'admin/vip_package/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 402,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['用户中心'],
            'title' => '用户积分管理',
            'icon' => 'layui-icon layui-icon-diamond',
            'node' => 'admin/user_points/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 404,
            'status' => 1
        ],
        
        // 收费系统模块
        [
            'pid' => $parentMenus['收费系统'],
            'title' => '订单管理',
            'icon' => 'layui-icon layui-icon-form',
            'node' => 'admin/order/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 502,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['收费系统'],
            'title' => '套餐配置',
            'icon' => 'layui-icon layui-icon-set',
            'node' => 'admin/package_config/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 504,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['收费系统'],
            'title' => '充值记录',
            'icon' => 'layui-icon layui-icon-rmb',
            'node' => 'admin/recharge_record/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 506,
            'status' => 1
        ],
        
        // 通知与消息模块
        [
            'pid' => $parentMenus['通知与消息'],
            'title' => '系统通知记录',
            'icon' => 'layui-icon layui-icon-notice',
            'node' => 'admin/system_notice/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 602,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['通知与消息'],
            'title' => '邮件配置',
            'icon' => 'layui-icon layui-icon-email',
            'node' => 'admin/email_config/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 604,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['通知与消息'],
            'title' => '通知记录',
            'icon' => 'layui-icon layui-icon-log',
            'node' => 'admin/notification_log/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 606,
            'status' => 1
        ],
        
        // 系统设置模块
        [
            'pid' => $parentMenus['系统设置'],
            'title' => '接口密钥管理',
            'icon' => 'layui-icon layui-icon-key',
            'node' => 'admin/api_key/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 702,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['系统设置'],
            'title' => 'Webhook配置',
            'icon' => 'layui-icon layui-icon-link',
            'node' => 'admin/webhook_config/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 704,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['系统设置'],
            'title' => '内容风控规则',
            'icon' => 'layui-icon layui-icon-vercode',
            'node' => 'admin/content_filter/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 706,
            'status' => 1
        ],
        [
            'pid' => $parentMenus['系统设置'],
            'title' => '基础参数设置',
            'icon' => 'layui-icon layui-icon-set-fill',
            'node' => 'admin/basic_config/index',
            'url' => '',
            'params' => '',
            'target' => '_self',
            'sort' => 708,
            'status' => 1
        ]
    ];

    // 准备插入语句
    $insertSql = "INSERT INTO system_menu (id, pid, title, icon, node, url, params, target, sort, status, create_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($insertSql);

    $addedCount = 0;
    $createTime = date('Y-m-d H:i:s');

    foreach ($menusToAdd as $menu) {
        // 检查父级菜单是否存在
        if (empty($menu['pid'])) {
            echo "跳过菜单 '{$menu['title']}'：找不到父级菜单\n";
            continue;
        }

        // 检查菜单是否已存在
        $checkStmt = $pdo->prepare("SELECT id FROM system_menu WHERE title = ? AND pid = ?");
        $checkStmt->execute([$menu['title'], $menu['pid']]);
        if ($checkStmt->fetch()) {
            echo "菜单 '{$menu['title']}' 已存在，跳过\n";
            continue;
        }

        // 插入菜单
        $result = $stmt->execute([
            $currentId,
            $menu['pid'],
            $menu['title'],
            $menu['icon'],
            $menu['node'],
            $menu['url'],
            $menu['params'],
            $menu['target'],
            $menu['sort'],
            $menu['status'],
            $createTime
        ]);

        if ($result) {
            echo "✅ 成功添加菜单: {$menu['title']} (ID: $currentId)\n";
            $addedCount++;
            $currentId++;
        } else {
            echo "❌ 添加菜单失败: {$menu['title']}\n";
        }
    }

    echo "\n总计添加了 $addedCount 个菜单项\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
