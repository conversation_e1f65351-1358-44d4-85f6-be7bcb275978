<?php

/**
 * 修复所有模板管理控制器
 * 参照原有ThinkAdmin控制器的正确写法
 */

echo "=== 修复所有模板管理控制器 ===\n\n";

// 需要修复的控制器映射
$controllerMappings = [
    'PromptTemplate' => [
        'title' => '提示词模板管理',
        'model' => 'PromptTemplate',
        'table' => 'prompt_template'
    ],
    'OutlineTemplate' => [
        'title' => '大纲模板管理',
        'model' => 'OutlineTemplate', 
        'table' => 'outline_template'
    ],
    'PaperType' => [
        'title' => '论文类型管理',
        'model' => 'PaperType',
        'table' => 'paper_type'
    ]
];

foreach ($controllerMappings as $controllerName => $config) {
    echo "修复控制器: {$controllerName}\n";
    
    $filePath = "app/admin/controller/{$controllerName}.php";
    
    if (!file_exists($filePath)) {
        echo "  ❌ 文件不存在，跳过\n";
        continue;
    }
    
    // 备份原文件
    $backupPath = $filePath . '.backup_' . date('YmdHis');
    copy($filePath, $backupPath);
    echo "  ✅ 已备份到: {$backupPath}\n";
    
    // 生成正确的控制器内容
    $controllerContent = generateCorrectController($controllerName, $config);
    
    // 写入新文件
    file_put_contents($filePath, $controllerContent);
    
    // 检查语法
    $output = [];
    $returnCode = 0;
    exec("php -l {$filePath} 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ 修复成功，语法正确\n";
    } else {
        echo "  ❌ 修复失败，语法错误: " . implode(' ', $output) . "\n";
        // 恢复备份
        copy($backupPath, $filePath);
        echo "  ↩️  已恢复备份\n";
    }
    
    echo "\n";
}

/**
 * 生成正确的控制器内容
 * 参照ThinkAdmin原有控制器的标准写法
 */
function generateCorrectController($controllerName, $config) {
    $modelName = $config['model'];
    $title = $config['title'];
    $table = $config['table'];
    
    return "<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\\{$modelName};
use think\admin\Controller;

/**
 * {$title}
 * @class {$controllerName}
 * @package app\admin\controller
 */
class {$controllerName} extends Controller
{
    /**
     * {$title}
     * @auth true
     * @menu true
     */
    public function index()
    {
        {$modelName}::mQuery(\$this)->layTable(function () {
            \$this->title = '{$title}';
        }, function (\$query) {
            \$query->like('name,title,description')->equal('status');
            \$query->dateBetween('create_time,update_time');
            \$query->order('sort asc,id desc');
        });
    }

    /**
     * 添加{$title}
     * @auth true
     */
    public function add()
    {
        {$modelName}::mForm('{$table}/form');
    }

    /**
     * 编辑{$title}
     * @auth true
     */
    public function edit()
    {
        {$modelName}::mForm('{$table}/form');
    }

    /**
     * 修改{$title}状态
     * @auth true
     */
    public function state()
    {
        {$modelName}::mSave(\$this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除{$title}
     * @auth true
     */
    public function remove()
    {
        {$modelName}::mDelete();
    }
}";
}

echo "=== 修复完成 ===\n";
echo "所有控制器已按照ThinkAdmin标准写法修复！\n";
