{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        邮件配置
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">SMTP服务器</label>
                <div class="layui-input-block">
                    <input type="text" name="smtp_host" value="{$configs.smtp_host}" placeholder="请输入SMTP服务器" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">SMTP端口</label>
                <div class="layui-input-block">
                    <input type="text" name="smtp_port" value="{$configs.smtp_port}" placeholder="请输入SMTP端口" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">SMTP用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="smtp_user" value="{$configs.smtp_user}" placeholder="请输入SMTP用户名" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">SMTP密码</label>
                <div class="layui-input-block">
                    <input type="password" name="smtp_pass" value="{$configs.smtp_pass}" placeholder="请输入SMTP密码" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">SMTP加密方式</label>
                <div class="layui-input-block">
                    <input type="text" name="smtp_secure" value="{$configs.smtp_secure}" placeholder="请输入SMTP加密方式" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">发件人邮箱</label>
                <div class="layui-input-block">
                    <input type="text" name="mail_from" value="{$configs.mail_from}" placeholder="请输入发件人邮箱" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">发件人名称</label>
                <div class="layui-input-block">
                    <input type="text" name="mail_from_name" value="{$configs.mail_from_name}" placeholder="请输入发件人名称" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info);
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}