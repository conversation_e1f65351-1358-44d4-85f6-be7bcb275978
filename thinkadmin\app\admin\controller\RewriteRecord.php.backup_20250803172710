<?php

namespace app\admin\controller;

use app\admin\model\RewriteRecordModel;
use think\admin\Controller;

/**
 * 降重记录管理
 * @class RewriteRecord
 * @package app\admin\controller
 */
class RewriteRecord extends Controller
{
    /**
     * 降重记录管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        RewriteRecordModel::mQuery($this)->layPage(function () {
            $this->title = '降重记录管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加降重记录管理
     * @auth true
     */
    public function add()
    {
        RewriteRecordModel::mForm('rewrite_record/form');
    }

    /**
     * 编辑降重记录管理
     * @auth true
     */
    public function edit()
    {
        RewriteRecordModel::mForm('rewrite_record/form');
    }

    /**
     * 删除降重记录管理
     * @auth true
     */
    public function remove()
    {
        RewriteRecordModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        RewriteRecordModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}