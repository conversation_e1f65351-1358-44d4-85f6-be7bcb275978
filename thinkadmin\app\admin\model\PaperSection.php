<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 论文章节模型
 * @class PaperSection
 * @package app\admin\model
 */
class PaperSection extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'paper_section';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'project_id' => 'integer',
        'section_level' => 'integer',
        'section_order' => 'integer',
        'word_count' => 'integer',
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getGenerationTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getStatusTextAttr($value, array $data): string
    {
        $statusMap = [
            'pending' => '待生成',
            'generating' => '生成中',
            'completed' => '已完成',
            'failed' => '生成失败'
        ];
        return $statusMap[$data['status'] ?? 'pending'] ?? '未知';
    }

    /**
     * 获取章节级别文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getSectionLevelTextAttr($value, array $data): string
    {
        $level = $data['section_level'] ?? 1;
        $levelMap = [
            1 => '一级标题',
            2 => '二级标题',
            3 => '三级标题',
            4 => '四级标题',
        ];
        return $levelMap[$level] ?? "第{$level}级标题";
    }

    /**
     * 关联论文项目
     * @return \think\model\relation\BelongsTo
     */
    public function project()
    {
        return $this->belongsTo(PaperProject::class, 'project_id', 'id');
    }

    /**
     * 获取项目的章节列表
     * @param int $projectId
     * @return \think\Collection
     */
    public static function getProjectSections(int $projectId): \think\Collection
    {
        return static::mk()->where(['project_id' => $projectId])
            ->order('section_order asc,id asc')
            ->select();
    }

    /**
     * 创建章节大纲
     * @param int $projectId
     * @param array $outline
     * @return bool
     */
    public static function createOutline(int $projectId, array $outline): bool
    {
        // 先删除现有章节
        static::mk()->where(['project_id' => $projectId])->delete();
        
        $sections = [];
        $order = 1;
        
        foreach ($outline as $section) {
            $sections[] = [
                'project_id' => $projectId,
                'section_title' => $section['title'] ?? '',
                'section_level' => $section['level'] ?? 1,
                'section_order' => $order++,
                'status' => 'pending',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ];
        }
        
        return static::mk()->insertAll($sections) > 0;
    }

    /**
     * 更新章节内容
     * @param int $sectionId
     * @param string $content
     * @param string $aiModel
     * @return bool
     */
    public static function updateContent(int $sectionId, string $content, string $aiModel = ''): bool
    {
        $wordCount = mb_strlen(strip_tags($content));
        
        $updateData = [
            'content' => $content,
            'word_count' => $wordCount,
            'status' => 'completed',
            'generation_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
        ];
        
        if (!empty($aiModel)) {
            $updateData['ai_model_used'] = $aiModel;
        }
        
        return static::mk()->where(['id' => $sectionId])->update($updateData) > 0;
    }

    /**
     * 获取章节统计
     * @param int $projectId
     * @return array
     */
    public static function getSectionStats(int $projectId): array
    {
        $sections = static::getProjectSections($projectId);
        
        $total = $sections->count();
        $completed = $sections->where('status', 'completed')->count();
        $generating = $sections->where('status', 'generating')->count();
        $failed = $sections->where('status', 'failed')->count();
        $totalWords = $sections->sum('word_count');
        
        return [
            'total' => $total,
            'completed' => $completed,
            'generating' => $generating,
            'failed' => $failed,
            'pending' => $total - $completed - $generating - $failed,
            'total_words' => $totalWords,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 1) : 0,
        ];
    }
}
