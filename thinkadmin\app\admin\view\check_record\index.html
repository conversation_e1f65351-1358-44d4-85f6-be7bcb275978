{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">查重记录管理</div>
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false" method="get" autocomplete="off">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">任务标题</label>
                <div class="layui-input-inline">
                    <input name="title" value="{$get.title|default=''}" placeholder="请输入任务标题" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">文件名</label>
                <div class="layui-input-inline">
                    <input name="file_name" value="{$get.file_name|default=''}" placeholder="请输入文件名" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">查重接口</label>
                <div class="layui-input-inline">
                    <select name="check_api_id" class="layui-select">
                        <option value="">-- 全部 --</option>
                        {foreach $checkApis as $id=>$name}
                        <option value="{$id}" {if isset($get.check_api_id) && $get.check_api_id eq $id}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">任务状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">-- 全部 --</option>
                        {foreach $statusOptions as $key=>$name}
                        <option value="{$key}" {if isset($get.status) && $get.status eq $key}selected{/if}>{$name}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line">
                    <thead>
                    <tr>
                        <th class='text-left nowrap'>
                            {if auth("export")}
                            <button data-action='{:url("export")}' data-rule="ids#{}" data-confirm="确定要导出选中的记录吗？" class='layui-btn layui-btn-sm layui-btn-primary'>
                                <i class='layui-icon layui-icon-export'></i> 导出记录
                            </button>
                            {/if}
                            {if auth("batchRemove")}
                            <button data-action='{:url("batchRemove")}' data-rule="ids#{}" data-confirm="确定要删除选中的记录吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                                <i class='layui-icon layui-icon-delete'></i> 批量删除
                            </button>
                            {/if}
                        </th>
                    </tr>
                    </thead>
                </table>
                {$pagehtml|raw|default=''}
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>任务标题</th>
                        <th class='text-left nowrap'>用户ID</th>
                        <th class='text-left nowrap'>文件信息</th>
                        <th class='text-left nowrap'>查重接口</th>
                        <th class='text-left nowrap'>相似度</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>费用</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.title|default=''}">{$vo.title|default=''}</div>
                        </td>
                        <td class='text-left nowrap'>{$vo.user_id|default=''}</td>
                        <td class='text-left nowrap'>
                            <div>
                                <div style="font-weight: bold;">{$vo.file_name|default='无文件'}</div>
                                <div style="font-size: 12px; color: #666;">
                                    {$vo.file_size_text|default='0 B'} | {$vo.word_count|default=0} 字
                                </div>
                            </div>
                        </td>
                        <td class='text-left nowrap'>{$checkApis[$vo.check_api_id]|default='未知'}</td>
                        <td class='text-left nowrap'>
                            {if !empty($vo.similarity_rate)}
                            <div>
                                <span style="font-weight: bold; color: {if $vo.similarity_rate <= 10}green{elseif $vo.similarity_rate <= 30}orange{else}red{/if};">
                                    {$vo.similarity_rate}%
                                </span>
                                <div style="font-size: 12px; color: #666;">{$vo.similarity_level}</div>
                            </div>
                            {else}
                            <span class="layui-badge layui-bg-gray">未检测</span>
                            {/if}
                        </td>
                        <td class='text-left nowrap'>
                            {switch name="vo.status"}
                            {case value="pending"}<span class="layui-badge">待提交</span>{/case}
                            {case value="submitted"}<span class="layui-badge layui-bg-blue">已提交</span>{/case}
                            {case value="checking"}<span class="layui-badge layui-bg-orange">查重中</span>{/case}
                            {case value="completed"}<span class="layui-badge layui-bg-green">已完成</span>{/case}
                            {case value="failed"}<span class="layui-badge layui-bg-red">失败</span>{/case}
                            {default /}<span class="layui-badge">{$vo.status}</span>
                            {/switch}
                        </td>
                        <td class='text-left nowrap'>¥{$vo.cost|default=0}</td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("view")}
                            <a class='layui-btn layui-btn-sm layui-btn-normal' data-modal='{:url("view")}?id={$vo.id}' data-title="查看详情">详情</a>
                            {/if}
                            {if auth("report") && !empty($vo.report_url) || !empty($vo.report_path)}
                            <a class='layui-btn layui-btn-sm layui-btn-warm' href='{:url("report")}?id={$vo.id}' target="_blank">报告</a>
                            {/if}
                            {if auth("resubmit") && in_array($vo.status, ['failed', 'pending'])}
                            <a class='layui-btn layui-btn-sm layui-btn-primary' data-confirm="确定要重新提交该任务吗？" data-action='{:url("resubmit")}' data-value="id#{$vo.id}" data-loading>重新提交</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-sm layui-btn-danger' data-confirm="确定要删除该记录吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                {if empty($list)}<p class="help-block text-center well">没 有 相 关 数 据 哦！</p>{/if}
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
