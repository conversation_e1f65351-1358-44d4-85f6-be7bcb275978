<?php

/**
 * 创建所有缺失的模板文件
 */

echo "=== 创建所有缺失的模板文件 ===\n\n";

// 需要创建模板的控制器映射
$controllerTemplates = [
    'DraftBox' => 'draft_box',
    'PaperType' => 'paper_type', 
    'AiModel' => 'ai_model',
    'RewriteRecord' => 'rewrite_record',
    'SystemNotice' => 'system_notice',
    'ExportTemplate' => 'export_template',
    'OutlineTemplate' => 'outline_template',
    'MessageTemplate' => 'message_template',
    'N8nWorkflow' => 'n8n_workflow',
    'RewriteModel' => 'rewrite_model',
    'VipPackage' => 'vip_package',
    'PackageConfig' => 'package_config',
    'ExportMonitor' => 'export_monitor',
    'PromptTemplate' => 'prompt_template',
    'CheckRecord' => 'check_record',
    'UserPoints' => 'user_points',
    'RechargeRecord' => 'recharge_record',
    'RewriteTask' => 'rewrite_task',
    'ContentTemplate' => 'content_template',
    'NotificationLog' => 'notification_log',
    'PaperProject' => 'paper_project'
];

foreach ($controllerTemplates as $controllerName => $templateDir) {
    $viewDir = "app/admin/view/{$templateDir}";
    
    // 检查目录是否存在
    if (!is_dir($viewDir)) {
        mkdir($viewDir, 0755, true);
        echo "✅ 创建目录: {$viewDir}\n";
    } else {
        echo "📁 目录已存在: {$viewDir}\n";
    }
    
    // 检查index.html是否存在
    $indexFile = "{$viewDir}/index.html";
    if (!file_exists($indexFile)) {
        $indexTemplate = generateGenericIndexTemplate($controllerName);
        file_put_contents($indexFile, $indexTemplate);
        echo "✅ 创建模板: {$indexFile}\n";
    } else {
        echo "📄 模板已存在: {$indexFile}\n";
    }
    
    // 检查form.html是否存在
    $formFile = "{$viewDir}/form.html";
    if (!file_exists($formFile)) {
        $formTemplate = generateGenericFormTemplate($controllerName);
        file_put_contents($formFile, $formTemplate);
        echo "✅ 创建模板: {$formFile}\n";
    } else {
        echo "📄 模板已存在: {$formFile}\n";
    }
}

echo "\n=== 创建完成 ===\n";

/**
 * 生成通用index模板
 */
function generateGenericIndexTemplate($controllerName) {
    $titles = [
        'DraftBox' => '草稿箱管理',
        'PaperType' => '论文类型管理',
        'AiModel' => 'AI模型配置',
        'RewriteRecord' => '降重记录管理',
        'SystemNotice' => '系统通知记录',
        'ExportTemplate' => '导出样式模板',
        'OutlineTemplate' => '大纲模板管理',
        'MessageTemplate' => '消息模板管理',
        'N8nWorkflow' => 'n8n工作流管理',
        'RewriteModel' => '降重模型配置',
        'VipPackage' => 'VIP套餐管理',
        'PackageConfig' => '套餐配置',
        'ExportMonitor' => '导出任务监控',
        'PromptTemplate' => '提示词模板管理',
        'CheckRecord' => '查重记录管理',
        'UserPoints' => '用户积分管理',
        'RechargeRecord' => '充值记录',
        'RewriteTask' => '降重任务管理',
        'ContentTemplate' => '正文模板管理',
        'NotificationLog' => '通知记录',
        'PaperProject' => '写作任务管理'
    ];
    
    $title = $titles[$controllerName] ?? $controllerName;
    
    return <<<HTML
{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        {$title}
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-inline">
                    <input name="name" value="{\$get.name|default=''}" placeholder="请输入名称" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" class="layui-select">
                        <option value="">全部状态</option>
                        <option value="1" {if isset(\$get.status) and \$get.status eq '1'}selected{/if}>启用</option>
                        <option value="0" {if isset(\$get.status) and \$get.status eq '0'}selected{/if}>禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <!-- 操作按钮 -->
        <div class="layui-row" style="margin-bottom: 15px;">
            <div class="layui-col-md12">
                {if auth("add")}
                <button data-modal='{:url("add")}' data-title="添加{$title}" class='layui-btn layui-btn-sm layui-btn-primary'>
                    <i class='layui-icon layui-icon-add-1'></i> 添加{$title}
                </button>
                {/if}
                {if auth("remove")}
                <button data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                    <i class='layui-icon layui-icon-delete'></i> 批量删除
                </button>
                {/if}
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>ID</th>
                        <th class='text-left nowrap'>名称</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach \$list as \$key=>\$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{\$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>{\$vo.id}</td>
                        <td class='text-left nowrap'>{\$vo.name|default=''}</td>
                        <td class='text-left nowrap'>
                            {if \$vo.status}<span class="layui-badge layui-bg-green">启用</span>{else}<span class="layui-badge layui-bg-red">禁用</span>{/if}
                        </td>
                        <td class='text-left nowrap'>{\$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-xs' data-modal='{:url("edit")}?id={\$vo.id}' data-title="编辑{$title}">编辑</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-xs layui-btn-danger' data-confirm="确定要删除吗？" data-action='{:url("remove")}' data-value="id#{\$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                
                {if empty(\$list)}
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <p class="help-block text-center well" style="padding: 20px; text-align: center; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><br>
                            暂 无 数 据！
                        </p>
                    </div>
                </div>
                {/if}
                
                <!-- 分页 -->
                {\$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
HTML;
}

/**
 * 生成通用form模板
 */
function generateGenericFormTemplate($controllerName) {
    $titles = [
        'DraftBox' => '草稿箱管理',
        'PaperType' => '论文类型管理',
        'AiModel' => 'AI模型配置',
        'RewriteRecord' => '降重记录管理',
        'SystemNotice' => '系统通知记录',
        'ExportTemplate' => '导出样式模板',
        'OutlineTemplate' => '大纲模板管理',
        'MessageTemplate' => '消息模板管理',
        'N8nWorkflow' => 'n8n工作流管理',
        'RewriteModel' => '降重模型配置',
        'VipPackage' => 'VIP套餐管理',
        'PackageConfig' => '套餐配置',
        'ExportMonitor' => '导出任务监控',
        'PromptTemplate' => '提示词模板管理',
        'CheckRecord' => '查重记录管理',
        'UserPoints' => '用户积分管理',
        'RechargeRecord' => '充值记录',
        'RewriteTask' => '降重任务管理',
        'ContentTemplate' => '正文模板管理',
        'NotificationLog' => '通知记录',
        'PaperProject' => '写作任务管理'
    ];
    
    $title = $titles[$controllerName] ?? $controllerName;
    
    return <<<HTML
{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-form"></span>
        {\$title}
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="{\$vo.name|default=''}" placeholder="请输入名称" class="layui-input" required>
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">{\$vo.description|default=''}</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort" value="{\$vo.sort|default=100}" placeholder="请输入排序值" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset(\$vo.status) || \$vo.status eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset(\$vo.status) && \$vo.status eq 0}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(formSubmit)', function(data){
        \$.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                \$.msg.success(ret.info, 3, function(){
                    parent.layer.closeAll();
                    parent.location.reload();
                });
            } else {
                \$.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}
HTML;
}
