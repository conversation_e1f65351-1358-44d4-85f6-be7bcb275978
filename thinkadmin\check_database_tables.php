<?php

/**
 * 检查数据库表结构
 */

echo "=== 检查数据库表结构 ===\n\n";

try {
    // 连接SQLite数据库
    $dbPath = __DIR__ . '/database/sqlite.db';
    
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在: {$dbPath}\n";
        exit;
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n\n";
    
    // 查看所有表
    echo "1. 数据库中的所有表:\n";
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "  - {$table}\n";
    }
    
    echo "\n表总数: " . count($tables) . "\n";
    
    // 检查是否有相关的表
    echo "\n2. 查找相关表:\n";
    $relatedTables = [];
    foreach ($tables as $table) {
        if (strpos($table, 'template') !== false || 
            strpos($table, 'paper') !== false || 
            strpos($table, 'document') !== false ||
            strpos($table, 'content') !== false) {
            $relatedTables[] = $table;
            echo "  ✅ 找到相关表: {$table}\n";
        }
    }
    
    if (empty($relatedTables)) {
        echo "  ❌ 没有找到相关表\n";
        echo "\n3. 需要创建的表:\n";
        echo "  - document_template (文档模板表)\n";
        echo "  - paper_type (论文类型表)\n";
        echo "  - prompt_template (提示词模板表)\n";
        
        // 检查是否有迁移文件
        echo "\n4. 检查迁移文件:\n";
        $migrationDir = __DIR__ . '/database/migrations';
        if (is_dir($migrationDir)) {
            $migrations = glob($migrationDir . '/*.php');
            echo "  找到 " . count($migrations) . " 个迁移文件:\n";
            foreach ($migrations as $migration) {
                echo "    - " . basename($migration) . "\n";
            }
        } else {
            echo "  ❌ 迁移目录不存在\n";
        }
    } else {
        echo "\n3. 检查表结构:\n";
        foreach ($relatedTables as $table) {
            echo "  表 {$table} 的字段:\n";
            $stmt = $pdo->query("PRAGMA table_info({$table})");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                echo "    - {$column['name']} ({$column['type']})\n";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
