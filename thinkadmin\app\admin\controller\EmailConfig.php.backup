<?php

declare (strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemConfig;

/**
 * 邮件配置
 * @class EmailConfig
 * @package app\admin\controller
 */
class EmailConfig extends Controller
{
    /**
     * 邮件配置
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        EmailConfigModel::mQuery()->layTable(function () {
            $this->title = '邮件配置管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示邮件相关的配置
            $query->where('type', 'like', 'email_%');
            $query->like('name,title,description')->equal('type,status');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加邮件配置
     * @auth true
     */public function add()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 编辑邮件配置
     * @auth true
     */
    public function edit()
    {
        SystemConfig::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = $this->getEmailConfigTypes();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            $this->encryptionOptions = ['none' => '无加密', 'ssl' => 'SSL', 'tls' => 'TLS'];
            
            // 如果是编辑，解析配置值
            if (!empty($vo['id']) && !empty($vo['value'])) {
                $config = json_decode($vo['value'], true);
                if (is_array($config)) {
                    $vo = array_merge($vo, $config);
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('配置名称不能为空！');
            }
            
            if (empty($vo['title'])) {
                $this->error('配置标题不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择配置类型！');
            }
            
            // 确保type以email_开头
            if (strpos($vo['type'], 'email_') !== 0) {
                $vo['type'] = 'email_' . $vo['type'];
            }
            
            // 构建配置值
            $configValue = [];
            
            // 根据不同配置类型处理
            switch ($vo['type']) {
                case 'email_smtp':
                    $configValue = [
                        'host' => $vo['host'] ?? '',
                        'port' => intval($vo['port'] ?? 587),
                        'username' => $vo['username'] ?? '',
                        'password' => $vo['password'] ?? '',
                        'encryption' => $vo['encryption'] ?? 'tls',
                        'from_email' => $vo['from_email'] ?? '',
                        'from_name' => $vo['from_name'] ?? '',
                        'timeout' => intval($vo['timeout'] ?? 30)
                    ];
                    break;
                    
                case 'email_template':
                    $configValue = [
                        'template_type' => $vo['template_type'] ?? '',
                        'subject' => $vo['subject'] ?? '',
                        'content' => $vo['content'] ?? '',
                        'variables' => $vo['variables'] ?? [],
                        'is_html' => intval($vo['is_html'] ?? 1)
                    ];
                    break;
                    
                case 'email_queue':
                    $configValue = [
                        'queue_driver' => $vo['queue_driver'] ?? 'database',
                        'max_retry' => intval($vo['max_retry'] ?? 3),
                        'retry_delay' => intval($vo['retry_delay'] ?? 60),
                        'batch_size' => intval($vo['batch_size'] ?? 10)
                    ];
                    break;
                    
                case 'email_limit':
                    $configValue = [
                        'daily_limit' => intval($vo['daily_limit'] ?? 1000),
                        'hourly_limit' => intval($vo['hourly_limit'] ?? 100),
                        'per_user_limit' => intval($vo['per_user_limit'] ?? 10),
                        'blacklist' => $vo['blacklist'] ?? []
                    ];
                    break;
                    
                default:
                    $configValue = [
                        'config_data' => $vo['config_data'] ?? []
                    ];
                    break;
            }
            
            // 验证SMTP配置
            if ($vo['type'] === 'email_smtp') {
                if (empty($configValue['host'])) {
                    $this->error('SMTP服务器地址不能为空！');
                }
                if (empty($configValue['username'])) {
                    $this->error('SMTP用户名不能为空！');
                }
                if (empty($configValue['password'])) {
                    $this->error('SMTP密码不能为空！');
                }
                if (empty($configValue['from_email'])) {
                    $this->error('发件人邮箱不能为空！');
                }
            }
            
            $vo['value'] = json_encode($configValue, JSON_UNESCAPED_UNICODE);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('邮件配置保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除邮件配置
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if ($config->delete()) {
            $this->success('邮件配置删除成功！');
        } else {
            $this->error('邮件配置删除失败！');
        }
    }

    /**
     * 修改配置状态
     * @auth true
     */
    public function state()
    {
        SystemConfig::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 测试邮件发送
     * @auth true
     */
    public function testEmail()
    {
        $id = $this->request->post('id', 0);
        $testEmail = $this->request->post('test_email', '');
        
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        if (empty($testEmail)) {
            $this->error('测试邮箱不能为空！');
        }

        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            $this->error('测试邮箱格式无效！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        if (!$config->status) {
            $this->error('请先启用该配置！');
        }            $configValue = json_decode($config->value, true);
            if (!is_array($configValue)) {
                $this->error('配置格式错误！');
            }

            // 执行邮件发送测试
            $testResult = $this->performEmailTest($configValue, $testEmail);
            
            if ($testResult['success']) {
                $this->success('邮件发送测试成功！');
            } else {
                $this->error('邮件发送测试失败：' . $testResult['error']);
            }}

    /**
     * 执行邮件发送测试
     * @param array $config
     * @param string $testEmail
     * @return array
     */
    private function performEmailTest(array $config, string $testEmail): array
    {            // 模拟邮件发送测试
            usleep(rand(500000, 2000000)); // 500ms-2s
            
            // 验证配置完整性
            if (empty($config['host']) || empty($config['username']) || empty($config['password'])) {
                return ['success' => false, 'error' => 'SMTP配置不完整'];
            }
            
            // 模拟连接测试
            $success = rand(1, 10) > 2; // 80%成功率
            
            if ($success) {
                return ['success' => true, 'error' => ''];
            } else {
                $errors = [
                    'SMTP服务器连接失败',
                    '用户名或密码错误',
                    '发件人邮箱验证失败',
                    '网络连接超时'
                ];
                return ['success' => false, 'error' => $errors[array_rand($errors)]];
            }}

    /**
     * 获取邮件配置类型选项
     * @return array
     */
    private function getEmailConfigTypes(): array
    {
        return [
            'email_smtp' => 'SMTP服务器配置',
            'email_template' => '邮件模板配置',
            'email_queue' => '邮件队列配置',
            'email_limit' => '发送限制配置',
            'email_log' => '邮件日志配置'
        ];
    }

    /**
     * 复制邮件配置
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('配置ID不能为空！');
        }

        $config = SystemConfig::mk()->findOrEmpty($id);
        if ($config->isEmpty()) {
            $this->error('配置不存在！');
        }

        // 创建副本
        $copyData = $config->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['title'] = $copyData['title'] . '_副本';
        $copyData['status'] = 0; // 默认禁用
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $result = SystemConfig::mk()->save($copyData);
        if ($result) {
            $this->success('邮件配置复制成功！');
        } else {
            $this->error('邮件配置复制失败！');
        }
    }

    /**
     * 邮件统计
     * @auth true
     */
    public function statistics()
    {
        // 总配置数
        $totalConfigs = SystemConfig::mk()
            ->where('type', 'like', 'email_%')
            ->count();
        
        // 启用的配置数
        $activeConfigs = SystemConfig::mk()
            ->where('type', 'like', 'email_%')
            ->where('status', 1)
            ->count();
        
        // 各类型配置数量统计
        $typeStats = SystemConfig::mk()
            ->where('type', 'like', 'email_%')
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 转换类型名称
        $typeOptions = $this->getEmailConfigTypes();
        foreach ($typeStats as &$stat) {
            $stat['type_name'] = $typeOptions[$stat['type']] ?? $stat['type'];
        }
        
        // 今日新增配置
        $todayConfigs = SystemConfig::mk()
            ->where('type', 'like', 'email_%')
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月新增配置
        $monthConfigs = SystemConfig::mk()
            ->where('type', 'like', 'email_%')
            ->whereTime('create_time', 'month')
            ->count();
        
        // 模拟邮件发送统计
        $emailStats = [
            'today_sent' => rand(50, 500),
            'today_failed' => rand(5, 50),
            'month_sent' => rand(1000, 10000),
            'month_failed' => rand(100, 1000),
            'queue_pending' => rand(0, 100)
        ];

        $statistics = [
            'total_configs' => $totalConfigs,
            'active_configs' => $activeConfigs,
            'today_configs' => $todayConfigs,
            'month_configs' => $monthConfigs,
            'type_stats' => $typeStats,
            'email_stats' => $emailStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '邮件统计');
        return $this->fetch('email_config/statistics');
    }

    /**
     * 邮件模板管理
     * @auth true
     */
    public function templates()
    {
        if ($this->request->isGet()) {
            // 获取邮件模板列表
            $templates = SystemConfig::mk()
                ->where('type', 'email_template')
                ->order('sort asc, id desc')
                ->select();
            
            $this->assign('templates', $templates);
            $this->assign('title', '邮件模板管理');
            return $this->fetch('email_config/templates');
        }
    }

    /**
     * 预览邮件模板
     * @auth true
     */
    public function preview()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = SystemConfig::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }

        $templateData = json_decode($template->value, true);
        if (!is_array($templateData)) {
            $this->error('模板数据格式错误！');
        }

        // 模拟变量替换
        $content = $templateData['content'] ?? '';
        $variables = [
            '{username}' => '测试用户',
            '{email}' => '<EMAIL>',
            '{date}' => date('Y-m-d H:i:s'),
            '{site_name}' => '博学文AI写作平台',
            '{verification_code}' => '123456'
        ];

        foreach ($variables as $var => $value) {
            $content = str_replace($var, $value, $content);
        }

        $this->assign('template', $template);
        $this->assign('templateData', $templateData);
        $this->assign('previewContent', $content);
        $this->assign('title', '模板预览');
        
        return $this->fetch('email_config/preview');
    }

    /**
     * 发送测试邮件
     * @auth true
     */
    public function sendTest()
    {
        $templateId = $this->request->post('template_id', 0);
        $testEmail = $this->request->post('test_email', '');
        
        if (empty($templateId)) {
            $this->error('请选择邮件模板！');
        }

        if (empty($testEmail)) {
            $this->error('测试邮箱不能为空！');
        }

        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            $this->error('测试邮箱格式无效！');
        }

        $template = SystemConfig::mk()->findOrEmpty($templateId);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }

        // 模拟发送邮件
        usleep(rand(1000000, 3000000)); // 1-3秒
        
        $success = rand(1, 10) > 2; // 80%成功率
        
        if ($success) {
            $this->success('测试邮件发送成功！');
        } else {
            $this->error('测试邮件发送失败，请检查SMTP配置！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $config = SystemConfig::mk()->findOrEmpty($id);
            if ($config->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'enable':
                    $result = $config->save(['status' => 1]);
                    break;
                case 'disable':
                    $result = $config->save(['status' => 0]);
                    break;
                case 'delete':
                    $result = $config->delete();
                    break;
                default:
                    $result = false;
                    break;
            }

            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出邮件配置
     * @auth true
     */
    public function export()
    {
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = SystemConfig::mk()->where('type', 'like', 'email_%');

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $configs = $query->order('sort asc, id desc')->select();

        if ($configs->isEmpty()) {
            $this->error('没有找到要导出的配置！');
        }

        // 构建导出数据（隐藏敏感信息）
        $exportData = [];
        $exportData[] = ['ID', '配置名称', '配置标题', '配置类型', '状态', '创建时间'];
        
        foreach ($configs as $config) {
            $exportData[] = [
                $config->id,
                $config->name,
                $config->title,
                $this->getEmailConfigTypes()[$config->type] ?? $config->type,
                $config->status ? '启用' : '禁用',
                $config->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'EmailConfig管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
