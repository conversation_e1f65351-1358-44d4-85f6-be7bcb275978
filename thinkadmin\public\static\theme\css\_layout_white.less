@charset "UTF-8";
@import "_config.less";

// +----------------------------------------------------------------------
// | Static Plugin for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------
// | gitee 代码仓库：https://gitee.com/zoujingli/think-plugs-static
// | github 代码仓库：https://github.com/zoujingli/think-plugs-static
// +----------------------------------------------------------------------

.layui-layout-theme-white > .layui-layout-admin {
  > .layui-side {
    box-shadow: none;
    background-color: #fff !important;

    .layui-logo {
      color: #333;
      box-shadow: none;
      font-weight: bold;
    }

    .layui-side-scroll {
      display: flex !important;
      box-sizing: border-box;

      .layui-nav-child {
        background: none !important;
      }

      .layui-side-icon {
        width: 60px;
        display: none;
        background: #fff;

        a {
          height: 60px;
          display: block;
          text-align: center;
          line-height: 60px;
        }

        .layui-icon {
          color: #999;
        }

        > .layui-this {
          background: #fff;

          .layui-icon {
            color: #090 !important;
          }
        }
      }

      .layui-side-tree {
        flex: 1;

        .layui-nav-item {
          background: none !important;
          border-bottom-color: #fff;

          a {
            color: #333 !important;
            background: none !important;
            border-bottom: none !important;

            &:hover {
              color: #090 !important;
            }
          }

          .layui-this, &.layui-this {
            > a {
              color: #090 !important;
              background: none !important;
              font-weight: bold !important;

              &:hover {
                background: none !important;
              }
            }
          }

          &ed > a {
            color: #999 !important;
          }
        }
      }
    }
  }

  > .layui-body > .think-page-body > .layui-card {
    &:before {
      top: 0;
      left: 0;
      bottom: 0;
      z-index: 4;
      content: '';
      position: absolute;
      box-shadow: @ShadowBodyRight;
    }

    > .layui-card-header {
      border-left: @BoxBottomLine;
    }
  }

  > .layui-header {
    background: #fff !important;

    .layui-nav-item {
      &.layui-this > a {
        color: #090 !important;
        font-weight: bold;
        background: none !important;
      }

      > a {
        color: #333 !important;

        &:hover {
          color: #090 !important;
          background: none !important;
        }
      }
    }
  }
}
