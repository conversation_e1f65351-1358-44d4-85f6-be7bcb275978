<div class="layui-header">
    <ul class="layui-nav layui-layout-left">
        <li class="layui-nav-item" lay-unselect>
            <a class="text-center" data-target-menu-type>
                <i class="layui-icon layui-icon-spread-left"></i>
            </a>
        </li>
        <li class="layui-nav-item" lay-unselect>
            <a class="layui-logo-hide layui-elip" href="{:sysuri('@')}" title="{:sysconf('app_name')}">
                <span class="headimg headimg-no headimg-xs" data-lazy-src="{:sysconf('site_icon')}"></span>
            </a>
        </li>
        {foreach $menus as $one}
        <li class="layui-nav-item">
            <a data-menu-node="m-{$one.id}" data-open="{$one.url}"><span>{$one.title|default=''}</span></a>
        </li>
        {/foreach}
    </ul>
    <ul class="layui-nav layui-layout-right">
        <li lay-unselect class="layui-nav-item"><a data-reload><i class="layui-icon layui-icon-refresh-3"></i></a></li>
        {if session('user.username')}
        <li class="layui-nav-item">
            <dl class="layui-nav-child">
                <dd lay-unselect><a data-modal="{:sysuri('admin/index/info',['id'=>session('user.id')])}"><i class="layui-icon layui-icon-set-fill"></i> {:lang('基本资料')}</a></dd>
                <dd lay-unselect><a data-modal="{:sysuri('admin/index/pass',['id'=>session('user.id')])}"><i class="layui-icon layui-icon-component"></i> {:lang('安全设置')}</a></dd>
                {if isset($super) and $super}
                <dd lay-unselect><a data-load="{:sysuri('admin/api.system/push')}"><i class="layui-icon layui-icon-template-1"></i> {:lang('缓存加速')}</a></dd>
                <dd lay-unselect><a data-load="{:sysuri('admin/api.system/clear')}"><i class="layui-icon layui-icon-fonts-clear"></i> {:lang('清理缓存')}</a></dd>
                {/if}
                <dd lay-unselect><a data-width="520px" data-modal="{:sysuri('admin/index/theme')}"><i class="layui-icon layui-icon-theme"></i> {:lang('配色方案')}</a></dd>
                <dd lay-unselect><a data-load="{:sysuri('admin/login/out')}" data-confirm="{:lang('确定要退出登录吗？')}"><i class="layui-icon layui-icon-release"></i> {:lang('退出登录')}</a></dd>
            </dl>
            <a class="layui-elip">
                <span class="headimg" data-lazy-src="{:htmlentities(session('user.headimg'))}"></span>
                <span>{:htmlentities(lang(session('user.nickname')?:session('user.username')))}</span>
            </a>
        </li>
        {else}
        <li class="layui-nav-item">
            <a data-href="{:sysuri('admin/login/index')}"><i class="layui-icon layui-icon-username"></i> {:lang('立即登录')}</a>
        </li>
        {/if}
    </ul>
</div>