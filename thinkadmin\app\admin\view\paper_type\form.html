<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card" data-table-id="PaperTypeTable">

    <div class="layui-card-body padding-left-40">

        <label class="layui-form-item relative block">
            <span class="help-label"><b>论文类型名称</b>Paper Type Name</span>
            <input maxlength="50" class="layui-input" name="name" value='{$vo.name|default=""}' required vali-name="论文类型名称" placeholder="请输入论文类型名称，如：毕业论文、学术论文等">
            <span class="help-block">请输入论文类型名称，建议使用简洁明了的名称</span>
        </label>

        <label class="layui-form-item relative block">
            <span class="help-label"><b>类型描述</b>Description</span>
            <textarea name="description" class="layui-textarea" placeholder="请输入论文类型的详细描述">{$vo.description|default=''}</textarea>
            <span class="help-block">请详细描述该论文类型的特点和适用场景</span>
        </label>

        <div class="layui-form-item">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>最小字数</b>Min Word Count</span>
                        <input type="number" min="0" class="layui-input" name="word_count_min" value='{$vo.word_count_min|default="0"}' required vali-name="最小字数" placeholder="请输入最小字数">
                    </label>
                </div>
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>最大字数</b>Max Word Count</span>
                        <input type="number" min="0" class="layui-input" name="word_count_max" value='{$vo.word_count_max|default="0"}' required vali-name="最大字数" placeholder="请输入最大字数">
                    </label>
                </div>
            </div>
            <span class="help-block">设置该类型论文的字数范围，用于指导用户创建项目</span>
        </div>

        <div class="layui-form-item">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>默认大纲模板</b>Default Outline Template</span>
                        <select name="outline_template_id" class="layui-select">
                            <option value="">请选择大纲模板</option>
                            {foreach $outline_templates as $k=>$v}
                            {if isset($vo.outline_template_id) and $vo.outline_template_id eq $k}
                            <option selected value="{$k}">{$v}</option>
                            {else}
                            <option value="{$k}">{$v}</option>
                            {/if}
                            {/foreach}
                        </select>
                    </label>
                </div>
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>默认提示词模板</b>Default Prompt Template</span>
                        <select name="prompt_template_id" class="layui-select">
                            <option value="">请选择提示词模板</option>
                            {foreach $prompt_templates as $k=>$v}
                            {if isset($vo.prompt_template_id) and $vo.prompt_template_id eq $k}
                            <option selected value="{$k}">{$v}</option>
                            {else}
                            <option value="{$k}">{$v}</option>
                            {/if}
                            {/foreach}
                        </select>
                    </label>
                </div>
            </div>
            <span class="help-block">选择该类型论文的默认模板，可以提高用户创建效率</span>
        </div>

        <div class="layui-form-item">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>排序权重</b>Sort Weight</span>
                        <input type="number" min="0" class="layui-input" name="sort" value='{$vo.sort|default="100"}' required vali-name="排序权重" placeholder="请输入排序权重">
                        <span class="help-block">数值越大排序越靠前</span>
                    </label>
                </div>
                <div class="layui-col-md6">
                    <label class="relative block">
                        <span class="help-label"><b>状态</b>Status</span>
                        <select name="status" class="layui-select">
                            {if isset($vo.status) and $vo.status eq 1}
                            <option selected value="1">启用</option>
                            <option value="0">禁用</option>
                            {else}
                            <option value="1">启用</option>
                            <option selected value="0">禁用</option>
                            {/if}
                        </select>
                        <span class="help-block">禁用后用户无法选择该类型</span>
                    </label>
                </div>
            </div>
        </div>

    </div>

    <div class="hr-line-dashed"></div>
    {notempty name='vo.id'}<input type='hidden' value='{$vo.id}' name='id'>{/notempty}

    <div class="layui-form-item text-center">
        <button class="layui-btn" type='submit'>保存数据</button>
        <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
    </div>

</form>
