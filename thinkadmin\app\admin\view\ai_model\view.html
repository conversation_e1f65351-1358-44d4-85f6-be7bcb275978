{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span>
        AI模型详情
        <div class="pull-right">
            {if auth("edit")}
            <button data-modal='{:url("edit")}?id={$vo.id}' class='layui-btn layui-btn-sm layui-btn-primary'>编辑模型</button>
            {/if}
            {if auth("testConnection")}
            <button data-action='{:url("testConnection")}' data-value="id#{$vo.id}" class='layui-btn layui-btn-sm layui-btn-primary'>测试连接</button>
            {/if}
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tr>
                                <td width="120">模型名称</td>
                                <td>{$vo.name|default='-'}</td>
                            </tr>
                            <tr>
                                <td>服务商</td>
                                <td>
                                    {switch name="vo.provider"}
                                    {case value="openai"}<span class="layui-badge layui-bg-blue">OpenAI</span>{/case}
                                    {case value="anthropic"}<span class="layui-badge layui-bg-green">Anthropic</span>{/case}
                                    {case value="google"}<span class="layui-badge layui-bg-orange">Google</span>{/case}
                                    {case value="baidu"}<span class="layui-badge layui-bg-red">百度</span>{/case}
                                    {case value="alibaba"}<span class="layui-badge layui-bg-cyan">阿里巴巴</span>{/case}
                                    {case value="tencent"}<span class="layui-badge layui-bg-blue">腾讯</span>{/case}
                                    {default /}<span class="layui-badge layui-bg-gray">{$vo.provider}</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td>模型标识</td>
                                <td><code>{$vo.model_id|default='-'}</code></td>
                            </tr>
                            <tr>
                                <td>API端点</td>
                                <td><code>{$vo.api_endpoint|default='-'}</code></td>
                            </tr>
                            <tr>
                                <td>模型描述</td>
                                <td>{$vo.description|default='-'}</td>
                            </tr>
                            <tr>
                                <td>最大Token数</td>
                                <td>{$vo.max_tokens|default=0|number_format} tokens</td>
                            </tr>
                            <tr>
                                <td>输入价格</td>
                                <td>¥{$vo.input_price|default=0} / 1K tokens</td>
                            </tr>
                            <tr>
                                <td>输出价格</td>
                                <td>¥{$vo.output_price|default=0} / 1K tokens</td>
                            </tr>
                            <tr>
                                <td>状态</td>
                                <td>
                                    {if $vo.status == 1}
                                    <span class="layui-badge layui-bg-green">启用</span>
                                    {else}
                                    <span class="layui-badge layui-bg-red">禁用</span>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>健康状态</td>
                                <td>
                                    {switch name="vo.health_status"}
                                    {case value="healthy"}<span class="layui-badge layui-bg-green">健康</span>{/case}
                                    {case value="warning"}<span class="layui-badge layui-bg-orange">警告</span>{/case}
                                    {case value="error"}<span class="layui-badge layui-bg-red">错误</span>{/case}
                                    {default /}<span class="layui-badge layui-bg-gray">未知</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td>最后检查时间</td>
                                <td>{$vo.last_health_check|default='-'}</td>
                            </tr>
                            <tr>
                                <td>排序权重</td>
                                <td>{$vo.sort|default=0}</td>
                            </tr>
                            <tr>
                                <td>创建时间</td>
                                <td>{$vo.create_time|default='-'}</td>
                            </tr>
                            <tr>
                                <td>更新时间</td>
                                <td>{$vo.update_time|default='-'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">使用统计</div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="text-center">
                                    <div class="font-s20 color-blue">{$vo.total_requests|default=0}</div>
                                    <div class="color-desc">总请求数</div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="text-center">
                                    <div class="font-s20 color-green">¥{$vo.total_cost|default=0}</div>
                                    <div class="color-desc">总费用</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row layui-col-space10 margin-top-10">
                            <div class="layui-col-md6">
                                <div class="text-center">
                                    <div class="font-s16 color-orange">{$vo.success_rate|default=0}%</div>
                                    <div class="color-desc">成功率</div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="text-center">
                                    <div class="font-s16 color-red">{$vo.avg_response_time|default=0}ms</div>
                                    <div class="color-desc">平均响应时间</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {if $vo.config}
                <div class="layui-card margin-top-20">
                    <div class="layui-card-header">配置信息</div>
                    <div class="layui-card-body">
                        <pre style="background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 12px; max-height: 200px; overflow-y: auto;">{$vo.config|json_encode:JSON_PRETTY_PRINT}</pre>
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </div>
</div>
{/block}
