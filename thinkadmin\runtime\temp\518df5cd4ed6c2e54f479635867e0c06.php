<?php /*a:3:{s:80:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\ai_model\form.html";i:1754096114;s:71:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\form.html";i:1754096978;s:71:"G:\Users\YOLO\Desktop\boxiaowen_sql\thinkadmin\app\admin\view\main.html";i:1754094170;}*/ ?>
<div class="layui-card"><?php if(!(empty($title) || (($title instanceof \think\Collection || $title instanceof \think\Paginator ) && $title->isEmpty()))): ?><div class="layui-card-header"><span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span><?php echo htmlentities((string) lang($title)); ?><div class="pull-right"></div></div><?php endif; ?><div class="layui-card-line"></div><div class="layui-card-body"><div class="layui-card-html"><?php if(!(empty($showErrorMessage) || (($showErrorMessage instanceof \think\Collection || $showErrorMessage instanceof \think\Paginator ) && $showErrorMessage->isEmpty()))): ?><div class="think-box-notify" type="error"><b><?php echo lang('系统提示：'); ?></b><span><?php echo $showErrorMessage; ?></span></div><?php endif; ?><div class="layui-card"><div class="layui-card-body"><form class="layui-form layui-box" action="<?php echo request()->url(); ?>" data-auto="true" method="post"><div class="layui-form-item"><label class="layui-form-label">模型名称</label><div class="layui-input-block"><input name="name" value="<?php echo htmlentities((string) (isset($vo['name']) && ($vo['name'] !== '')?$vo['name']:'')); ?>" required lay-verify="required" placeholder="请输入模型名称" class="layui-input"></div></div><div class="layui-form-item"><label class="layui-form-label">提供商</label><div class="layui-input-block"><select name="provider" lay-verify="required"><option value="">请选择提供商</option><option value="openai" <?php if(isset($vo['provider']) and $vo['provider'] == 'openai'): ?>selected<?php endif; ?>>OpenAI</option><option value="baidu" <?php if(isset($vo['provider']) and $vo['provider'] == 'baidu'): ?>selected<?php endif; ?>>百度</option><option value="aliyun" <?php if(isset($vo['provider']) and $vo['provider'] == 'aliyun'): ?>selected<?php endif; ?>>阿里云</option><option value="tencent" <?php if(isset($vo['provider']) and $vo['provider'] == 'tencent'): ?>selected<?php endif; ?>>腾讯云</option><option value="zhipu" <?php if(isset($vo['provider']) and $vo['provider'] == 'zhipu'): ?>selected<?php endif; ?>>智谱AI</option><option value="moonshot" <?php if(isset($vo['provider']) and $vo['provider'] == 'moonshot'): ?>selected<?php endif; ?>>Moonshot</option><option value="other" <?php if(isset($vo['provider']) and $vo['provider'] == 'other'): ?>selected<?php endif; ?>>其他</option></select></div></div><div class="layui-form-item"><label class="layui-form-label">模型代码</label><div class="layui-input-block"><input name="model_code" value="<?php echo htmlentities((string) (isset($vo['model_code']) && ($vo['model_code'] !== '')?$vo['model_code']:'')); ?>" required lay-verify="required" placeholder="请输入模型代码，如：gpt-3.5-turbo" class="layui-input"><div class="layui-form-mid layui-word-aux">API调用时使用的模型标识</div></div></div><div class="layui-form-item"><label class="layui-form-label">API地址</label><div class="layui-input-block"><input name="api_url" value="<?php echo htmlentities((string) (isset($vo['api_url']) && ($vo['api_url'] !== '')?$vo['api_url']:'')); ?>" required lay-verify="required" placeholder="请输入API地址" class="layui-input"></div></div><div class="layui-form-item"><label class="layui-form-label">API密钥</label><div class="layui-input-block"><input name="api_key" value="<?php echo htmlentities((string) (isset($vo['api_key']) && ($vo['api_key'] !== '')?$vo['api_key']:'')); ?>" required lay-verify="required" placeholder="请输入API密钥" class="layui-input"></div></div><div class="layui-form-item"><label class="layui-form-label">最大Token</label><div class="layui-input-block"><input name="max_tokens" value="<?php echo htmlentities((string) (isset($vo['max_tokens']) && ($vo['max_tokens'] !== '')?$vo['max_tokens']:'4096')); ?>" required lay-verify="required|number" placeholder="请输入最大Token数" class="layui-input"><div class="layui-form-mid layui-word-aux">模型支持的最大Token数量</div></div></div><div class="layui-form-item"><label class="layui-form-label">输入成本</label><div class="layui-input-block"><input name="input_cost" value="<?php echo htmlentities((string) (isset($vo['input_cost']) && ($vo['input_cost'] !== '')?$vo['input_cost']:'0.000000')); ?>" required lay-verify="required" placeholder="请输入每1K Token的输入成本" class="layui-input"><div class="layui-form-mid layui-word-aux">每1000个输入Token的成本（元）</div></div></div><div class="layui-form-item"><label class="layui-form-label">输出成本</label><div class="layui-input-block"><input name="output_cost" value="<?php echo htmlentities((string) (isset($vo['output_cost']) && ($vo['output_cost'] !== '')?$vo['output_cost']:'0.000000')); ?>" required lay-verify="required" placeholder="请输入每1K Token的输出成本" class="layui-input"><div class="layui-form-mid layui-word-aux">每1000个输出Token的成本（元）</div></div></div><div class="layui-form-item"><label class="layui-form-label">优先级</label><div class="layui-input-block"><input name="priority" value="<?php echo htmlentities((string) (isset($vo['priority']) && ($vo['priority'] !== '')?$vo['priority']:'0')); ?>" required lay-verify="required|number" placeholder="请输入优先级" class="layui-input"><div class="layui-form-mid layui-word-aux">数值越大优先级越高</div></div></div><div class="layui-form-item"><label class="layui-form-label">超时时间</label><div class="layui-input-block"><input name="timeout" value="<?php echo htmlentities((string) (isset($vo['timeout']) && ($vo['timeout'] !== '')?$vo['timeout']:'30')); ?>" required lay-verify="required|number" placeholder="请输入超时时间" class="layui-input"><div class="layui-form-mid layui-word-aux">API请求超时时间（秒）</div></div></div><div class="layui-form-item"><label class="layui-form-label">重试次数</label><div class="layui-input-block"><input name="retry_count" value="<?php echo htmlentities((string) (isset($vo['retry_count']) && ($vo['retry_count'] !== '')?$vo['retry_count']:'3')); ?>" required lay-verify="required|number" placeholder="请输入重试次数" class="layui-input"><div class="layui-form-mid layui-word-aux">请求失败时的重试次数</div></div></div><div class="layui-form-item"><label class="layui-form-label">模型描述</label><div class="layui-input-block"><textarea name="description" placeholder="请输入模型描述" class="layui-textarea"><?php echo htmlentities((string) (isset($vo['description']) && ($vo['description'] !== '')?$vo['description']:'')); ?></textarea></div></div><div class="layui-form-item"><label class="layui-form-label">配置参数</label><div class="layui-input-block"><textarea name="config" placeholder="请输入JSON格式的配置参数" class="layui-textarea"><?php echo htmlentities((string) (isset($vo['config']) && ($vo['config'] !== '')?$vo['config']:'')); ?></textarea><div class="layui-form-mid layui-word-aux">JSON格式的额外配置参数，如：{"temperature": 0.7, "top_p": 1}</div></div></div><div class="layui-form-item"><label class="layui-form-label">状态</label><div class="layui-input-block"><input type="radio" name="status" value="1" title="启用" <?php if(!isset($vo['status']) or $vo['status'] == 1): ?>checked<?php endif; ?>><input type="radio" name="status" value="0" title="禁用" <?php if(isset($vo['status']) and $vo['status'] == 0): ?>checked<?php endif; ?>></div></div><div class="hr-line-dashed"></div><div class="layui-form-item text-center"><?php if(empty($vo['id'])): ?><button class="layui-btn" type='submit'>添加模型</button><?php else: ?><button class="layui-btn" type='submit'>编辑模型</button><?php endif; ?><button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button></div></form></div></div></div></div><script>
    layui.use(['form'], function () {
        var form = layui.form;
        
        // 监听提供商选择
        form.on('select(provider)', function(data){
            var provider = data.value;
            var apiUrlInput = $('input[name="api_url"]');
            var modelCodeInput = $('input[name="model_code"]');
            
            // 根据提供商设置默认值
            switch(provider) {
                case 'openai':
                    apiUrlInput.val('https://api.openai.com/v1/chat/completions');
                    modelCodeInput.val('gpt-3.5-turbo');
                    break;
                case 'baidu':
                    apiUrlInput.val('https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions');
                    modelCodeInput.val('ernie-bot-turbo');
                    break;
                case 'zhipu':
                    apiUrlInput.val('https://open.bigmodel.cn/api/paas/v4/chat/completions');
                    modelCodeInput.val('glm-4');
                    break;
                case 'moonshot':
                    apiUrlInput.val('https://api.moonshot.cn/v1/chat/completions');
                    modelCodeInput.val('moonshot-v1-8k');
                    break;
            }
        });
    });
</script></div>