<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\AiModel;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 降重模型配置
 * @class RewriteModel
 * @package app\admin\controller
 */
class RewriteModel extends Controller
{
    /**
     * 降重模型配置
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        RewriteModelModel::mQuery()->layTable(function () {
            $this->title = '降重模型管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示降重相关的AI模型
            $query->where('type', 'rewrite');
            $query->like('name,description,provider')->equal('type,status,is_default');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加降重模型
     * @auth true
     */public function add()
    {
        AiModel::mForm('form');
    }

    /**
     * 编辑降重模型
     * @auth true
     */
    public function edit()
    {
        AiModel::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = ['rewrite' => '降重模型'];
            $this->providerOptions = AiModel::getProviderOptions();
            
            // 如果是编辑，获取当前模型配置
            if (!empty($vo['id'])) {
                $vo['config'] = $vo['config'] ?: [];
            }
        } else {
            // 限制只能创建降重模型
            $vo['type'] = 'rewrite';
            
            // 处理模型配置
            if (!empty($vo['config']) && is_string($vo['config'])) {
                $config = json_decode($vo['config'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('模型配置格式错误！');
                }
                $vo['config'] = $config;
            }
            
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('模型名称不能为空！');
            }
            
            if (empty($vo['provider'])) {
                $this->error('服务提供商不能为空！');
            }
            
            if (empty($vo['api_key'])) {
                $this->error('API密钥不能为空！');
            }
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('模型配置保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除降重模型
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }

        $model = AiModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }

        // 检查是否为默认模型
        if ($model->is_default) {
            $this->error('默认模型不能删除！');
        }

        // 检查是否有关联的降重任务
        $taskCount = $model->rewriteTasks()->count();
        if ($taskCount > 0) {
            $this->error('该模型已有关联的降重任务，不能删除！');
        }

        if ($model->delete()) {
            $this->success('模型删除成功！');
        } else {
            $this->error('模型删除失败！');
        }
    }

    /**
     * 修改降重模型状态
     * @auth true
     */
    public function state()
    {
        AiModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 设置默认模型
     * @auth true
     */
    public function setDefault()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }

        $model = AiModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }

        if (!$model->status) {
            $this->error('只能设置启用状态的模型为默认！');
        }

        // 取消其他降重模型的默认状态
        AiModel::mk()->where(['type' => 'rewrite'])->save(['is_default' => 0]);

        // 设置当前模型为默认
        if ($model->save(['is_default' => 1])) {
            $this->success('设置默认模型成功！');
        } else {
            $this->error('设置默认模型失败！');
        }
    }

    /**
     * 测试模型连接
     * @auth true
     */
    public function testConnection()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }

        $model = AiModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }            // 这里应该调用实际的API测试连接
            // 暂时模拟测试结果
            $testResult = $this->simulateApiTest($model);
            
            if ($testResult['success']) {
                $this->success('连接测试成功！响应时间：' . $testResult['response_time'] . 'ms');
            } else {
                $this->error('连接测试失败：' . $testResult['error']);
            }}

    /**
     * 模拟API测试（实际项目中应该调用真实API）
     * @param AiModel $model
     * @return array
     */
    private function simulateApiTest(AiModel $model): array
    {
        // 模拟网络延迟
        usleep(rand(100000, 500000)); // 100-500ms
        
        // 模拟测试结果
        $success = rand(1, 10) > 2; // 80%成功率
        
        if ($success) {
            return [
                'success' => true,
                'response_time' => rand(100, 500)
            ];
        } else {
            $errors = [
                'API密钥无效',
                '网络连接超时',
                '服务暂时不可用',
                '请求频率超限'
            ];
            return [
                'success' => false,
                'error' => $errors[array_rand($errors)]
            ];
        }
    }

    /**
     * 复制模型配置
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }

        $model = AiModel::mk()->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }

        // 创建副本
        $copyData = $model->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['is_default'] = 0;
        $copyData['status'] = 0; // 默认禁用
        $copyData['usage_count'] = 0;
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $newModel = AiModel::mk()->save($copyData);
        if ($newModel) {
            $this->success('模型配置复制成功！');
        } else {
            $this->error('模型配置复制失败！');
        }
    }

    /**
     * 批量设置排序
     * @auth true
     */
    public function sort()
    {
        $ids = $this->request->post('ids', '');
        $sorts = $this->request->post('sorts', '');
        
        if (empty($ids) || empty($sorts)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $sortArray = explode(',', $sorts);
        
        if (count($idArray) !== count($sortArray)) {
            $this->error('参数数量不匹配！');
        }

        $updateData = [];
        foreach ($idArray as $index => $id) {
            $updateData[] = [
                'id' => $id,
                'sort' => intval($sortArray[$index])
            ];
        }

        $result = AiModel::mk()->saveAll($updateData);
        if ($result) {
            $this->success('排序设置成功！');
        } else {
            $this->error('排序设置失败！');
        }
    }

    /**
     * 模型详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('模型ID不能为空！');
        }

        $model = AiModel::mk()->with(['rewriteTasks'])->findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('模型不存在！');
        }

        // 获取使用统计
        $usageStats = $this->getModelUsageStats($model);
        
        // 获取最近使用记录
        $recentTasks = $model->rewriteTasks()
            ->with(['user'])
            ->order('id desc')
            ->limit(10)
            ->select();

        $this->assign('model', $model);
        $this->assign('usageStats', $usageStats);
        $this->assign('recentTasks', $recentTasks);
        $this->assign('title', '模型详情');
        
        return $this->fetch('rewrite_model/view');
    }

    /**
     * 获取模型使用统计
     * @param AiModel $model
     * @return array
     */
    private function getModelUsageStats(AiModel $model): array
    {
        // 总使用次数
        $totalUsage = $model->rewriteTasks()->count();
        
        // 今日使用次数
        $todayUsage = $model->rewriteTasks()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月使用次数
        $monthUsage = $model->rewriteTasks()
            ->whereTime('create_time', 'month')
            ->count();
        
        // 成功率
        $successCount = $model->rewriteTasks()
            ->where('status', 'completed')
            ->count();
        $successRate = $totalUsage > 0 ? round(($successCount / $totalUsage) * 100, 2) : 0;
        
        // 平均处理时间（模拟数据）
        $avgProcessTime = rand(30, 120); // 30-120秒
        
        return [
            'total_usage' => $totalUsage,
            'today_usage' => $todayUsage,
            'month_usage' => $monthUsage,
            'success_rate' => $successRate,
            'avg_process_time' => $avgProcessTime
        ];
    }

    /**
     * 获取模型统计数据
     * @auth true
     */
    public function statistics()
    {
        // 总模型数（仅降重）
        $totalModels = AiModel::mk()->where('type', 'rewrite')->count();
        
        // 启用的模型数
        $activeModels = AiModel::mk()
            ->where('type', 'rewrite')
            ->where('status', 1)
            ->count();
        
        // 默认模型
        $defaultModel = AiModel::mk()
            ->where('type', 'rewrite')
            ->where('is_default', 1)
            ->findOrEmpty();

        // 各提供商模型数
        $providerStats = AiModel::mk()
            ->where('type', 'rewrite')
            ->field('provider, COUNT(*) as count')
            ->group('provider')
            ->select()
            ->toArray();

        // 今日新增模型数
        $todayModels = AiModel::mk()
            ->where('type', 'rewrite')
            ->whereTime('create_time', 'today')
            ->count();

        // 本月新增模型数
        $monthModels = AiModel::mk()
            ->where('type', 'rewrite')
            ->whereTime('create_time', 'month')
            ->count();

        // 使用最多的模型
        $mostUsedModel = AiModel::mk()
            ->where('type', 'rewrite')
            ->order('usage_count desc')
            ->findOrEmpty();

        $statistics = [
            'total_models' => $totalModels,
            'active_models' => $activeModels,
            'today_models' => $todayModels,
            'month_models' => $monthModels,
            'provider_stats' => $providerStats,
            'default_model' => $defaultModel->toArray(),
            'most_used_model' => $mostUsedModel->toArray()
        ];

        return json($statistics);
    }

    /**
     * 导出模型配置
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的模型！');
        }

        $idArray = explode(',', $ids);
        $models = AiModel::mk()
            ->whereIn('id', $idArray)
            ->where('type', 'rewrite')
            ->order('sort asc, id desc')
            ->select();

        if ($models->isEmpty()) {
            $this->error('没有找到要导出的模型！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '模型名称', '服务提供商', '模型版本', '是否默认', '使用次数', '状态', '创建时间'];
        
        foreach ($models as $model) {
            $exportData[] = [
                $model->id,
                $model->name,
                $model->provider,
                $model->model_version ?: '默认',
                $model->is_default ? '是' : '否',
                $model->usage_count,
                $model->status ? '启用' : '禁用',
                $model->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 批量删除模型
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的模型！');
        }

        $idArray = explode(',', $ids);
        
        // 检查是否有默认模型
        $defaultCount = AiModel::mk()
            ->whereIn('id', $idArray)
            ->where('is_default', 1)
            ->count();
            
        if ($defaultCount > 0) {
            $this->error('不能删除默认模型，请先取消默认设置！');
        }
        
        // 检查是否有关联任务
        $taskCount = 0;
        foreach ($idArray as $id) {
            $model = AiModel::mk()->findOrEmpty($id);
            if (!$model->isEmpty()) {
                $taskCount += $model->rewriteTasks()->count();
            }
        }
        
        if ($taskCount > 0) {
            $this->error('选中的模型中有已关联任务的记录，不能删除！');
        }
        
        $result = AiModel::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'RewriteModel管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
