{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        草稿箱管理
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form layui-form-pane form-search" action="{:request()->url()}" onsubmit="return false">
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">草稿标题</label>
                <div class="layui-input-inline">
                    <input name="title" value="{$get.title|default=''}" placeholder="请输入草稿标题" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">论文类型</label>
                <div class="layui-input-inline">
                    <select name="paper_type_id" class="layui-select">
                        <option value="">全部类型</option>
                        {foreach $paperTypes as $k=>$v}
                        {if isset($get.paper_type_id) and $get.paper_type_id eq $k}
                        <option selected value="{$k}">{$v}</option>
                        {else}
                        <option value="{$k}">{$v}</option>
                        {/if}
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <label class="layui-form-label">关键词</label>
                <div class="layui-input-inline">
                    <input name="keywords" value="{$get.keywords|default=''}" placeholder="请输入关键词" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-inline">
                <button class="layui-btn layui-btn-primary" type="submit"><i class="layui-icon layui-icon-search"></i> 搜 索</button>
                <button class="layui-btn layui-btn-primary" type="reset"><i class="layui-icon layui-icon-refresh"></i> 重 置</button>
            </div>
        </form>

        <!-- 操作按钮 -->
        <div class="layui-row" style="margin-bottom: 15px;">
            <div class="layui-col-md12">
                {if auth("add")}
                <button data-modal='{:url("add")}' data-title="创建草稿" class='layui-btn layui-btn-sm layui-btn-primary'>
                    <i class='layui-icon layui-icon-add-1'></i> 创建草稿
                </button>
                {/if}
                {if auth("remove")}
                <button data-action='{:url("remove")}' data-rule="id#{id}" data-confirm="确定要批量删除草稿吗？" class='layui-btn layui-btn-sm layui-btn-danger'>
                    <i class='layui-icon layui-icon-delete'></i> 批量删除
                </button>
                {/if}
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <table class="layui-table" lay-skin="line" data-auto-none>
                    <thead>
                    <tr>
                        <th class='list-table-check-td think-checkbox'>
                            <input data-auto-none data-check-target='.list-check-box' type='checkbox'>
                        </th>
                        <th class='text-left nowrap'>草稿标题</th>
                        <th class='text-left nowrap'>论文类型</th>
                        <th class='text-left nowrap'>版本号</th>
                        <th class='text-left nowrap'>主题</th>
                        <th class='text-left nowrap'>关键词</th>
                        <th class='text-left nowrap'>字数</th>
                        <th class='text-left nowrap'>状态</th>
                        <th class='text-left nowrap'>创建时间</th>
                        <th class='text-left nowrap'>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    {foreach $list as $key=>$vo}
                    <tr>
                        <td class='list-table-check-td think-checkbox'>
                            <input class="list-check-box" value='{$vo.id}' type='checkbox'>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.title|default=''}">{$vo.title|default=''}</div>
                        </td>
                        <td class='text-left nowrap'>{$paperTypes[$vo.paper_type_id]|default='未分类'}</td>
                        <td class='text-left nowrap'>
                            <span class="layui-badge layui-bg-blue">v{$vo.outline_version|default=1}</span>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.subject|default=''}">{$vo.subject|default=''}</div>
                        </td>
                        <td class='text-left nowrap'>
                            <div style="max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{$vo.keywords|default=''}">{$vo.keywords|default=''}</div>
                        </td>
                        <td class='text-left nowrap'>{$vo.current_word_count|default=0}</td>
                        <td class='text-left nowrap'>
                            {switch name="vo.status"}
                            {case value="draft"}<span class="layui-badge layui-bg-gray">草稿</span>{/case}
                            {case value="outline"}<span class="layui-badge layui-bg-blue">大纲</span>{/case}
                            {case value="writing"}<span class="layui-badge layui-bg-orange">写作中</span>{/case}
                            {case value="completed"}<span class="layui-badge layui-bg-green">已完成</span>{/case}
                            {default}<span class="layui-badge">{$vo.status}</span>
                            {/switch}
                        </td>
                        <td class='text-left nowrap'>{$vo.create_time|format_datetime}</td>
                        <td class='text-left nowrap'>
                            {if auth("view")}
                            <a class='layui-btn layui-btn-xs layui-btn-normal' data-modal='{:url("view")}?id={$vo.id}' data-title="查看草稿">查看</a>
                            {/if}
                            {if auth("edit")}
                            <a class='layui-btn layui-btn-xs' data-modal='{:url("edit")}?id={$vo.id}' data-title="编辑草稿">编辑</a>
                            {/if}
                            {if auth("remove")}
                            <a class='layui-btn layui-btn-xs layui-btn-danger' data-confirm="确定要删除该草稿吗？" data-action='{:url("remove")}' data-value="id#{$vo.id}" data-loading>删除</a>
                            {/if}
                        </td>
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
                
                {if empty($list)}
                <div class="layui-row">
                    <div class="layui-col-md12">
                        <p class="help-block text-center well" style="padding: 20px; text-align: center; color: #999;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><br>
                            暂 无 草 稿 数 据！
                        </p>
                    </div>
                </div>
                {/if}
                
                <!-- 分页 -->
                {$pagehtml|raw|default=''}
            </div>
        </div>
    </div>
</div>
{/block}
