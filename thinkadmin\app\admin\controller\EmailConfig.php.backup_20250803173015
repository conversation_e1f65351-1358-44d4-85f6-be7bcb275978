<?php

namespace app\admin\controller;

use app\admin\model\EmailConfigModel;
use think\admin\Controller;

/**
 * 邮件配置管理
 * @class EmailConfig
 * @package app\admin\controller
 */
class EmailConfig extends Controller
{
    /**
     * 邮件配置管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        EmailConfigModel::mQuery($this)->layPage(function () {
            $this->title = '邮件配置管理';
        }, function ($query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加邮件配置管理
     * @auth true
     */
    public function add()
    {
        EmailConfigModel::mForm('email_config/form');
    }

    /**
     * 编辑邮件配置管理
     * @auth true
     */
    public function edit()
    {
        EmailConfigModel::mForm('email_config/form');
    }

    /**
     * 删除邮件配置管理
     * @auth true
     */
    public function remove()
    {
        EmailConfigModel::mDelete();
    }

    /**
     * 修改状态
     * @auth true
     */
    public function state()
    {
        EmailConfigModel::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }
}