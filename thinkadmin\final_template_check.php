<?php

/**
 * 最终模板语法检查
 */

echo "=== 最终模板语法检查 ===\n\n";

// 检查关键文件的语法
$keyFiles = [
    'app/admin/view/content_template/index.html',
    'app/admin/view/content_template/form.html',
    'app/admin/view/prompt_template/index.html',
    'app/admin/view/prompt_template/form.html',
    'app/admin/view/check_record/index.html',
    'app/admin/view/rewrite_record/index.html'
];

$allGood = true;

foreach ($keyFiles as $file) {
    $fullPath = __DIR__ . '/' . $file;
    
    if (!file_exists($fullPath)) {
        echo "❌ 文件不存在: {$file}\n";
        $allGood = false;
        continue;
    }
    
    $content = file_get_contents($fullPath);
    $errors = [];
    
    // 检查各种语法错误
    if (preg_match('/\|default==/', $content)) {
        $errors[] = "包含 |default== 语法错误";
    }

    // 只检查在条件判断中的错误用法
    if (preg_match('/\{if[^}]*\|default=\'\'[^}]*\}/', $content)) {
        $errors[] = "包含在条件判断中的 |default='' 语法错误";
    }

    if (preg_match('/\{if[^}]*\?\?\s*\'\'[^}]*\}/', $content)) {
        $errors[] = "包含在条件判断中的 ?? '' 语法错误";
    }
    
    // 检查未闭合的标签
    $openTags = preg_match_all('/\{[^}]*$/', $content);
    $closeTags = preg_match_all('/\{\/[^}]*\}/', $content);
    
    if (empty($errors)) {
        echo "✅ {$file} - 语法正确\n";
    } else {
        echo "❌ {$file} - 发现错误:\n";
        foreach ($errors as $error) {
            echo "   - {$error}\n";
        }
        $allGood = false;
    }
}

echo "\n";

if ($allGood) {
    echo "🎉 所有关键模板文件语法正确！\n";
    echo "\n建议操作：\n";
    echo "1. 清理模板缓存: Remove-Item -Recurse -Force runtime\\temp\n";
    echo "2. 访问系统测试各个模块功能\n";
    echo "3. 如果仍有错误，请检查具体的错误信息\n";
} else {
    echo "⚠️  仍有模板文件需要修复\n";
}

// 检查模板缓存状态
$tempDir = __DIR__ . '/runtime/temp';
if (is_dir($tempDir)) {
    $tempFiles = glob($tempDir . '/*.php');
    if (count($tempFiles) > 0) {
        echo "\n⚠️  发现 " . count($tempFiles) . " 个模板缓存文件\n";
        echo "建议清理缓存以确保使用最新模板\n";
    } else {
        echo "\n✅ 模板缓存已清理\n";
    }
} else {
    echo "\n✅ 模板缓存目录不存在（已清理）\n";
}

echo "\n=== 检查完成 ===\n";
