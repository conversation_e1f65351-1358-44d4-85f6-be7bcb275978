{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span>
        大纲模板详情
        <div class="pull-right">
            {if auth("edit")}
            <button data-modal='{:url("edit")}?id={$vo.id}' class='layui-btn layui-btn-sm layui-btn-primary'>编辑模板</button>
            {/if}
            {if auth("copy")}
            <button data-action='{:url("copy")}' data-value="id#{$vo.id}" class='layui-btn layui-btn-sm layui-btn-primary'>复制模板</button>
            {/if}
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tr>
                                <td width="120">模板名称</td>
                                <td>{$vo.name|default='-'}</td>
                            </tr>
                            <tr>
                                <td>适用类型</td>
                                <td>
                                    {if $vo.paper_type_id == 0}
                                    <span class="layui-badge layui-bg-blue">通用模板</span>
                                    {else}
                                    {$vo.paper_type.name|default='-'}
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>模板描述</td>
                                <td>{$vo.description|default='-'}</td>
                            </tr>
                            <tr>
                                <td>是否默认</td>
                                <td>
                                    {if $vo.is_default == 1}
                                    <span class="layui-badge layui-bg-green">是</span>
                                    {else}
                                    <span class="layui-badge layui-bg-gray">否</span>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>状态</td>
                                <td>
                                    {if $vo.status == 1}
                                    <span class="layui-badge layui-bg-green">启用</span>
                                    {else}
                                    <span class="layui-badge layui-bg-red">禁用</span>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td>排序权重</td>
                                <td>{$vo.sort|default=0}</td>
                            </tr>
                            <tr>
                                <td>使用次数</td>
                                <td>{$vo.usage_count|default=0} 次</td>
                            </tr>
                            <tr>
                                <td>创建时间</td>
                                <td>{$vo.create_time|default='-'}</td>
                            </tr>
                            <tr>
                                <td>更新时间</td>
                                <td>{$vo.update_time|default='-'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">使用统计</div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md12">
                                <div class="text-center">
                                    <div class="font-s24 color-green">{$vo.usage_count|default=0}</div>
                                    <div class="color-desc">总使用次数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-card margin-top-20">
            <div class="layui-card-header">模板内容</div>
            <div class="layui-card-body">
                {if $vo.content}
                <div class="content-text" style="white-space: pre-wrap; font-family: monospace; background: #f8f8f8; padding: 15px; border-radius: 4px;">
{$vo.content}
                </div>
                {else}
                <div class="text-center color-desc padding-20">
                    暂无模板内容
                </div>
                {/if}
            </div>
        </div>
    </div>
</div>
{/block}
