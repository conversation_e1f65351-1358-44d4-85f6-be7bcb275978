{extend name="../../admin/view/main"}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon font-s10 color-desc margin-right-5">&#xe65b;</span>
        论文项目详情
        <div class="pull-right">
            {if auth("edit")}
            <button data-modal='{:url("edit")}?id={$vo.id}' class='layui-btn layui-btn-sm layui-btn-primary'>编辑项目</button>
            {/if}
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space20">
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <table class="layui-table">
                            <tr>
                                <td width="120">项目标题</td>
                                <td>{$vo.title|default='-'}</td>
                            </tr>
                            <tr>
                                <td>论文类型</td>
                                <td>{$vo.paper_type.name|default='-'}</td>
                            </tr>
                            <tr>
                                <td>研究主题</td>
                                <td>{$vo.subject|default='-'}</td>
                            </tr>
                            <tr>
                                <td>关键词</td>
                                <td>{$vo.keywords|default='-'}</td>
                            </tr>
                            <tr>
                                <td>目标字数</td>
                                <td>{$vo.target_word_count|default=0} 字</td>
                            </tr>
                            <tr>
                                <td>当前字数</td>
                                <td>{$vo.current_word_count|default=0} 字</td>
                            </tr>
                            <tr>
                                <td>完成进度</td>
                                <td>
                                    <div class="layui-progress">
                                        <div class="layui-progress-bar" lay-percent="{$vo.progress|default=0}%"></div>
                                    </div>
                                    {$vo.progress|default=0}%
                                </td>
                            </tr>
                            <tr>
                                <td>项目状态</td>
                                <td>
                                    {switch name="vo.status"}
                                    {case value="draft"}<span class="layui-badge layui-bg-gray">草稿</span>{/case}
                                    {case value="outline"}<span class="layui-badge layui-bg-blue">大纲生成中</span>{/case}
                                    {case value="writing"}<span class="layui-badge layui-bg-orange">写作中</span>{/case}
                                    {case value="completed"}<span class="layui-badge layui-bg-green">已完成</span>{/case}
                                    {case value="failed"}<span class="layui-badge layui-bg-red">失败</span>{/case}
                                    {default /}<span class="layui-badge layui-bg-gray">未知</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td>创建时间</td>
                                <td>{$vo.create_time|default='-'}</td>
                            </tr>
                            <tr>
                                <td>更新时间</td>
                                <td>{$vo.update_time|default='-'}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {if $vo.description}
                <div class="layui-card">
                    <div class="layui-card-header">项目描述</div>
                    <div class="layui-card-body">
                        <div class="content-text">{$vo.description|nl2br}</div>
                    </div>
                </div>
                {/if}
                
                {if $vo.outline_content}
                <div class="layui-card">
                    <div class="layui-card-header">论文大纲</div>
                    <div class="layui-card-body">
                        <div class="content-text">{$vo.outline_content|nl2br}</div>
                    </div>
                </div>
                {/if}
            </div>
            
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">操作记录</div>
                    <div class="layui-card-body">
                        <div class="layui-timeline">
                            <div class="layui-timeline-item">
                                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                                <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">{$vo.create_time}</h3>
                                    <p>项目创建</p>
                                </div>
                            </div>
                            {if $vo.outline_generated_at}
                            <div class="layui-timeline-item">
                                <i class="layui-icon layui-timeline-axis">&#xe705;</i>
                                <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">{$vo.outline_generated_at}</h3>
                                    <p>大纲生成完成</p>
                                </div>
                            </div>
                            {/if}
                            {if $vo.writing_started_at}
                            <div class="layui-timeline-item">
                                <i class="layui-icon layui-timeline-axis">&#xe642;</i>
                                <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">{$vo.writing_started_at}</h3>
                                    <p>开始写作</p>
                                </div>
                            </div>
                            {/if}
                            {if $vo.completed_at}
                            <div class="layui-timeline-item">
                                <i class="layui-icon layui-timeline-axis">&#xe605;</i>
                                <div class="layui-timeline-content layui-text">
                                    <h3 class="layui-timeline-title">{$vo.completed_at}</h3>
                                    <p>项目完成</p>
                                </div>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
