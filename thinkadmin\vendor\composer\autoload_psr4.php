<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'think\\view\\driver\\' => array($vendorDir . '/topthink/think-view/src'),
    'think\\migration\\' => array($vendorDir . '/topthink/think-migration/src'),
    'think\\admin\\install\\' => array($vendorDir . '/zoujingli/think-install/src'),
    'think\\admin\\' => array($vendorDir . '/zoujingli/think-library/src'),
    'think\\' => array($vendorDir . '/topthink/think-template/src', $vendorDir . '/topthink/framework/src/think', $vendorDir . '/topthink/think-validate/src', $vendorDir . '/topthink/think-orm/src', $vendorDir . '/topthink/think-container/src', $vendorDir . '/topthink/think-helper/src'),
    'plugin\\payment\\' => array($vendorDir . '/zoujingli/think-plugs-payment/src'),
    'plugin\\center\\' => array($vendorDir . '/zoujingli/think-plugs-center/src'),
    'plugin\\account\\' => array($vendorDir . '/zoujingli/think-plugs-account/src'),
    'app\\admin\\' => array($vendorDir . '/zoujingli/think-plugs-admin/src'),
    'app\\' => array($baseDir . '/app'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Phinx\\' => array($vendorDir . '/topthink/think-migration/phinx'),
);
