<?php

/**
 * 最终验证脚本
 * 验证所有模块、菜单、权限节点配置是否完整
 */

$dbPath = __DIR__ . '/database/sqlite.db';

if (!file_exists($dbPath)) {
    echo "数据库文件不存在: {$dbPath}\n";
    exit(1);
}

try {
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 最终验证报告 ===\n\n";
    
    // 1. 验证控制器文件
    echo "1. 控制器文件验证\n";
    echo "==================\n";
    
    $controllerDir = __DIR__ . '/app/admin/controller';
    $expectedControllers = [
        'PaperType', 'OutlineTemplate', 'PromptTemplate', 'ContentTemplate', 'PaperProject',
        'RewriteRecord', 'RewriteModel', 'CheckRecord', 'RewriteTask',
        'ExportTemplate', 'ExportMonitor', 'Invoice',
        'User', 'VipPackage', 'UserPoints',
        'Order', 'PackageConfig', 'RechargeRecord',
        'SystemNotice', 'MessageTemplate', 'EmailConfig', 'NotificationLog',
        'AiModel', 'N8nWorkflow', 'ApiKey', 'WebhookConfig', 'ContentFilter', 'BasicConfig'
    ];
    
    $missingControllers = [];
    $existingControllers = [];
    
    foreach ($expectedControllers as $controller) {
        $filePath = $controllerDir . '/' . $controller . '.php';
        if (file_exists($filePath)) {
            $existingControllers[] = $controller;
            echo "✅ {$controller}.php\n";
        } else {
            $missingControllers[] = $controller;
            echo "❌ {$controller}.php (缺失)\n";
        }
    }
    
    echo "\n控制器统计:\n";
    echo "- 预期: " . count($expectedControllers) . " 个\n";
    echo "- 存在: " . count($existingControllers) . " 个\n";
    echo "- 缺失: " . count($missingControllers) . " 个\n";
    
    // 2. 验证菜单结构
    echo "\n2. 菜单结构验证\n";
    echo "================\n";
    
    $expectedMenuStructure = [
        '写作中心' => ['论文类型管理', '大纲模板管理', '提示词模板管理', '正文模板管理', '写作任务管理', '草稿箱管理'],
        '降重与查重' => ['降重记录管理', '降重模型配置', '查重记录管理', '降重任务管理'],
        '文档导出' => ['导出样式模板', '导出任务监控'],
        '用户中心' => ['用户列表', 'VIP套餐管理', '用户积分管理'],
        '收费系统' => ['订单管理', '套餐配置', '充值记录'],
        '通知与消息' => ['系统通知记录', '消息模板管理', '邮件配置', '通知记录'],
        '系统设置' => ['AI模型配置', 'n8n工作流管理', '接口密钥管理', 'Webhook配置', '内容风控规则', '基础参数设置']
    ];
    
    $totalExpectedMenus = 0;
    $totalExistingMenus = 0;
    $missingMenus = [];
    
    foreach ($expectedMenuStructure as $parentTitle => $children) {
        echo "\n{$parentTitle}:\n";
        
        // 检查父菜单
        $stmt = $pdo->prepare("SELECT id, icon, sort FROM system_menu WHERE title = ? AND pid = 0");
        $stmt->execute([$parentTitle]);
        $parentMenu = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($parentMenu) {
            echo "  ✅ 父菜单存在 (ID: {$parentMenu['id']}, Sort: {$parentMenu['sort']}, Icon: {$parentMenu['icon']})\n";
            
            // 检查子菜单
            foreach ($children as $childTitle) {
                $totalExpectedMenus++;
                
                $stmt = $pdo->prepare("SELECT id, node, icon, sort FROM system_menu WHERE title = ? AND pid = ?");
                $stmt->execute([$childTitle, $parentMenu['id']]);
                $childMenu = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($childMenu) {
                    $totalExistingMenus++;
                    echo "    ✅ {$childTitle} -> {$childMenu['node']} (Sort: {$childMenu['sort']})\n";
                } else {
                    $missingMenus[] = "{$parentTitle} -> {$childTitle}";
                    echo "    ❌ {$childTitle} (缺失)\n";
                }
            }
        } else {
            echo "  ❌ 父菜单不存在\n";
            foreach ($children as $childTitle) {
                $totalExpectedMenus++;
                $missingMenus[] = "{$parentTitle} -> {$childTitle}";
            }
        }
    }
    
    echo "\n菜单统计:\n";
    echo "- 预期子菜单: {$totalExpectedMenus} 个\n";
    echo "- 存在子菜单: {$totalExistingMenus} 个\n";
    echo "- 缺失子菜单: " . count($missingMenus) . " 个\n";
    
    // 3. 验证权限节点
    echo "\n3. 权限节点验证\n";
    echo "================\n";
    
    $stmt = $pdo->query("
        SELECT p.title as parent_title, c.title, c.node, c.url 
        FROM system_menu c 
        JOIN system_menu p ON c.pid = p.id 
        WHERE c.pid > 0 AND c.status = 1 AND c.node != '' 
        ORDER BY p.sort, c.sort
    ");
    $menuNodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $validNodes = 0;
    $invalidNodes = 0;
    
    foreach ($menuNodes as $menu) {
        if (preg_match('/^admin\/[a-z_]+\/[a-z_]+$/', $menu['node'])) {
            $validNodes++;
            echo "✅ {$menu['parent_title']} -> {$menu['title']}: {$menu['node']}\n";
        } else {
            $invalidNodes++;
            echo "❌ {$menu['parent_title']} -> {$menu['title']}: {$menu['node']} (格式无效)\n";
        }
    }
    
    echo "\n权限节点统计:\n";
    echo "- 有效节点: {$validNodes} 个\n";
    echo "- 无效节点: {$invalidNodes} 个\n";
    
    // 4. 验证模型文件
    echo "\n4. 模型文件验证\n";
    echo "===============\n";
    
    $modelDir = __DIR__ . '/app/admin/model';
    $expectedModels = [
        'PaperType', 'OutlineTemplate', 'PromptTemplate', 'DocumentTemplate', 'PaperProject',
        'RewriteResult', 'AiModel', 'CheckTask', 'RewriteTask',
        'Invoice', 'Package', 'Order', 'MessageTemplate', 'N8nWorkflow'
    ];
    
    $existingModels = [];
    $missingModels = [];
    
    foreach ($expectedModels as $model) {
        $filePath = $modelDir . '/' . $model . '.php';
        if (file_exists($filePath)) {
            $existingModels[] = $model;
            echo "✅ {$model}.php\n";
        } else {
            $missingModels[] = $model;
            echo "❌ {$model}.php (缺失)\n";
        }
    }
    
    echo "\n模型统计:\n";
    echo "- 预期: " . count($expectedModels) . " 个\n";
    echo "- 存在: " . count($existingModels) . " 个\n";
    echo "- 缺失: " . count($missingModels) . " 个\n";
    
    // 5. 生成完整性报告
    echo "\n5. 完整性报告\n";
    echo "=============\n";
    
    $controllerCompleteness = count($existingControllers) / count($expectedControllers) * 100;
    $menuCompleteness = $totalExistingMenus / $totalExpectedMenus * 100;
    $modelCompleteness = count($existingModels) / count($expectedModels) * 100;
    $nodeValidness = $validNodes / ($validNodes + $invalidNodes) * 100;
    
    echo "控制器完整性: " . round($controllerCompleteness, 2) . "%\n";
    echo "菜单完整性: " . round($menuCompleteness, 2) . "%\n";
    echo "模型完整性: " . round($modelCompleteness, 2) . "%\n";
    echo "权限节点有效性: " . round($nodeValidness, 2) . "%\n";
    
    $overallCompleteness = ($controllerCompleteness + $menuCompleteness + $modelCompleteness + $nodeValidness) / 4;
    echo "\n总体完整性: " . round($overallCompleteness, 2) . "%\n";
    
    // 6. 生成建议
    echo "\n6. 改进建议\n";
    echo "===========\n";
    
    if (!empty($missingControllers)) {
        echo "需要创建的控制器:\n";
        foreach ($missingControllers as $controller) {
            echo "- {$controller}.php\n";
        }
        echo "\n";
    }
    
    if (!empty($missingMenus)) {
        echo "需要添加的菜单:\n";
        foreach ($missingMenus as $menu) {
            echo "- {$menu}\n";
        }
        echo "\n";
    }
    
    if (!empty($missingModels)) {
        echo "需要创建的模型:\n";
        foreach ($missingModels as $model) {
            echo "- {$model}.php\n";
        }
        echo "\n";
    }
    
    if ($overallCompleteness >= 95) {
        echo "🎉 系统实现度非常高！可以开始功能测试。\n";
    } elseif ($overallCompleteness >= 80) {
        echo "✅ 系统基本完整，建议完善缺失项目后进行测试。\n";
    } else {
        echo "⚠️ 系统还需要进一步完善，请优先处理缺失的核心组件。\n";
    }
    
    // 7. 生成权限配置文档
    echo "\n7. 权限配置参考\n";
    echo "===============\n";
    echo "以下是控制器中@auth注解的标准配置:\n\n";
    
    $authExamples = [
        'index' => '@auth true  // 列表页面权限',
        'add' => '@auth true   // 添加功能权限',
        'edit' => '@auth true  // 编辑功能权限',
        'remove' => '@auth true // 删除功能权限',
        'state' => '@auth true  // 状态切换权限'
    ];
    
    foreach ($authExamples as $method => $annotation) {
        echo "public function {$method}() {\n";
        echo "    /**\n";
        echo "     * {$annotation}\n";
        echo "     */\n";
        echo "}\n\n";
    }
    
    echo "=== 验证完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
