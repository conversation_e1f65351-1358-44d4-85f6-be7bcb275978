<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\User;
use think\admin\Controller;
use think\admin\helper\QueryHelper;
use think\admin\model\SystemNotice as SystemNoticeModel;

/**
 * 系统通知记录
 * @class SystemNotice
 * @package app\admin\controller
 */
class SystemNotice extends Controller
{
    /**
     * 系统通知记录
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        SystemNoticeModel::mQuery()->layTable(function () {
            $this->title = '系统通知记录';
            $this->typeOptions = SystemNoticeModel::getTypeOptions();
            $this->statusOptions = SystemNoticeModel::getStatusOptions();
        }, static function (QueryHelper $query) {
            $query->with(['user']);
            $query->like('title,content,user.username,user.nickname')->equal('type,status,is_read');
            $query->dateBetween('create_time,read_time,send_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加系统通知
     * @auth true
     */
    public function add()
    {
        SystemNoticeModel::mForm('form');
    }

    /**
     * 编辑系统通知
     * @auth true
     */
    public function edit()
    {
        SystemNoticeModel::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = SystemNoticeModel::getTypeOptions();
            $this->statusOptions = SystemNoticeModel::getStatusOptions();
            
            // 获取用户列表
            $this->userOptions = User::mk()
                ->where('status', 1)
                ->field('id,username,nickname')
                ->order('id desc')
                ->limit(100)
                ->select()
                ->toArray();
        } else {
            // 验证必要字段
            if (empty($vo['title'])) {
                $this->error('通知标题不能为空！');
            }
            
            if (empty($vo['content'])) {
                $this->error('通知内容不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择通知类型！');
            }
            
            // 如果是指定用户通知，验证用户ID
            if ($vo['type'] == 'user' && empty($vo['user_id'])) {
                $this->error('请选择接收用户！');
            }
            
            // 如果是指定用户通知，验证用户是否存在
            if (!empty($vo['user_id'])) {
                $user = User::mk()->findOrEmpty($vo['user_id']);
                if ($user->isEmpty()) {
                    $this->error('指定用户不存在！');
                }
            }
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
                $vo['send_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('通知保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除系统通知
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notice = SystemNoticeModel::mk()->findOrEmpty($id);
        if ($notice->isEmpty()) {
            $this->error('通知不存在！');
        }

        if ($notice->delete()) {
            $this->success('通知删除成功！');
        } else {
            $this->error('通知删除失败！');
        }
    }

    /**
     * 修改通知状态
     * @auth true
     */
    public function state()
    {
        SystemNoticeModel::mSave($this->_vali([
            'status.in:active,inactive'  => '状态值范围异常！',
        ]));
    }

    /**
     * 批量发送通知
     * @auth true
     */
    public function batchSend()
    {
        if ($this->request->isGet()) {
            $this->title = '批量发送通知';
            
            // 获取用户列表
            $this->userOptions = User::mk()
                ->where('status', 1)
                ->field('id,username,nickname')
                ->order('id desc')
                ->limit(200)
                ->select()
                ->toArray();
                
            return $this->fetch('system_notice/batch_send');
        }

        $title = $this->request->post('title', '');
        $content = $this->request->post('content', '');
        $userIds = $this->request->post('user_ids', '');
        $sendType = $this->request->post('send_type', 'selected'); // selected, all

        if (empty($title)) {
            $this->error('通知标题不能为空！');
        }

        if (empty($content)) {
            $this->error('通知内容不能为空！');
        }

        $targetUsers = [];
        if ($sendType == 'all') {
            // 发送给所有用户
            $targetUsers = User::mk()
                ->where('status', 1)
                ->field('id,username,nickname')
                ->select()
                ->toArray();
        } else {
            // 发送给指定用户
            if (empty($userIds)) {
                $this->error('请选择接收用户！');
            }
            
            $userIdArray = explode(',', $userIds);
            $targetUsers = User::mk()
                ->whereIn('id', $userIdArray)
                ->where('status', 1)
                ->field('id,username,nickname')
                ->select()
                ->toArray();
        }

        if (empty($targetUsers)) {
            $this->error('没有找到有效的接收用户！');
        }

        $successCount = 0;
        $failCount = 0;
        $currentTime = date('Y-m-d H:i:s');

        foreach ($targetUsers as $user) {
            $noticeData = [
                'user_id' => $user['id'],
                'type' => 'user',
                'title' => $title,
                'content' => $content,
                'status' => 'active',
                'is_read' => 0,
                'create_time' => $currentTime,
                'send_time' => $currentTime,
                'update_time' => $currentTime
            ];

            $result = SystemNoticeModel::mk()->save($noticeData);
            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量发送完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 通知详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notice = SystemNoticeModel::mk()->with(['user'])->findOrEmpty($id);
        if ($notice->isEmpty()) {
            $this->error('通知不存在！');
        }

        $this->assign('notice', $notice);
        $this->assign('title', '通知详情');
        
        return $this->fetch('system_notice/view');
    }

    /**
     * 标记为已读
     * @auth true
     */
    public function markRead()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('通知ID不能为空！');
        }

        $notice = SystemNoticeModel::mk()->findOrEmpty($id);
        if ($notice->isEmpty()) {
            $this->error('通知不存在！');
        }

        if ($notice->is_read) {
            $this->error('通知已经是已读状态！');
        }

        $result = $notice->save([
            'is_read' => 1,
            'read_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $this->success('标记已读成功！');
        } else {
            $this->error('标记已读失败！');
        }
    }

    /**
     * 批量标记已读
     * @auth true
     */
    public function batchMarkRead()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要标记的通知！');
        }

        $idArray = explode(',', $ids);
        $result = SystemNoticeModel::mk()
            ->whereIn('id', $idArray)
            ->where('is_read', 0)
            ->save([
                'is_read' => 1,
                'read_time' => date('Y-m-d H:i:s')
            ]);

        if ($result) {
            $this->success('批量标记已读成功！');
        } else {
            $this->error('批量标记已读失败！');
        }
    }

    /**
     * 通知统计
     * @auth true
     */
    public function statistics()
    {
        // 总通知数
        $totalNotices = SystemNoticeModel::mk()->count();
        
        // 今日通知数
        $todayNotices = SystemNoticeModel::mk()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月通知数
        $monthNotices = SystemNoticeModel::mk()
            ->whereTime('create_time', 'month')
            ->count();
        
        // 未读通知数
        $unreadNotices = SystemNoticeModel::mk()
            ->where('is_read', 0)
            ->count();
        
        // 已读通知数
        $readNotices = SystemNoticeModel::mk()
            ->where('is_read', 1)
            ->count();
        
        // 通知类型分布
        $typeStats = SystemNoticeModel::mk()
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 最近7天通知趋势
        $weekTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $count = SystemNoticeModel::mk()
                ->whereTime('create_time', $date)
                ->count();
            
            $weekTrend[] = [
                'date' => $date,
                'count' => $count
            ];
        }
        
        // 用户阅读率统计（前10）
        $readRateStats = SystemNoticeModel::mk()
            ->with(['user'])
            ->field('user_id, COUNT(*) as total_notices, SUM(is_read) as read_notices')
            ->where('user_id', '>', 0)
            ->group('user_id')
            ->having('total_notices', '>', 0)
            ->order('total_notices desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 计算阅读率
        foreach ($readRateStats as &$stat) {
            $stat['read_rate'] = $stat['total_notices'] > 0 
                ? round(($stat['read_notices'] / $stat['total_notices']) * 100, 2) 
                : 0;
        }

        $statistics = [
            'total_notices' => $totalNotices,
            'today_notices' => $todayNotices,
            'month_notices' => $monthNotices,
            'unread_notices' => $unreadNotices,
            'read_notices' => $readNotices,
            'read_rate' => $totalNotices > 0 ? round(($readNotices / $totalNotices) * 100, 2) : 0,
            'type_stats' => $typeStats,
            'week_trend' => $weekTrend,
            'read_rate_stats' => $readRateStats
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '通知统计');
        return $this->fetch('system_notice/statistics');
    }

    /**
     * 清理过期通知
     * @auth true
     */
    public function cleanup()
    {
        $days = $this->request->post('days', 30);
        
        if ($days < 1) {
            $this->error('清理天数必须大于0！');
        }

        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // 只清理已读的通知
        $count = SystemNoticeModel::mk()
            ->where('is_read', 1)
            ->where('create_time', '<', $expireDate)
            ->delete();

        $this->success("清理完成！共清理了 {$count} 条过期通知。");
    }

    /**
     * 导出通知记录
     * @auth true
     */
    public function export()
    {
        $startDate = $this->request->post('start_date', '');
        $endDate = $this->request->post('end_date', '');
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = SystemNoticeModel::mk()->with(['user']);

        if (!empty($startDate)) {
            $query->where('create_time', '>=', $startDate . ' 00:00:00');
        }

        if (!empty($endDate)) {
            $query->where('create_time', '<=', $endDate . ' 23:59:59');
        }

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        $notices = $query->order('id desc')->select();

        if ($notices->isEmpty()) {
            $this->error('没有找到要导出的记录！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '接收用户', '通知类型', '标题', '内容', '是否已读', '状态', '发送时间', '阅读时间'];
        
        foreach ($notices as $notice) {
            $exportData[] = [
                $notice->id,
                $notice->user->username ?? '系统通知',
                SystemNoticeModel::getTypeOptions()[$notice->type] ?? $notice->type,
                $notice->title,
                mb_substr($notice->content, 0, 100) . (mb_strlen($notice->content) > 100 ? '...' : ''),
                $notice->is_read ? '是' : '否',
                SystemNoticeModel::getStatusOptions()[$notice->status] ?? $notice->status,
                $notice->send_time,
                $notice->read_time ?: ''
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 批量删除通知
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的通知！');
        }

        $idArray = explode(',', $ids);
        $result = SystemNoticeModel::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }
}
