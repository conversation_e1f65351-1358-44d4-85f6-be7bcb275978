<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\Package;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 套餐配置
 * @class PackageConfig
 * @package app\admin\controller
 */
class PackageConfig extends Controller
{
    /**
     * 套餐配置
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        Package::mQuery()->layTable(function () {
            $this->title = '套餐配置';
            $this->typeOptions = Package::getTypeOptions();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
        }, static function (QueryHelper $query) {
            $query->like('name,description')->equal('type,status,is_hot,is_recommended');
            $query->dateBetween('create_time,update_time');
            $query->order('sort asc, id desc');
        });
    }

    /**
     * 添加套餐
     * @auth true
     */
    public function add()
    {
        Package::mForm('form');
    }

    /**
     * 编辑套餐
     * @auth true
     */
    public function edit()
    {
        Package::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->typeOptions = Package::getTypeOptions();
            $this->statusOptions = ['0' => '禁用', '1' => '启用'];
            
            // 如果是编辑，解析配置数据
            if (!empty($vo['id'])) {
                if (!empty($vo['features']) && is_string($vo['features'])) {
                    $vo['features'] = json_decode($vo['features'], true) ?: [];
                }
                if (!empty($vo['limitations']) && is_string($vo['limitations'])) {
                    $vo['limitations'] = json_decode($vo['limitations'], true) ?: [];
                }
            }
        } else {
            // 验证必要字段
            if (empty($vo['name'])) {
                $this->error('套餐名称不能为空！');
            }
            
            if (empty($vo['type'])) {
                $this->error('请选择套餐类型！');
            }
            
            if (!isset($vo['price']) || $vo['price'] < 0) {
                $this->error('套餐价格不能为负数！');
            }
            
            // 处理特性和限制
            if (!empty($vo['features']) && is_array($vo['features'])) {
                $vo['features'] = json_encode($vo['features'], JSON_UNESCAPED_UNICODE);
            } else {
                $vo['features'] = '[]';
            }
            
            if (!empty($vo['limitations']) && is_array($vo['limitations'])) {
                $vo['limitations'] = json_encode($vo['limitations'], JSON_UNESCAPED_UNICODE);
            } else {
                $vo['limitations'] = '[]';
            }
            
            // 验证套餐配置
            $this->validatePackageConfig($vo);
            
            // 设置时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 验证套餐配置
     * @param array $vo
     */
    private function validatePackageConfig(array $vo): void
    {
        switch ($vo['type']) {
            case 'vip':
                if (empty($vo['duration_days'])) {
                    $this->error('VIP套餐必须设置有效期天数！');
                }
                break;
                
            case 'credits':
                if (empty($vo['credits_amount'])) {
                    $this->error('积分套餐必须设置积分数量！');
                }
                break;
                
            case 'combo':
                if (empty($vo['duration_days']) && empty($vo['credits_amount'])) {
                    $this->error('组合套餐必须设置VIP天数或积分数量！');
                }
                break;
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('套餐配置保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除套餐
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 检查是否有关联订单
        $orderCount = $package->orders()->count();
        if ($orderCount > 0) {
            $this->error('该套餐已有关联订单，不能删除！');
        }

        if ($package->delete()) {
            $this->success('套餐删除成功！');
        } else {
            $this->error('套餐删除失败！');
        }
    }

    /**
     * 修改套餐状态
     * @auth true
     */
    public function state()
    {
        Package::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 设置热门套餐
     * @auth true
     */
    public function setHot()
    {
        $id = $this->request->post('id', 0);
        $isHot = $this->request->post('is_hot', 0);
        
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        if ($package->save(['is_hot' => $isHot ? 1 : 0])) {
            $message = $isHot ? '设置热门成功！' : '取消热门成功！';
            $this->success($message);
        } else {
            $this->error('操作失败！');
        }
    }

    /**
     * 设置推荐套餐
     * @auth true
     */
    public function setRecommended()
    {
        $id = $this->request->post('id', 0);
        $isRecommended = $this->request->post('is_recommended', 0);
        
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        if ($package->save(['is_recommended' => $isRecommended ? 1 : 0])) {
            $message = $isRecommended ? '设置推荐成功！' : '取消推荐成功！';
            $this->success($message);
        } else {
            $this->error('操作失败！');
        }
    }

    /**
     * 复制套餐
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 创建副本
        $copyData = $package->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['is_hot'] = 0;
        $copyData['is_recommended'] = 0;
        $copyData['status'] = 0; // 默认禁用
        $copyData['sales_count'] = 0;
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $result = Package::mk()->save($copyData);
        if ($result) {
            $this->success('套餐复制成功！');
        } else {
            $this->error('套餐复制失败！');
        }
    }

    /**
     * 批量设置排序
     * @auth true
     */
    public function sort()
    {
        $ids = $this->request->post('ids', '');
        $sorts = $this->request->post('sorts', '');
        
        if (empty($ids) || empty($sorts)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $sortArray = explode(',', $sorts);
        
        if (count($idArray) !== count($sortArray)) {
            $this->error('参数数量不匹配！');
        }

        $updateData = [];
        foreach ($idArray as $index => $id) {
            $updateData[] = [
                'id' => $id,
                'sort' => intval($sortArray[$index])
            ];
        }

        $result = Package::mk()->saveAll($updateData);
        if ($result) {
            $this->success('排序设置成功！');
        } else {
            $this->error('排序设置失败！');
        }
    }

    /**
     * 套餐详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('套餐ID不能为空！');
        }

        $package = Package::mk()->with(['orders'])->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        // 获取销售统计
        $salesStats = $this->getPackageSalesStats($package);
        
        // 获取最近订单
        $recentOrders = $package->orders()
            ->with(['user'])
            ->order('id desc')
            ->limit(10)
            ->select();

        $this->assign('package', $package);
        $this->assign('salesStats', $salesStats);
        $this->assign('recentOrders', $recentOrders);
        $this->assign('title', '套餐详情');
        
        return $this->fetch('package_config/view');
    }

    /**
     * 获取套餐销售统计
     * @param Package $package
     * @return array
     */
    private function getPackageSalesStats(Package $package): array
    {
        // 总销售量
        $totalSales = $package->orders()->where('payment_status', 'paid')->count();
        
        // 总销售额
        $totalRevenue = $package->orders()
            ->where('payment_status', 'paid')
            ->sum('final_price') ?: 0;
        
        // 今日销售量
        $todaySales = $package->orders()
            ->where('payment_status', 'paid')
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月销售量
        $monthSales = $package->orders()
            ->where('payment_status', 'paid')
            ->whereTime('create_time', 'month')
            ->count();
        
        // 转化率（模拟数据）
        $viewCount = $totalSales * rand(5, 20); // 假设查看次数是销售量的5-20倍
        $conversionRate = $viewCount > 0 ? round(($totalSales / $viewCount) * 100, 2) : 0;
        
        return [
            'total_sales' => $totalSales,
            'total_revenue' => $totalRevenue,
            'today_sales' => $todaySales,
            'month_sales' => $monthSales,
            'conversion_rate' => $conversionRate,
            'avg_order_value' => $totalSales > 0 ? round($totalRevenue / $totalSales, 2) : 0
        ];
    }

    /**
     * 套餐统计
     * @auth true
     */
    public function statistics()
    {
        // 总套餐数
        $totalPackages = Package::mk()->count();
        
        // 启用的套餐数
        $activePackages = Package::mk()->where('status', 1)->count();
        
        // 各类型套餐数
        $typeStats = Package::mk()
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();
        
        // 热门套餐数
        $hotPackages = Package::mk()->where('is_hot', 1)->count();
        
        // 推荐套餐数
        $recommendedPackages = Package::mk()->where('is_recommended', 1)->count();
        
        // 销售排行榜（前10）
        $salesRanking = Package::mk()
            ->field('id,name,type,price,sales_count')
            ->where('status', 1)
            ->order('sales_count desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 收入排行榜（前10）
        $revenueRanking = Package::mk()
            ->field('id,name,type,price,sales_count,(price * sales_count) as total_revenue')
            ->where('status', 1)
            ->order('total_revenue desc')
            ->limit(10)
            ->select()
            ->toArray();
        
        // 今日新增套餐
        $todayPackages = Package::mk()
            ->whereTime('create_time', 'today')
            ->count();
        
        // 本月新增套餐
        $monthPackages = Package::mk()
            ->whereTime('create_time', 'month')
            ->count();

        $statistics = [
            'total_packages' => $totalPackages,
            'active_packages' => $activePackages,
            'hot_packages' => $hotPackages,
            'recommended_packages' => $recommendedPackages,
            'today_packages' => $todayPackages,
            'month_packages' => $monthPackages,
            'type_stats' => $typeStats,
            'sales_ranking' => $salesRanking,
            'revenue_ranking' => $revenueRanking
        ];

        if ($this->request->isAjax()) {
            return json($statistics);
        }

        $this->assign('statistics', $statistics);
        $this->assign('title', '套餐统计');
        return $this->fetch('package_config/statistics');
    }

    /**
     * 价格调整
     * @auth true
     */
    public function adjustPrice()
    {
        if ($this->request->isGet()) {
            $id = $this->request->get('id', 0);
            if (!empty($id)) {
                $package = Package::mk()->findOrEmpty($id);
                $this->assign('package', $package);
            }
            
            $this->title = '价格调整';
            return $this->fetch('package_config/adjust_price');
        }

        $id = $this->request->post('id', 0);
        $newPrice = $this->request->post('new_price', 0);
        $reason = $this->request->post('reason', '');

        if (empty($id)) {
            $this->error('请选择套餐！');
        }

        if ($newPrice < 0) {
            $this->error('价格不能为负数！');
        }

        if (empty($reason)) {
            $this->error('请填写调整原因！');
        }

        $package = Package::mk()->findOrEmpty($id);
        if ($package->isEmpty()) {
            $this->error('套餐不存在！');
        }

        $oldPrice = $package->price;
        
        // 记录价格变更历史（这里应该保存到价格历史表）
        $priceHistory = [
            'package_id' => $id,
            'old_price' => $oldPrice,
            'new_price' => $newPrice,
            'reason' => $reason,
            'operator_id' => session('user.id'),
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        // 更新套餐价格
        $result = $package->save([
            'price' => $newPrice,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            // 这里应该保存价格历史记录
            // PackagePriceHistory::mk()->save($priceHistory);
            
            $this->success('价格调整成功！');
        } else {
            $this->error('价格调整失败！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', '');

        if (empty($action) || empty($ids)) {
            $this->error('参数不完整！');
        }

        $idArray = explode(',', $ids);
        $successCount = 0;
        $failCount = 0;

        foreach ($idArray as $id) {
            $package = Package::mk()->findOrEmpty($id);
            if ($package->isEmpty()) {
                $failCount++;
                continue;
            }

            switch ($action) {
                case 'enable':
                    $result = $package->save(['status' => 1]);
                    break;
                case 'disable':
                    $result = $package->save(['status' => 0]);
                    break;
                case 'set_hot':
                    $result = $package->save(['is_hot' => 1]);
                    break;
                case 'unset_hot':
                    $result = $package->save(['is_hot' => 0]);
                    break;
                case 'set_recommended':
                    $result = $package->save(['is_recommended' => 1]);
                    break;
                case 'unset_recommended':
                    $result = $package->save(['is_recommended' => 0]);
                    break;
                case 'delete':
                    // 检查是否有关联订单
                    if ($package->orders()->count() == 0) {
                        $result = $package->delete();
                    } else {
                        $result = false;
                    }
                    break;
                default:
                    $result = false;
                    break;
            }

            if ($result) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->success("批量操作完成！成功：{$successCount}，失败：{$failCount}");
    }

    /**
     * 导出套餐配置
     * @auth true
     */
    public function export()
    {
        $type = $this->request->post('type', '');
        $status = $this->request->post('status', '');

        $query = Package::mk();

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $packages = $query->order('sort asc, id desc')->select();

        if ($packages->isEmpty()) {
            $this->error('没有找到要导出的套餐！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '套餐名称', '套餐类型', '价格', '销售量', '是否热门', '是否推荐', '状态', '创建时间'];
        
        foreach ($packages as $package) {
            $exportData[] = [
                $package->id,
                $package->name,
                Package::getTypeOptions()[$package->type] ?? $package->type,
                $package->price,
                $package->sales_count,
                $package->is_hot ? '是' : '否',
                $package->is_recommended ? '是' : '否',
                $package->status ? '启用' : '禁用',
                $package->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }
}
