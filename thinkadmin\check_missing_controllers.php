<?php

/**
 * 检查缺失的控制器
 */

echo "=== 检查缺失的控制器 ===\n\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在\n";
        exit(1);
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
    
    // 获取所有菜单项
    echo "1. 检查菜单项对应的控制器:\n";
    $stmt = $pdo->query("SELECT title, node, url FROM system_menu WHERE status = 1 AND node != '' ORDER BY sort ASC, id ASC");
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $missingControllers = [];
    $existingControllers = [];
    
    foreach ($menus as $menu) {
        $node = $menu['node'];
        $title = $menu['title'];
        
        // 解析控制器路径
        if (strpos($node, 'admin/') === 0) {
            $controllerPath = str_replace('admin/', '', $node);
            $parts = explode('/', $controllerPath);
            
            if (count($parts) >= 2) {
                $controllerName = ucfirst($parts[0]);
                $method = $parts[1];
                $controllerFile = "app/admin/controller/{$controllerName}.php";
                
                if (file_exists($controllerFile)) {
                    $existingControllers[] = [
                        'title' => $title,
                        'controller' => $controllerName,
                        'method' => $method,
                        'file' => $controllerFile
                    ];
                    echo "  ✅ {$title}: {$controllerName}::{$method} - 控制器存在\n";
                } else {
                    $missingControllers[] = [
                        'title' => $title,
                        'controller' => $controllerName,
                        'method' => $method,
                        'file' => $controllerFile,
                        'node' => $node
                    ];
                    echo "  ❌ {$title}: {$controllerName}::{$method} - 控制器不存在\n";
                }
            }
        }
    }
    
    echo "\n2. 缺失控制器统计:\n";
    if (empty($missingControllers)) {
        echo "  🎉 所有菜单项都有对应的控制器！\n";
    } else {
        echo "  📊 共有 " . count($missingControllers) . " 个控制器缺失:\n";
        
        $controllerGroups = [];
        foreach ($missingControllers as $missing) {
            $controllerGroups[$missing['controller']][] = $missing;
        }
        
        foreach ($controllerGroups as $controllerName => $methods) {
            echo "    - {$controllerName}.php (需要方法: ";
            $methodNames = array_unique(array_column($methods, 'method'));
            echo implode(', ', $methodNames) . ")\n";
            
            foreach ($methods as $method) {
                echo "      * {$method['title']} -> {$method['method']}()\n";
            }
        }
    }
    
    echo "\n3. 现有控制器统计:\n";
    $existingGroups = [];
    foreach ($existingControllers as $existing) {
        $existingGroups[$existing['controller']][] = $existing;
    }
    
    echo "  📊 共有 " . count($existingGroups) . " 个控制器存在:\n";
    foreach ($existingGroups as $controllerName => $methods) {
        echo "    ✅ {$controllerName}.php (方法: ";
        $methodNames = array_unique(array_column($methods, 'method'));
        echo implode(', ', $methodNames) . ")\n";
    }
    
    // 检查控制器文件的实际方法
    echo "\n4. 检查控制器方法完整性:\n";
    foreach ($existingGroups as $controllerName => $methods) {
        $controllerFile = "app/admin/controller/{$controllerName}.php";
        if (file_exists($controllerFile)) {
            $content = file_get_contents($controllerFile);
            $requiredMethods = array_unique(array_column($methods, 'method'));
            
            $missingMethods = [];
            foreach ($requiredMethods as $method) {
                if (strpos($content, "public function {$method}()") === false) {
                    $missingMethods[] = $method;
                }
            }
            
            if (empty($missingMethods)) {
                echo "  ✅ {$controllerName}: 所有方法都存在\n";
            } else {
                echo "  ⚠️  {$controllerName}: 缺少方法 " . implode(', ', $missingMethods) . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";
