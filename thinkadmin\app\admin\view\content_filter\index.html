{extend name='admin@public/layout'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-set"></span>
        内容风控规则
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">启用内容过滤</label>
                <div class="layui-input-block">
                    <input type="checkbox" name="content_filter_enable" value="1" lay-skin="switch" lay-text="启用|禁用" {if $configs.content_filter_enable}checked{/if}>
                </div>
            </div>            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">敏感词列表</label>
                <div class="layui-input-block">
                    <textarea name="sensitive_words" placeholder="请输入敏感词列表" class="layui-textarea">{$configs.sensitive_words}</textarea>
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">过滤级别</label>
                <div class="layui-input-block">
                    <input type="text" name="filter_level" value="{$configs.filter_level}" placeholder="请输入过滤级别" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">自动替换敏感词</label>
                <div class="layui-input-block">
                    <input type="text" name="auto_replace" value="{$configs.auto_replace}" placeholder="请输入自动替换敏感词" class="layui-input">
                </div>
            </div>            <div class="layui-form-item">
                <label class="layui-form-label">替换字符</label>
                <div class="layui-input-block">
                    <input type="text" name="replacement_char" value="{$configs.replacement_char}" placeholder="请输入替换字符" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="configForm">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.form.on('submit(configForm)', function(data){
        $.form.load('', data.field, 'post', function(ret){
            if(ret.code === 1){
                $.msg.success(ret.info);
            } else {
                $.msg.error(ret.info);
            }
        });
        return false;
    });
</script>
{/block}