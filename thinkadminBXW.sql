-- =====================================================
-- AI论文写作平台数据库设计 (FastAdmin + n8n 架构)
-- 表前缀: bxw_
-- 创建时间: 2025-01-01
-- 说明: 基于FastAdmin框架的AI论文写作平台数据库结构
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 用户相关表
-- =====================================================

-- 用户基础信息表
DROP TABLE IF EXISTS `bxw_user`;
CREATE TABLE `bxw_user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希值',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=正常',
  `user_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '用户类型：1=普通用户，2=VIP用户，3=企业用户',
  `credits` int(10) NOT NULL DEFAULT '0' COMMENT '积分余额',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP到期时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `status` (`status`),
  KEY `user_type` (`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户配额限制表
DROP TABLE IF EXISTS `bxw_user_quota`;
CREATE TABLE `bxw_user_quota` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `quota_type` varchar(50) NOT NULL COMMENT '配额类型：writing=写作，rewrite=降重，check=查重',
  `daily_limit` int(10) NOT NULL DEFAULT '0' COMMENT '每日限制次数，0=无限制',
  `monthly_limit` int(10) NOT NULL DEFAULT '0' COMMENT '每月限制次数，0=无限制',
  `daily_used` int(10) NOT NULL DEFAULT '0' COMMENT '今日已使用次数',
  `monthly_used` int(10) NOT NULL DEFAULT '0' COMMENT '本月已使用次数',
  `last_reset_date` date NOT NULL COMMENT '最后重置日期',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_quota` (`user_id`,`quota_type`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配额限制表';

-- =====================================================
-- 2. 论文写作相关表
-- =====================================================

-- 论文类型表
DROP TABLE IF EXISTS `bxw_paper_type`;
CREATE TABLE `bxw_paper_type` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '论文类型名称',
  `description` text COMMENT '类型描述',
  `word_count_min` int(10) NOT NULL DEFAULT '6000' COMMENT '最小字数',
  `word_count_max` int(10) NOT NULL DEFAULT '30000' COMMENT '最大字数',
  `outline_template_id` int(10) DEFAULT NULL COMMENT '默认大纲模板ID',
  `prompt_template_id` int(10) DEFAULT NULL COMMENT '默认提示词模板ID',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文类型表';

-- 大纲模板表
DROP TABLE IF EXISTS `bxw_outline_template`;
CREATE TABLE `bxw_outline_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '关联论文类型ID',
  `template_content` text NOT NULL COMMENT '模板内容，JSON格式存储大纲结构',
  `description` text COMMENT '模板描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `usage_count` int(10) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `paper_type_id` (`paper_type_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大纲模板表';

-- 提示词模板表
DROP TABLE IF EXISTS `bxw_prompt_template`;
CREATE TABLE `bxw_prompt_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(50) NOT NULL COMMENT '模板类型：outline=大纲生成，writing=写作，rewrite=降重',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '关联论文类型ID',
  `prompt_content` text NOT NULL COMMENT '提示词内容，支持变量替换',
  `variables` text COMMENT '变量说明，JSON格式',
  `ai_model` varchar(50) DEFAULT NULL COMMENT '推荐AI模型',
  `description` text COMMENT '模板描述',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `usage_count` int(10) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `paper_type_id` (`paper_type_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提示词模板表';

-- 论文项目表
DROP TABLE IF EXISTS `bxw_paper_project`;
CREATE TABLE `bxw_paper_project` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '论文标题',
  `paper_type_id` int(10) unsigned NOT NULL COMMENT '论文类型ID',
  `subject` varchar(100) DEFAULT NULL COMMENT '学科领域',
  `keywords` varchar(500) DEFAULT NULL COMMENT '关键词，逗号分隔',
  `requirements` text COMMENT '具体要求描述',
  `target_word_count` int(10) NOT NULL DEFAULT '10000' COMMENT '目标字数',
  `writing_style` varchar(50) NOT NULL DEFAULT 'academic' COMMENT '写作风格：academic=学术，popular=通俗，oral=口语，international=国际',
  `outline_content` longtext COMMENT '确认的大纲内容',
  `outline_version` int(10) NOT NULL DEFAULT '1' COMMENT '大纲版本号',
  `content` longtext COMMENT '论文正文内容',
  `current_word_count` int(10) NOT NULL DEFAULT '0' COMMENT '当前字数',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft=草稿，outline_generating=大纲生成中，outline_completed=大纲完成，writing=写作中，completed=已完成，failed=失败',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `progress` tinyint(3) NOT NULL DEFAULT '0' COMMENT '完成进度百分比',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `paper_type_id` (`paper_type_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文项目表';

-- 论文章节表
DROP TABLE IF EXISTS `bxw_paper_section`;
CREATE TABLE `bxw_paper_section` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `project_id` int(10) unsigned NOT NULL COMMENT '论文项目ID',
  `section_title` varchar(255) NOT NULL COMMENT '章节标题',
  `section_level` tinyint(2) NOT NULL DEFAULT '1' COMMENT '章节层级：1=一级标题，2=二级标题，3=三级标题',
  `section_order` int(10) NOT NULL DEFAULT '0' COMMENT '章节排序',
  `content` longtext COMMENT '章节内容',
  `word_count` int(10) NOT NULL DEFAULT '0' COMMENT '字数统计',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待生成，generating=生成中，completed=已完成，failed=失败',
  `ai_model_used` varchar(50) DEFAULT NULL COMMENT '使用的AI模型',
  `generation_time` datetime DEFAULT NULL COMMENT '生成时间',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `project_id` (`project_id`),
  KEY `status` (`status`),
  KEY `section_order` (`section_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文章节表';

-- =====================================================
-- 3. AI模型配置相关表
-- =====================================================

-- AI模型配置表
DROP TABLE IF EXISTS `bxw_ai_model`;
CREATE TABLE `bxw_ai_model` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模型名称',
  `provider` varchar(50) NOT NULL COMMENT '提供商：openai，baidu，aliyun，anthropic等',
  `model_code` varchar(100) NOT NULL COMMENT '模型代码标识',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点URL',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥（加密存储）',
  `max_tokens` int(10) NOT NULL DEFAULT '4000' COMMENT '最大token数',
  `temperature` decimal(3,2) NOT NULL DEFAULT '0.70' COMMENT '温度参数',
  `top_p` decimal(3,2) NOT NULL DEFAULT '1.00' COMMENT 'top_p参数',
  `frequency_penalty` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '存在惩罚',
  `cost_per_1k_tokens` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT '每1K token成本',
  `priority` int(10) NOT NULL DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
  `rate_limit_per_minute` int(10) NOT NULL DEFAULT '60' COMMENT '每分钟请求限制',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `health_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '健康状态：0=异常，1=正常',
  `last_health_check` datetime DEFAULT NULL COMMENT '最后健康检查时间',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_model` (`provider`,`model_code`),
  KEY `status` (`status`),
  KEY `priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';

-- AI模型使用统计表
DROP TABLE IF EXISTS `bxw_ai_usage_log`;
CREATE TABLE `bxw_ai_usage_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `ai_model_id` int(10) unsigned NOT NULL COMMENT 'AI模型ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型：outline=大纲生成，writing=写作，rewrite=降重',
  `task_id` int(10) unsigned DEFAULT NULL COMMENT '关联任务ID',
  `prompt_tokens` int(10) NOT NULL DEFAULT '0' COMMENT '输入token数',
  `completion_tokens` int(10) NOT NULL DEFAULT '0' COMMENT '输出token数',
  `total_tokens` int(10) NOT NULL DEFAULT '0' COMMENT '总token数',
  `cost` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT '本次调用成本',
  `response_time` int(10) NOT NULL DEFAULT '0' COMMENT '响应时间（毫秒）',
  `status` varchar(20) NOT NULL DEFAULT 'success' COMMENT '状态：success=成功，failed=失败，timeout=超时',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `ai_model_id` (`ai_model_id`),
  KEY `task_type` (`task_type`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型使用统计表';

-- =====================================================
-- 4. 语义降重相关表
-- =====================================================

-- 降重任务表
DROP TABLE IF EXISTS `bxw_rewrite_task`;
CREATE TABLE `bxw_rewrite_task` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '任务标题',
  `original_text` longtext NOT NULL COMMENT '原始文本内容',
  `original_word_count` int(10) NOT NULL DEFAULT '0' COMMENT '原文字数',
  `rewrite_mode` varchar(50) NOT NULL DEFAULT 'standard' COMMENT '降重模式：standard=标准，deep=深度，light=轻度',
  `target_similarity` decimal(5,2) NOT NULL DEFAULT '30.00' COMMENT '目标相似度百分比',
  `ai_model_id` int(10) unsigned DEFAULT NULL COMMENT '使用的AI模型ID',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待处理，processing=处理中，completed=已完成，failed=失败',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `progress` tinyint(3) NOT NULL DEFAULT '0' COMMENT '完成进度百分比',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降重任务表';

-- 降重结果表
DROP TABLE IF EXISTS `bxw_rewrite_result`;
CREATE TABLE `bxw_rewrite_result` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(10) unsigned NOT NULL COMMENT '降重任务ID',
  `ai_model_id` int(10) unsigned NOT NULL COMMENT 'AI模型ID',
  `rewritten_text` longtext NOT NULL COMMENT '降重后文本',
  `rewritten_word_count` int(10) NOT NULL DEFAULT '0' COMMENT '降重后字数',
  `similarity_score` decimal(5,2) DEFAULT NULL COMMENT '相似度评分',
  `quality_score` decimal(5,2) DEFAULT NULL COMMENT '质量评分',
  `processing_time` int(10) NOT NULL DEFAULT '0' COMMENT '处理时间（秒）',
  `is_selected` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被用户选中',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `ai_model_id` (`ai_model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降重结果表';

-- =====================================================
-- 5. 查重相关表
-- =====================================================

-- 查重接口配置表
DROP TABLE IF EXISTS `bxw_check_api`;
CREATE TABLE `bxw_check_api` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '接口名称',
  `provider` varchar(50) NOT NULL COMMENT '提供商：paperpass，weipu，wanfang，cnki等',
  `api_endpoint` varchar(255) NOT NULL COMMENT 'API端点URL',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥（加密存储）',
  `api_secret` varchar(255) DEFAULT NULL COMMENT 'API密钥（加密存储）',
  `cost_per_check` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '每次查重成本',
  `max_word_count` int(10) NOT NULL DEFAULT '50000' COMMENT '最大支持字数',
  `supported_formats` varchar(255) NOT NULL DEFAULT 'txt,doc,docx,pdf' COMMENT '支持的文件格式',
  `priority` int(10) NOT NULL DEFAULT '0' COMMENT '优先级',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `health_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '健康状态：0=异常，1=正常',
  `last_health_check` datetime DEFAULT NULL COMMENT '最后健康检查时间',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider` (`provider`),
  KEY `status` (`status`),
  KEY `priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查重接口配置表';

-- 查重任务表
DROP TABLE IF EXISTS `bxw_check_task`;
CREATE TABLE `bxw_check_task` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '文档标题',
  `file_path` varchar(255) DEFAULT NULL COMMENT '上传文件路径',
  `file_name` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `file_size` int(10) NOT NULL DEFAULT '0' COMMENT '文件大小（字节）',
  `word_count` int(10) NOT NULL DEFAULT '0' COMMENT '文档字数',
  `check_api_id` int(10) unsigned NOT NULL COMMENT '查重接口ID',
  `external_task_id` varchar(100) DEFAULT NULL COMMENT '第三方任务ID',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待提交，submitted=已提交，checking=查重中，completed=已完成，failed=失败',
  `similarity_rate` decimal(5,2) DEFAULT NULL COMMENT '总重复率',
  `report_url` varchar(255) DEFAULT NULL COMMENT '查重报告URL',
  `report_path` varchar(255) DEFAULT NULL COMMENT '本地报告文件路径',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '查重费用',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `check_api_id` (`check_api_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查重任务表';

-- 查重报告详情表
DROP TABLE IF EXISTS `bxw_check_detail`;
CREATE TABLE `bxw_check_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(10) unsigned NOT NULL COMMENT '查重任务ID',
  `paragraph_index` int(10) NOT NULL COMMENT '段落索引',
  `original_text` text NOT NULL COMMENT '原始段落文本',
  `similarity_rate` decimal(5,2) NOT NULL COMMENT '段落重复率',
  `matched_sources` text COMMENT '匹配来源，JSON格式',
  `suggestions` text COMMENT '修改建议',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `similarity_rate` (`similarity_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查重报告详情表';

-- =====================================================
-- 6. 文档导出相关表
-- =====================================================

-- 文档模板表
DROP TABLE IF EXISTS `bxw_document_template`;
CREATE TABLE `bxw_document_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(50) NOT NULL COMMENT '模板类型：cover=封面，content=正文，reference=参考文献',
  `paper_type_id` int(10) unsigned DEFAULT NULL COMMENT '关联论文类型ID',
  `template_content` longtext NOT NULL COMMENT '模板内容',
  `style_config` text COMMENT '样式配置，JSON格式',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `usage_count` int(10) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `paper_type_id` (`paper_type_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档模板表';

-- 文档导出记录表
DROP TABLE IF EXISTS `bxw_export_record`;
CREATE TABLE `bxw_export_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型：paper=论文，rewrite=降重结果',
  `source_id` int(10) unsigned NOT NULL COMMENT '来源ID',
  `export_format` varchar(20) NOT NULL COMMENT '导出格式：docx，pdf',
  `template_id` int(10) unsigned DEFAULT NULL COMMENT '使用的模板ID',
  `file_name` varchar(255) NOT NULL COMMENT '生成的文件名',
  `file_path` varchar(255) NOT NULL COMMENT '文件存储路径',
  `file_size` int(10) NOT NULL DEFAULT '0' COMMENT '文件大小（字节）',
  `download_count` int(10) NOT NULL DEFAULT '0' COMMENT '下载次数',
  `n8n_workflow_id` varchar(100) DEFAULT NULL COMMENT 'n8n工作流ID',
  `n8n_execution_id` varchar(100) DEFAULT NULL COMMENT 'n8n执行ID',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待生成，generating=生成中，completed=已完成，failed=失败',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `source_type_id` (`source_type`,`source_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档导出记录表';

-- =====================================================
-- 7. 订单支付相关表
-- =====================================================

-- 套餐配置表
DROP TABLE IF EXISTS `bxw_package`;
CREATE TABLE `bxw_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '套餐名称',
  `type` varchar(50) NOT NULL COMMENT '套餐类型：credits=积分包，vip=VIP套餐，combo=组合套餐',
  `description` text COMMENT '套餐描述',
  `credits` int(10) NOT NULL DEFAULT '0' COMMENT '包含积分数量',
  `vip_days` int(10) NOT NULL DEFAULT '0' COMMENT 'VIP天数',
  `writing_quota` int(10) NOT NULL DEFAULT '0' COMMENT '写作次数配额，0=无限制',
  `rewrite_quota` int(10) NOT NULL DEFAULT '0' COMMENT '降重次数配额，0=无限制',
  `check_quota` int(10) NOT NULL DEFAULT '0' COMMENT '查重次数配额，0=无限制',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售价',
  `discount_rate` decimal(5,2) NOT NULL DEFAULT '100.00' COMMENT '折扣率',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热门推荐',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=下架，1=上架',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套餐配置表';

-- 订单表
DROP TABLE IF EXISTS `bxw_order`;
CREATE TABLE `bxw_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `package_id` int(10) unsigned NOT NULL COMMENT '套餐ID',
  `package_name` varchar(100) NOT NULL COMMENT '套餐名称（冗余存储）',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `final_price` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式：wechat=微信，alipay=支付宝',
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending=待支付，paid=已支付，cancelled=已取消，refunded=已退款',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易号',
  `paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `expired_time` datetime NOT NULL COMMENT '订单过期时间',
  `coupon_id` int(10) unsigned DEFAULT NULL COMMENT '使用的优惠券ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `package_id` (`package_id`),
  KEY `payment_status` (`payment_status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 优惠券表
DROP TABLE IF EXISTS `bxw_coupon`;
CREATE TABLE `bxw_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) NOT NULL COMMENT '优惠券代码',
  `type` varchar(20) NOT NULL COMMENT '类型：fixed=固定金额，percent=百分比',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低消费金额',
  `max_discount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最大优惠金额（百分比类型）',
  `total_quantity` int(10) NOT NULL DEFAULT '0' COMMENT '总发放数量，0=无限制',
  `used_quantity` int(10) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `per_user_limit` int(10) NOT NULL DEFAULT '1' COMMENT '每用户限用次数',
  `start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `applicable_packages` varchar(500) DEFAULT NULL COMMENT '适用套餐ID，逗号分隔，空=全部适用',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `status` (`status`),
  KEY `start_end_time` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 用户优惠券表
DROP TABLE IF EXISTS `bxw_user_coupon`;
CREATE TABLE `bxw_user_coupon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `coupon_id` int(10) unsigned NOT NULL COMMENT '优惠券ID',
  `order_id` int(10) unsigned DEFAULT NULL COMMENT '使用的订单ID',
  `status` varchar(20) NOT NULL DEFAULT 'unused' COMMENT '状态：unused=未使用，used=已使用，expired=已过期',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `createtime` datetime NOT NULL COMMENT '获得时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `coupon_id` (`coupon_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 积分变动记录表
DROP TABLE IF EXISTS `bxw_credits_log`;
CREATE TABLE `bxw_credits_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '变动类型：recharge=充值，consume=消费，refund=退款，reward=奖励',
  `amount` int(10) NOT NULL COMMENT '变动数量（正数=增加，负数=减少）',
  `balance_before` int(10) NOT NULL COMMENT '变动前余额',
  `balance_after` int(10) NOT NULL COMMENT '变动后余额',
  `related_id` int(10) unsigned DEFAULT NULL COMMENT '关联ID（订单ID、任务ID等）',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型：order=订单，task=任务',
  `description` varchar(255) NOT NULL COMMENT '变动描述',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分变动记录表';

-- =====================================================
-- 8. 系统配置相关表
-- =====================================================

-- 系统配置表
DROP TABLE IF EXISTS `bxw_config`;
CREATE TABLE `bxw_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group` varchar(50) NOT NULL COMMENT '配置分组',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `title` varchar(100) NOT NULL COMMENT '配置标题',
  `value` text COMMENT '配置值',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string=字符串，number=数字，boolean=布尔，json=JSON',
  `options` text COMMENT '选项配置（select类型使用）',
  `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_name` (`group`,`name`),
  KEY `group` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 内容风控规则表
DROP TABLE IF EXISTS `bxw_content_filter`;
CREATE TABLE `bxw_content_filter` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '规则名称',
  `type` varchar(50) NOT NULL COMMENT '规则类型：keyword=关键词，regex=正则表达式，ai=AI检测',
  `pattern` text NOT NULL COMMENT '匹配模式',
  `action` varchar(20) NOT NULL DEFAULT 'block' COMMENT '处理动作：block=阻止，warn=警告，replace=替换',
  `replacement` varchar(500) DEFAULT NULL COMMENT '替换内容（replace动作使用）',
  `severity` varchar(20) NOT NULL DEFAULT 'medium' COMMENT '严重程度：low=低，medium=中，high=高',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容风控规则表';

-- =====================================================
-- 9. 通知相关表
-- =====================================================

-- 消息模板表
DROP TABLE IF EXISTS `bxw_message_template`;
CREATE TABLE `bxw_message_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '模板代码',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(20) NOT NULL COMMENT '消息类型：email=邮件，sms=短信，system=系统通知',
  `subject` varchar(255) DEFAULT NULL COMMENT '消息主题（邮件使用）',
  `content` text NOT NULL COMMENT '消息内容，支持变量替换',
  `variables` text COMMENT '变量说明，JSON格式',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息模板表';

-- 用户通知表
DROP TABLE IF EXISTS `bxw_user_notification`;
CREATE TABLE `bxw_user_notification` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '通知类型：task_completed=任务完成，payment_success=支付成功等',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `related_id` int(10) unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型：paper=论文，rewrite=降重，check=查重，order=订单',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：0=未读，1=已读',
  `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `is_read` (`is_read`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知表';

-- 邮件发送记录表
DROP TABLE IF EXISTS `bxw_email_log`;
CREATE TABLE `bxw_email_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '用户ID',
  `template_id` int(10) unsigned DEFAULT NULL COMMENT '模板ID',
  `to_email` varchar(255) NOT NULL COMMENT '收件人邮箱',
  `subject` varchar(255) NOT NULL COMMENT '邮件主题',
  `content` text NOT NULL COMMENT '邮件内容',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '发送状态：pending=待发送，sent=已发送，failed=发送失败',
  `error_message` text COMMENT '错误信息',
  `sent_time` datetime DEFAULT NULL COMMENT '发送时间',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送记录表';

-- =====================================================
-- 10. n8n集成相关表
-- =====================================================

-- n8n工作流配置表
DROP TABLE IF EXISTS `bxw_n8n_workflow`;
CREATE TABLE `bxw_n8n_workflow` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '工作流名称',
  `type` varchar(50) NOT NULL COMMENT '工作流类型：outline=大纲生成，writing=写作，rewrite=降重，check=查重，export=导出',
  `n8n_workflow_id` varchar(100) NOT NULL COMMENT 'n8n中的工作流ID',
  `webhook_url` varchar(255) DEFAULT NULL COMMENT 'Webhook触发URL',
  `description` text COMMENT '工作流描述',
  `config` text COMMENT '配置参数，JSON格式',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `n8n_workflow_id` (`n8n_workflow_id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='n8n工作流配置表';

-- n8n执行记录表
DROP TABLE IF EXISTS `bxw_n8n_execution`;
CREATE TABLE `bxw_n8n_execution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `workflow_id` int(10) unsigned NOT NULL COMMENT '工作流ID',
  `n8n_execution_id` varchar(100) NOT NULL COMMENT 'n8n执行ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `task_id` int(10) unsigned NOT NULL COMMENT '任务ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `input_data` text COMMENT '输入数据，JSON格式',
  `output_data` text COMMENT '输出数据，JSON格式',
  `status` varchar(20) NOT NULL DEFAULT 'running' COMMENT '执行状态：running=运行中，success=成功，failed=失败，cancelled=已取消',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(10) DEFAULT NULL COMMENT '执行时长（秒）',
  `error_message` text COMMENT '错误信息',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `n8n_execution_id` (`n8n_execution_id`),
  KEY `workflow_id` (`workflow_id`),
  KEY `task_type_id` (`task_type`,`task_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='n8n执行记录表';

-- =====================================================
-- 11. 日志相关表
-- =====================================================

-- 操作日志表
DROP TABLE IF EXISTS `bxw_operation_log`;
CREATE TABLE `bxw_operation_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '用户ID',
  `admin_id` int(10) unsigned DEFAULT NULL COMMENT '管理员ID',
  `module` varchar(50) NOT NULL COMMENT '模块名称',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `description` varchar(500) NOT NULL COMMENT '操作描述',
  `request_data` text COMMENT '请求数据，JSON格式',
  `response_data` text COMMENT '响应数据，JSON格式',
  `ip_address` varchar(50) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execution_time` int(10) NOT NULL DEFAULT '0' COMMENT '执行时间（毫秒）',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `admin_id` (`admin_id`),
  KEY `module` (`module`),
  KEY `action` (`action`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 错误日志表
DROP TABLE IF EXISTS `bxw_error_log`;
CREATE TABLE `bxw_error_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL COMMENT '错误级别：error=错误，warning=警告，info=信息',
  `category` varchar(50) NOT NULL COMMENT '错误分类：api=API调用，workflow=工作流，payment=支付，system=系统',
  `message` varchar(1000) NOT NULL COMMENT '错误消息',
  `context` text COMMENT '错误上下文，JSON格式',
  `stack_trace` text COMMENT '堆栈跟踪',
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '相关用户ID',
  `request_id` varchar(100) DEFAULT NULL COMMENT '请求ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `level` (`level`),
  KEY `category` (`category`),
  KEY `user_id` (`user_id`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错误日志表';

-- =====================================================
-- 12. 统计分析相关表
-- =====================================================

-- 用户行为统计表
DROP TABLE IF EXISTS `bxw_user_stats`;
CREATE TABLE `bxw_user_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '统计日期',
  `login_count` int(10) NOT NULL DEFAULT '0' COMMENT '登录次数',
  `writing_count` int(10) NOT NULL DEFAULT '0' COMMENT '写作任务数',
  `rewrite_count` int(10) NOT NULL DEFAULT '0' COMMENT '降重任务数',
  `check_count` int(10) NOT NULL DEFAULT '0' COMMENT '查重任务数',
  `export_count` int(10) NOT NULL DEFAULT '0' COMMENT '导出次数',
  `credits_consumed` int(10) NOT NULL DEFAULT '0' COMMENT '消费积分',
  `online_duration` int(10) NOT NULL DEFAULT '0' COMMENT '在线时长（分钟）',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`,`date`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为统计表';

-- 系统统计表
DROP TABLE IF EXISTS `bxw_system_stats`;
CREATE TABLE `bxw_system_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `new_users` int(10) NOT NULL DEFAULT '0' COMMENT '新增用户数',
  `active_users` int(10) NOT NULL DEFAULT '0' COMMENT '活跃用户数',
  `total_orders` int(10) NOT NULL DEFAULT '0' COMMENT '订单总数',
  `total_revenue` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `writing_tasks` int(10) NOT NULL DEFAULT '0' COMMENT '写作任务数',
  `rewrite_tasks` int(10) NOT NULL DEFAULT '0' COMMENT '降重任务数',
  `check_tasks` int(10) NOT NULL DEFAULT '0' COMMENT '查重任务数',
  `ai_api_calls` int(10) NOT NULL DEFAULT '0' COMMENT 'AI API调用次数',
  `ai_api_cost` decimal(10,6) NOT NULL DEFAULT '0.000000' COMMENT 'AI API成本',
  `error_count` int(10) NOT NULL DEFAULT '0' COMMENT '错误次数',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统统计表';

-- =====================================================
-- 13. 初始化数据
-- =====================================================

-- 插入默认论文类型
INSERT INTO `bxw_paper_type` (`name`, `description`, `word_count_min`, `word_count_max`, `sort`, `createtime`, `updatetime`) VALUES
('毕业论文', '本科、硕士、博士毕业论文', 8000, 50000, 1, NOW(), NOW()),
('学术论文', '期刊论文、会议论文', 3000, 15000, 2, NOW(), NOW()),
('综述论文', '文献综述、研究综述', 5000, 20000, 3, NOW(), NOW()),
('开题报告', '研究开题报告', 2000, 8000, 4, NOW(), NOW()),
('课程论文', '课程作业论文', 1000, 5000, 5, NOW(), NOW());

-- 插入默认AI模型配置
INSERT INTO `bxw_ai_model` (`name`, `provider`, `model_code`, `api_endpoint`, `max_tokens`, `temperature`, `cost_per_1k_tokens`, `priority`, `createtime`, `updatetime`) VALUES
('GPT-4', 'openai', 'gpt-4', 'https://api.openai.com/v1/chat/completions', 8000, 0.70, 0.030000, 100, NOW(), NOW()),
('GPT-3.5 Turbo', 'openai', 'gpt-3.5-turbo', 'https://api.openai.com/v1/chat/completions', 4000, 0.70, 0.002000, 90, NOW(), NOW()),
('文心一言', 'baidu', 'ernie-bot', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions', 4000, 0.70, 0.001200, 80, NOW(), NOW()),
('通义千问', 'aliyun', 'qwen-max', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 6000, 0.70, 0.002000, 85, NOW(), NOW());

-- 插入默认套餐配置
INSERT INTO `bxw_package` (`name`, `type`, `description`, `credits`, `writing_quota`, `rewrite_quota`, `check_quota`, `original_price`, `sale_price`, `sort`, `createtime`, `updatetime`) VALUES
('体验套餐', 'credits', '新用户体验套餐，包含基础功能', 100, 2, 5, 1, 19.90, 9.90, 1, NOW(), NOW()),
('标准套餐', 'credits', '标准用户套餐，适合日常使用', 500, 10, 20, 5, 99.00, 79.00, 2, NOW(), NOW()),
('专业套餐', 'credits', '专业用户套餐，功能全面', 1000, 20, 50, 10, 199.00, 149.00, 3, NOW(), NOW()),
('VIP月卡', 'vip', 'VIP会员月卡，享受更多特权', 0, 0, 0, 0, 99.00, 89.00, 4, NOW(), NOW()),
('VIP年卡', 'vip', 'VIP会员年卡，超值优惠', 0, 0, 0, 0, 999.00, 699.00, 5, NOW(), NOW());

-- 插入默认系统配置
INSERT INTO `bxw_config` (`group`, `name`, `title`, `value`, `type`, `description`, `sort`, `createtime`, `updatetime`) VALUES
('system', 'site_name', '网站名称', 'AI论文写作平台', 'string', '网站名称配置', 1, NOW(), NOW()),
('system', 'site_description', '网站描述', '基于AI的智能论文写作平台', 'string', '网站描述配置', 2, NOW(), NOW()),
('system', 'default_credits', '新用户默认积分', '50', 'number', '新用户注册默认获得的积分', 3, NOW(), NOW()),
('ai', 'default_model', '默认AI模型', 'gpt-3.5-turbo', 'string', '系统默认使用的AI模型', 1, NOW(), NOW()),
('ai', 'max_concurrent_tasks', '最大并发任务数', '10', 'number', '系统最大并发处理任务数', 2, NOW(), NOW()),
('payment', 'wechat_enabled', '微信支付启用', 'true', 'boolean', '是否启用微信支付', 1, NOW(), NOW()),
('payment', 'alipay_enabled', '支付宝支付启用', 'true', 'boolean', '是否启用支付宝支付', 2, NOW(), NOW()),
('notification', 'email_enabled', '邮件通知启用', 'true', 'boolean', '是否启用邮件通知', 1, NOW(), NOW()),
('notification', 'sms_enabled', '短信通知启用', 'false', 'boolean', '是否启用短信通知', 2, NOW(), NOW());

-- 插入默认消息模板
INSERT INTO `bxw_message_template` (`code`, `name`, `type`, `subject`, `content`, `variables`, `createtime`, `updatetime`) VALUES
('task_completed', '任务完成通知', 'system', NULL, '您的{task_type}任务"{task_title}"已完成，请及时查看结果。', '{"task_type":"任务类型","task_title":"任务标题"}', NOW(), NOW()),
('payment_success', '支付成功通知', 'system', NULL, '您的订单{order_no}支付成功，金额：￥{amount}，感谢您的支持！', '{"order_no":"订单号","amount":"支付金额"}', NOW(), NOW()),
('welcome_email', '欢迎邮件', 'email', '欢迎使用AI论文写作平台', '亲爱的{username}，欢迎使用我们的AI论文写作平台！您已获得{credits}积分，可以开始体验我们的服务。', '{"username":"用户名","credits":"积分数量"}', NOW(), NOW());

-- 插入默认n8n工作流配置
INSERT INTO `bxw_n8n_workflow` (`name`, `type`, `n8n_workflow_id`, `description`, `createtime`, `updatetime`) VALUES
('论文大纲生成', 'outline', 'outline-generation-workflow', '根据用户输入生成论文大纲的工作流', NOW(), NOW()),
('论文写作', 'writing', 'paper-writing-workflow', '根据大纲生成论文内容的工作流', NOW(), NOW()),
('语义降重', 'rewrite', 'text-rewrite-workflow', '对文本进行语义降重的工作流', NOW(), NOW()),
('论文查重', 'check', 'plagiarism-check-workflow', '提交论文进行查重检测的工作流', NOW(), NOW()),
('文档导出', 'export', 'document-export-workflow', '将内容导出为DOCX或PDF的工作流', NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 外键约束和关联关系
-- =====================================================

-- 用户配额表外键
ALTER TABLE `bxw_user_quota`
ADD CONSTRAINT `fk_user_quota_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 论文类型表外键
ALTER TABLE `bxw_paper_type`
ADD CONSTRAINT `fk_paper_type_outline_template` FOREIGN KEY (`outline_template_id`) REFERENCES `bxw_outline_template` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_paper_type_prompt_template` FOREIGN KEY (`prompt_template_id`) REFERENCES `bxw_prompt_template` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 大纲模板表外键
ALTER TABLE `bxw_outline_template`
ADD CONSTRAINT `fk_outline_template_paper_type` FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 提示词模板表外键
ALTER TABLE `bxw_prompt_template`
ADD CONSTRAINT `fk_prompt_template_paper_type` FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 论文项目表外键
ALTER TABLE `bxw_paper_project`
ADD CONSTRAINT `fk_paper_project_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_paper_project_paper_type` FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 论文章节表外键
ALTER TABLE `bxw_paper_section`
ADD CONSTRAINT `fk_paper_section_project` FOREIGN KEY (`project_id`) REFERENCES `bxw_paper_project` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AI使用统计表外键
ALTER TABLE `bxw_ai_usage_log`
ADD CONSTRAINT `fk_ai_usage_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_ai_usage_model` FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 降重任务表外键
ALTER TABLE `bxw_rewrite_task`
ADD CONSTRAINT `fk_rewrite_task_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_rewrite_task_ai_model` FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 降重结果表外键
ALTER TABLE `bxw_rewrite_result`
ADD CONSTRAINT `fk_rewrite_result_task` FOREIGN KEY (`task_id`) REFERENCES `bxw_rewrite_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_rewrite_result_ai_model` FOREIGN KEY (`ai_model_id`) REFERENCES `bxw_ai_model` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 查重任务表外键
ALTER TABLE `bxw_check_task`
ADD CONSTRAINT `fk_check_task_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_check_task_api` FOREIGN KEY (`check_api_id`) REFERENCES `bxw_check_api` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 查重报告详情表外键
ALTER TABLE `bxw_check_detail`
ADD CONSTRAINT `fk_check_detail_task` FOREIGN KEY (`task_id`) REFERENCES `bxw_check_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 文档模板表外键
ALTER TABLE `bxw_document_template`
ADD CONSTRAINT `fk_document_template_paper_type` FOREIGN KEY (`paper_type_id`) REFERENCES `bxw_paper_type` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 文档导出记录表外键
ALTER TABLE `bxw_export_record`
ADD CONSTRAINT `fk_export_record_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_export_record_template` FOREIGN KEY (`template_id`) REFERENCES `bxw_document_template` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 订单表外键
ALTER TABLE `bxw_order`
ADD CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_order_package` FOREIGN KEY (`package_id`) REFERENCES `bxw_package` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
ADD CONSTRAINT `fk_order_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `bxw_coupon` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 用户优惠券表外键
ALTER TABLE `bxw_user_coupon`
ADD CONSTRAINT `fk_user_coupon_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_user_coupon_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `bxw_coupon` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_user_coupon_order` FOREIGN KEY (`order_id`) REFERENCES `bxw_order` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 积分变动记录表外键
ALTER TABLE `bxw_credits_log`
ADD CONSTRAINT `fk_credits_log_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 用户通知表外键
ALTER TABLE `bxw_user_notification`
ADD CONSTRAINT `fk_user_notification_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 邮件发送记录表外键
ALTER TABLE `bxw_email_log`
ADD CONSTRAINT `fk_email_log_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_email_log_template` FOREIGN KEY (`template_id`) REFERENCES `bxw_message_template` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- n8n执行记录表外键
ALTER TABLE `bxw_n8n_execution`
ADD CONSTRAINT `fk_n8n_execution_workflow` FOREIGN KEY (`workflow_id`) REFERENCES `bxw_n8n_workflow` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT `fk_n8n_execution_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 操作日志表外键
ALTER TABLE `bxw_operation_log`
ADD CONSTRAINT `fk_operation_log_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 用户行为统计表外键
ALTER TABLE `bxw_user_stats`
ADD CONSTRAINT `fk_user_stats_user` FOREIGN KEY (`user_id`) REFERENCES `bxw_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- =====================================================
-- 数据库设计说明
-- =====================================================
/*
1. 表前缀统一使用 bxw_，符合FastAdmin规范
2. 所有表都包含createtime和updatetime字段，便于数据追踪
3. 主键统一使用自增int(10) unsigned
4. 状态字段统一使用tinyint(1)，0=禁用/否，1=启用/是
5. 金额字段使用decimal类型，确保精度
6. 文本内容使用text或longtext类型
7. JSON数据使用text类型存储
8. 时间字段使用datetime类型
9. 完整的外键约束和关联关系，保证数据完整性
10. 预留了n8n集成相关字段，支持工作流管理

外键约束策略说明：
- CASCADE：当父表记录删除时，子表相关记录也会被删除（用于强关联数据）
- RESTRICT：当子表存在相关记录时，不允许删除父表记录（用于重要引用）
- SET NULL：当父表记录删除时，子表外键字段设为NULL（用于可选引用）

主要关联关系：
1. 用户相关：
   - bxw_user → bxw_user_quota (1:1)
   - bxw_user → bxw_paper_project (1:N)
   - bxw_user → bxw_rewrite_task (1:N)
   - bxw_user → bxw_check_task (1:N)
   - bxw_user → bxw_order (1:N)

2. 论文写作相关：
   - bxw_paper_type → bxw_paper_project (1:N)
   - bxw_paper_project → bxw_paper_section (1:N)
   - bxw_paper_type → bxw_outline_template (1:N)
   - bxw_paper_type → bxw_prompt_template (1:N)

3. AI模型相关：
   - bxw_ai_model → bxw_ai_usage_log (1:N)
   - bxw_ai_model → bxw_rewrite_task (1:N)
   - bxw_ai_model → bxw_rewrite_result (1:N)

4. 降重相关：
   - bxw_rewrite_task → bxw_rewrite_result (1:N)

5. 查重相关：
   - bxw_check_api → bxw_check_task (1:N)
   - bxw_check_task → bxw_check_detail (1:N)

6. 文档导出相关：
   - bxw_document_template → bxw_export_record (1:N)
   - bxw_paper_type → bxw_document_template (1:N)

7. 订单支付相关：
   - bxw_package → bxw_order (1:N)
   - bxw_coupon → bxw_user_coupon (1:N)
   - bxw_order → bxw_user_coupon (1:1)

8. 通知相关：
   - bxw_message_template → bxw_email_log (1:N)

9. n8n集成相关：
   - bxw_n8n_workflow → bxw_n8n_execution (1:N)

主要功能模块：
- 用户管理：用户信息、配额、VIP等
- 论文写作：项目、章节、模板等
- AI模型：配置、使用统计等
- 降重功能：任务、结果等
- 查重功能：接口配置、任务、报告等
- 文档导出：模板、记录等
- 订单支付：套餐、订单、优惠券、积分等
- 系统配置：参数配置、风控规则等
- 通知系统：模板、用户通知、邮件日志等
- n8n集成：工作流配置、执行记录等
- 日志统计：操作日志、错误日志、统计数据等

数据完整性保证：
1. 用户删除时，相关的任务、订单、统计等数据会级联删除
2. 重要配置（如论文类型、AI模型、查重接口）被引用时不允许删除
3. 可选引用（如模板、优惠券）删除时，相关字段设为NULL
4. 所有金额和积分相关操作都有完整的日志记录
5. 外键约束确保数据引用的有效性和一致性
*/
