<?php

// +----------------------------------------------------------------------
// | Prompt Template Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\PromptTemplate as PromptTemplateModel;
use app\admin\model\PaperType;

/**
 * 提示词模板管理
 * @class PromptTemplate
 * @package app\admin\controller
 */
class PromptTemplate extends Controller
{
    /**
     * 提示词模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        PromptTemplateModel::mQuery()->layTable(function () {
            $this->title = '提示词模板管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            $query->like('name,type,ai_model')->equal('paper_type_id,status,is_default');
            $query->dateBetween('create_time');
            $query->order('is_default desc,usage_count desc,id desc');
        });
    }

    /**
     * 添加提示词模板
     * @auth true
     */public function add()
    {
        PromptTemplateModel::mForm('prompt_template/form');
    }

    /**
     * 编辑提示词模板
     * @auth true
     */
    public function edit()
    {
        PromptTemplateModel::mForm('prompt_template/form');
    }

    /**
     * 查看提示词模板详情
     * @auth true
     */
    public function view()
    {
        PromptTemplateModel::mForm('prompt_template/view');
    }

    /**
     * 表单数据处理
     * @param array $data
     * @throws \think\db\exception\DbException
     */
    protected function _form_filter(array &$data)
    {
        if ($this->request->isGet()) {
            // 获取论文类型列表
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
            
            // 模板类型选项
            $this->typeOptions = [
                'outline' => '大纲生成',
                'writing' => '内容写作',
                'rewrite' => '内容改写',
                'summary' => '内容总结',
                'expansion' => '内容扩展',
                'polish' => '内容润色'
            ];
            
            // AI模型选项
            $this->aiModelOptions = [
                'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
                'gpt-4' => 'GPT-4',
                'claude-3' => 'Claude-3',
                'qwen' => '通义千问',
                'chatglm' => 'ChatGLM'
            ];
            
            // 状态选项
            $this->statusOptions = [
                0 => '禁用',
                1 => '启用'
            ];
            
            // 默认模板选项
            $this->defaultOptions = [
                0 => '否',
                1 => '是'
            ];
        } else {
            // POST请求时的数据处理
            if (empty($data['id'])) {
                $data['create_time'] = date('Y-m-d H:i:s');
                $data['usage_count'] = 0;
            }
            $data['update_time'] = date('Y-m-d H:i:s');
            
            // 处理变量定义JSON
            if (!empty($data['variables']) && is_array($data['variables'])) {
                $data['variables'] = json_encode($data['variables'], JSON_UNESCAPED_UNICODE);
            }
            
            // 如果设置为默认模板，取消其他同类型的默认状态
            if (!empty($data['is_default']) && $data['is_default'] == 1) {
                PromptTemplateModel::mk()
                    ->where(['type' => $data['type'], 'paper_type_id' => $data['paper_type_id']])
                    ->update(['is_default' => 0]);
            }
        }
    }

    /**
     * 修改模板状态
     * @auth true
     */
    public function state()
    {
        PromptTemplateModel::mSave($this->_vali([
            'status.require' => '状态值不能为空！',
        ]));
    }

    /**
     * 删除提示词模板
     * @auth true
     */
    public function remove()
    {
        PromptTemplateModel::mDelete();
    }

    /**
     * 设置默认模板
     * @auth true
     */
    public function setDefault()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = PromptTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        // 取消同类型的其他默认模板
        PromptTemplateModel::mk()
            ->where(['type' => $template['type'], 'paper_type_id' => $template['paper_type_id']])
            ->update(['is_default' => 0]);
        
        // 设置当前模板为默认
        $template->save(['is_default' => 1, 'update_time' => date('Y-m-d H:i:s')]);
        
        $this->success('默认模板设置成功！');
    }

    /**
     * 复制模板
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = PromptTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        $copyData = $template->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . ' - 副本';
        $copyData['is_default'] = 0;
        $copyData['usage_count'] = 0;
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');
        
        PromptTemplateModel::mk()->save($copyData);
        $this->success('模板复制成功！');
    }

    /**
     * 预览模板
     * @auth true
     */
    public function preview()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }
        
        $template = PromptTemplateModel::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }
        
        $this->template = $template;
        $this->variables = !empty($template['variables']) ? json_decode($template['variables'], true) : [];
        
        $this->fetch('prompt_template/preview');
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'PromptTemplate管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
