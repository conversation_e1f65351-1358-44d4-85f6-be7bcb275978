<form action="{:sysuri()}" method="post" data-auto="true" class="layui-form layui-card">
    <div class="layui-card-body padding-top-20">

        <div class="color-text layui-code text-center layui-bg-gray" style="border-left-width:1px;margin:0 0 15px 40px">
            <p class="margin-bottom-5 font-w7">文件将上传到 <a target="_blank" href="https://alist.nn.ci/zh/">Alist</a> 自建存储，需要自行搭建 <a target="_blank" href="https://alist.nn.ci/zh/">Alist</a> 存储服务器。</p>
            <p>Alist 是一个支持多种存储的文件列表程序，可将各种云盘及本地磁盘资源进行整合。</p>
            <p>建议不要开放匿名用户访问，尽量使用独立账号管理，需要关闭 “签名所有” 让文件可以直接访问。</p>
        </div>

        {include file='config/storage-0'}

        <div class="layui-form-item">
            <label class="layui-form-label label-required">
                <b class="color-green">访问协议</b><br><span class="nowrap color-desc">Protocol</span>
            </label>
            <div class="layui-input-block">
                {if !sysconf('storage.alist_http_protocol')}{php}sysconf('storage.alist_http_protocol','http');{/php}{/if}
                <div class="layui-input help-checks">
                    {foreach ['http'=>'HTTP','https'=>'HTTPS','auto'=>"AUTO"] as $protocol=>$remark}
                    <label class="think-radio">
                        {if sysconf('storage.alist_http_protocol') eq $protocol}
                        <input checked type="radio" name="storage.alist_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {else}
                        <input type="radio" name="storage.alist_http_protocol" value="{$protocol}" lay-ignore> {$remark}
                        {/if}
                    </label>
                    {/foreach}
                </div>
                <p class="help-block">请选择 Alist 存储访问协议，其中 HTTPS 需要配置证书才能使用（ AUTO 为相对协议 ）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alist_http_domain">
                <b class="color-green">访问域名</b><br><span class="nowrap color-desc">Domain</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alist_http_domain" type="text" name="storage.alist_http_domain" value="{:sysconf('storage.alist_http_domain')}" required vali-name="访问域名" placeholder="请输入 Alist 存储的访问域名" class="layui-input">
                <p class="help-block">请填写 Alist 存储访问域名，不需要填写访问协议，如：storage.thinkadmin.top</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alist_savepath">
                <b class="color-green">存储目录</b><br><span class="nowrap color-desc">Directory</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alist_savepath" type="text" name="storage.alist_savepath" value="{:sysconf('storage.alist_savepath')}" required vali-name="存储目录" placeholder="请输入 Alist 存储目录" class="layui-input">
                <p class="help-block">请填写 Alist 用户基本目录的相对存储位置，填写 / 表示用户基本目录（ 需要拥有读写权限 ）</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alist_username">
                <b class="color-green">用户账号</b><br><span class="nowrap color-desc">Username</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alist_username" name="storage.alist_username" value="{:sysconf('storage.alist_username')}" maxlength="100" required vali-name="用户账号" placeholder="请输入 Alist 存储的用户账号" class="layui-input">
                <p class="help-block">请填写 Alist 用户账号，注意此账号需要拥有上面存储目录的访问权限。</p>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="storage.alist_password">
                <b class="color-green">用户密码</b><br><span class="nowrap color-desc">Password</span>
            </label>
            <div class="layui-input-block">
                <input id="storage.alist_password" name="storage.alist_password" value="{:sysconf('storage.alist_password')}" maxlength="100" required vali-name="用户密码" placeholder="请输入 Alist 存储的用户密码" class="layui-input">
                <p class="help-block">请填写 Alist 用户登录密码，用于生成文件上传的接口认证令牌，如果填写错误将无法上传文件。</p>
            </div>
        </div>

        <div class="hr-line-dashed margin-left-40"></div>
        <input type="hidden" name="storage.type" value="alist">

        <div class="layui-form-item text-center padding-left-40">
            <button class="layui-btn" type="submit">保存配置</button>
            <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消修改吗？" data-close>取消修改</button>
        </div>
    </div>
</form>