<?php

/**
 * 直接测试控制器调用
 */

echo "=== 直接测试控制器调用 ===\n\n";

// 设置基本环境
define('ROOT_PATH', __DIR__ . '/');

// 加载自动加载器
require __DIR__ . '/vendor/autoload.php';

// 设置应用路径
define('APP_PATH', __DIR__ . '/app/');

try {
    // 初始化应用
    $app = new \think\App();
    $app->initialize();
    
    echo "✅ ThinkPHP应用初始化成功\n";
    
    // 测试各个控制器
    $controllers = [
        'ContentTemplate' => 'app\\admin\\controller\\ContentTemplate',
        'PromptTemplate' => 'app\\admin\\controller\\PromptTemplate',
        'OutlineTemplate' => 'app\\admin\\controller\\OutlineTemplate',
        'DraftBox' => 'app\\admin\\controller\\DraftBox',
        'PaperType' => 'app\\admin\\controller\\PaperType'
    ];
    
    foreach ($controllers as $name => $class) {
        echo "\n🔍 测试 {$name} 控制器:\n";
        
        try {
            // 检查类是否存在
            if (class_exists($class)) {
                echo "  ✅ 类 {$class} 存在\n";
                
                // 尝试实例化
                $controller = new $class($app);
                echo "  ✅ 控制器实例化成功\n";
                
                // 检查index方法是否存在
                if (method_exists($controller, 'index')) {
                    echo "  ✅ index方法存在\n";
                    
                    // 模拟请求对象
                    $request = $app->request;
                    $request->withGet([]);
                    
                    try {
                        // 尝试调用index方法（但不执行，只检查语法）
                        $reflection = new ReflectionMethod($controller, 'index');
                        echo "  ✅ index方法可以反射\n";
                        
                    } catch (Exception $e) {
                        echo "  ❌ index方法反射失败: " . $e->getMessage() . "\n";
                    }
                    
                } else {
                    echo "  ❌ index方法不存在\n";
                }
                
            } else {
                echo "  ❌ 类 {$class} 不存在\n";
            }
            
        } catch (Exception $e) {
            echo "  ❌ 测试失败: " . $e->getMessage() . "\n";
        } catch (Error $e) {
            echo "  ❌ 致命错误: " . $e->getMessage() . "\n";
        }
    }
    
    // 测试模型
    echo "\n🗄️ 测试模型:\n";
    $models = [
        'DocumentTemplate' => 'app\\admin\\model\\DocumentTemplate',
        'PromptTemplate' => 'app\\admin\\model\\PromptTemplate',
        'OutlineTemplate' => 'app\\admin\\model\\OutlineTemplate',
        'PaperProject' => 'app\\admin\\model\\PaperProject',
        'PaperType' => 'app\\admin\\model\\PaperType'
    ];
    
    foreach ($models as $name => $class) {
        try {
            if (class_exists($class)) {
                echo "  ✅ 模型 {$name} 存在\n";
                
                // 尝试实例化
                $model = new $class();
                echo "  ✅ 模型 {$name} 实例化成功\n";
                
            } else {
                echo "  ❌ 模型 {$name} 不存在\n";
            }
        } catch (Exception $e) {
            echo "  ❌ 模型 {$name} 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 测试数据库查询
    echo "\n📊 测试数据库查询:\n";
    try {
        $documentTemplate = new \app\admin\model\DocumentTemplate();
        $count = $documentTemplate->where(['type' => 'content'])->count();
        echo "  ✅ DocumentTemplate查询成功，共 {$count} 条记录\n";
        
        $paperType = new \app\admin\model\PaperType();
        $count = $paperType->count();
        echo "  ✅ PaperType查询成功，共 {$count} 条记录\n";
        
    } catch (Exception $e) {
        echo "  ❌ 数据库查询失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 应用初始化失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ 致命错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
