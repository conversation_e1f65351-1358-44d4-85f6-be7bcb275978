<?php

// +----------------------------------------------------------------------
// | Paper Project Management for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\controller;

use think\admin\Controller;
use think\admin\helper\QueryHelper;
use app\admin\model\PaperType as PaperTypeModel;
use app\admin\model\OutlineTemplate;

/**
 * 论文类型管理
 * @class PaperType
 * @package app\admin\controller
 */
class PaperType extends Controller
{
    /**
     * 论文类型管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        try {
            $this->title = '论文类型管理';

            // 构建查询条件
            $map = [];
            $params = $this->request->param();

            // 处理搜索条件
            if (!empty($params['name'])) {
                $map[] = ['name', 'like', '%' . $params['name'] . '%'];
            }

            if (!empty($params['description'])) {
                $map[] = ['description', 'like', '%' . $params['description'] . '%'];
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $map['status'] = $params['status'];
            }

            // 执行查询
            $list = PaperTypeModel::where($map)
                ->order('sort asc,id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => $params
                ]);

            $this->assign([
                'title' => '论文类型管理',
                'list' => $list,
                'pagehtml' => $list->render(),
                'get' => $params
            ]);

            return $this->fetch();

        } catch (\Exception $e) {
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 添加论文类型
     * @auth true
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name' => 'require|max:100',
                'description' => 'max:500',
                'status' => 'in:0,1',
                'sort' => 'integer'
            ];

            $this->validate($data, $validate, [
                'name.require' => '论文类型名称不能为空',
                'name.max' => '论文类型名称不能超过100个字符',
                'description.max' => '描述不能超过500个字符',
                'status.in' => '状态值范围异常',
                'sort.integer' => '排序值必须为整数'
            ]);

            // 检查名称是否重复
            if (PaperTypeModel::where(['name' => $data['name']])->count() > 0) {
                $this->error('论文类型名称已经存在！');
            }

            $data['create_time'] = time();
            $data['update_time'] = time();

            if (PaperTypeModel::create($data)) {
                $this->success('添加成功！');
            } else {
                $this->error('添加失败！');
            }
        }

        // 获取大纲模板列表
        $this->assign('outline_templates', OutlineTemplate::where(['status' => 1])->column('name', 'id'));
        $this->assign('prompt_templates', []);

        return $this->fetch('paper_type/form');
    }

    /**
     * 编辑论文类型
     * @auth true
     */
    public function edit()
    {
        $id = $this->request->param('id', 0);
        $model = PaperTypeModel::findOrEmpty($id);

        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name' => 'require|max:100',
                'description' => 'max:500',
                'status' => 'in:0,1',
                'sort' => 'integer'
            ];

            $this->validate($data, $validate, [
                'name.require' => '论文类型名称不能为空',
                'name.max' => '论文类型名称不能超过100个字符',
                'description.max' => '描述不能超过500个字符',
                'status.in' => '状态值范围异常',
                'sort.integer' => '排序值必须为整数'
            ]);

            // 检查名称是否重复（排除自己）
            if (PaperTypeModel::where(['name' => $data['name']])->where('id', '<>', $id)->count() > 0) {
                $this->error('论文类型名称已经存在！');
            }

            $data['update_time'] = time();

            if ($model->save($data)) {
                $this->success('编辑成功！');
            } else {
                $this->error('编辑失败！');
            }
        }

        // 获取大纲模板列表
        $this->assign('outline_templates', OutlineTemplate::where(['status' => 1])->column('name', 'id'));
        $this->assign('prompt_templates', []);
        $this->assign('vo', $model);

        return $this->fetch('paper_type/form');
    }

    /**
     * 修改论文类型状态
     * @auth true
     */
    public function state()
    {
        $id = $this->request->post('id', 0);
        $status = $this->request->post('status', 0);

        if (empty($id)) {
            $this->error('参数错误！');
        }

        if (!in_array($status, [0, 1])) {
            $this->error('状态值范围异常！');
        }

        $model = PaperTypeModel::findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($model->save(['status' => $status, 'update_time' => time()])) {
            $this->success('状态修改成功！');
        } else {
            $this->error('状态修改失败！');
        }
    }

    /**
     * 删除论文类型
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);

        if (empty($id)) {
            $this->error('参数错误！');
        }

        $model = PaperTypeModel::findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($model->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }
}
