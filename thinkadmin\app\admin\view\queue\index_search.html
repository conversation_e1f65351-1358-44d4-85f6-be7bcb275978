<fieldset>
    <legend>{:lang('条件搜索')}</legend>
    <form class="layui-form layui-form-pane form-search" action="{:sysuri()}" onsubmit="return false" method="get" autocomplete="off">

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('编号名称')}</label>
            <label class="layui-input-inline">
                <input name="title" value="{$get.title|default=''}" placeholder="{:lang('请输入名称或编号')}" class="layui-input">
            </label>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('任务指令')}</label>
            <label class="layui-input-inline">
                <input name="command" value="{$get.command|default=''}" placeholder="{:lang('请输入任务指令')}" class="layui-input">
            </label>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('任务状态')}</label>
            <label class="layui-input-inline">
                <select name="status" class="layui-select">
                    <option value=''>-- {:lang('全部')} --</option>
                    {foreach ['1'=>lang('等待处理'),'2'=>lang('正在处理'),'3'=>lang('处理完成'),'4'=>lang('处理失败')] as $k=>$v}
                    {if isset($get.status) and $get.status eq $k}
                    <option selected value="{$k}">{$v}</option>
                    {else}
                    <option value="{$k}">{$v}</option>
                    {/if}{/foreach}
                </select>
            </label>
        </div>

        <div class="layui-form-item layui-inline">
            <label class="layui-form-label">{:lang('计划时间')}</label>
            <label class="layui-input-inline">
                <input data-date-range name="exec_time" value="{$get.exec_time|default=''}" placeholder="{:lang('请选择计划时间')}" class="layui-input">
            </label>
        </div>

        <div class="layui-form-item layui-inline">
            <button class="layui-btn layui-btn-primary"><i class="layui-icon">&#xe615;</i> {:lang('搜 索')}</button>
        </div>
    </form>
</fieldset>