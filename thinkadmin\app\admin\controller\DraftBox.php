<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\PaperProject;
use app\admin\model\PaperType;
use think\admin\Controller;

/**
 * 草稿箱管理
 * @class DraftBox
 * @package app\admin\controller
 */
class DraftBox extends Controller
{
    /**
     * 草稿箱管理
     * @auth true
     * @menu true
     */
    public function index()
    {
        try {
            $this->title = '草稿箱管理';

            // 获取论文类型选项
            $paperTypes = PaperType::where(['status' => 1])->column('name', 'id');

            // 构建查询条件 - 只显示草稿状态的项目
            $map = ['is_draft' => 1];
            $params = $this->request->param();

            // 处理搜索条件
            if (!empty($params['title'])) {
                $map[] = ['title', 'like', '%' . $params['title'] . '%'];
            }

            if (!empty($params['content'])) {
                $map[] = ['content', 'like', '%' . $params['content'] . '%'];
            }

            if (isset($params['paper_type_id']) && $params['paper_type_id'] !== '') {
                $map['paper_type_id'] = $params['paper_type_id'];
            }

            if (!empty($params['outline_version'])) {
                $map['outline_version'] = $params['outline_version'];
            }

            if (!empty($params['keywords'])) {
                $map[] = ['keywords', 'like', '%' . $params['keywords'] . '%'];
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $map['status'] = $params['status'];
            }

            // 时间范围查询
            if (!empty($params['create_time_start'])) {
                $map[] = ['create_time', '>=', strtotime($params['create_time_start'])];
            }
            if (!empty($params['create_time_end'])) {
                $map[] = ['create_time', '<=', strtotime($params['create_time_end']) + 86399];
            }

            if (!empty($params['update_time_start'])) {
                $map[] = ['update_time', '>=', strtotime($params['update_time_start'])];
            }
            if (!empty($params['update_time_end'])) {
                $map[] = ['update_time', '<=', strtotime($params['update_time_end']) + 86399];
            }

            // 执行查询
            $list = PaperProject::where($map)
                ->order('update_time desc,id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => $params
                ]);

            $this->assign([
                'title' => '草稿箱管理',
                'list' => $list,
                'pagehtml' => $list->render(),
                'paperTypes' => $paperTypes,
                'get' => $params
            ]);

            return $this->fetch();

        } catch (\Exception $e) {
            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 查看草稿详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->param('id', 0);
        $model = PaperProject::findOrEmpty($id);

        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        $this->assign('vo', $model);
        return $this->fetch('draft_box/view');
    }

    /**
     * 编辑草稿
     * @auth true
     */
    public function edit()
    {
        $id = $this->request->param('id', 0);
        $model = PaperProject::findOrEmpty($id);

        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'title' => 'require|max:200',
                'content' => 'require',
                'paper_type_id' => 'require|integer'
            ];

            $this->validate($data, $validate, [
                'title.require' => '标题不能为空',
                'title.max' => '标题不能超过200个字符',
                'content.require' => '内容不能为空',
                'paper_type_id.require' => '论文类型不能为空',
                'paper_type_id.integer' => '论文类型格式错误'
            ]);

            $data['update_time'] = time();

            if ($model->save($data)) {
                $this->success('编辑成功！');
            } else {
                $this->error('编辑失败！');
            }
        }

        $this->assign('vo', $model);
        return $this->fetch('draft_box/form');
    }

    /**
     * 发布草稿
     * @auth true
     */
    public function publish()
    {
        $id = $this->request->param('id', 0);
        if (empty($id)) {
            $this->error('草稿ID不能为空！');
        }

        $draft = PaperProject::findOrEmpty($id);
        if ($draft->isEmpty()) {
            $this->error('草稿不存在！');
        }

        if ($draft->save(['is_draft' => 0, 'status' => 1, 'update_time' => time()])) {
            $this->success('草稿发布成功！');
        } else {
            $this->error('草稿发布失败！');
        }
    }

    /**
     * 删除草稿
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->post('id', 0);

        if (empty($id)) {
            $this->error('参数错误！');
        }

        $model = PaperProject::findOrEmpty($id);
        if ($model->isEmpty()) {
            $this->error('记录不存在！');
        }

        if ($model->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 批量操作
     * @auth true
     */
    public function batch()
    {
        $action = $this->request->post('action', '');
        $ids = $this->request->post('ids', []);

        if (empty($action) || empty($ids)) {
            $this->error('参数错误！');
        }

        $count = 0;
        foreach ($ids as $id) {
            $draft = PaperProject::findOrEmpty($id);
            if ($draft->isEmpty()) continue;

            switch ($action) {
                case 'publish':
                    if ($draft->save(['is_draft' => 0, 'status' => 1, 'update_time' => time()])) {
                        $count++;
                    }
                    break;
                case 'delete':
                    if ($draft->delete()) {
                        $count++;
                    }
                    break;
            }
        }

        $this->success("批量操作成功，处理了 {$count} 个草稿！");
    }
}
