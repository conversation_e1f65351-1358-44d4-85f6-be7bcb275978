{extend name='table'}

{block name="button"}
<!--{if $type eq 'index' and auth("add")}-->
<button data-modal='{:url("add")}' data-table-id="MenuTable" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('添加菜单')}</button>
<!--{/if}-->

<!--{if $type eq 'index' and auth("state")}-->
<button data-action='{:url("state")}' data-table-id="MenuTable" data-rule="id#{sps};status#0" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('禁用菜单')}</button>
<!--{/if}-->

<!--{if $type eq 'recycle' and auth("state")}-->
<button data-action='{:url("state")}' data-table-id="MenuTable" data-rule="id#{spp};status#1" class='layui-btn layui-btn-sm layui-btn-primary'>{:lang('激活菜单')}</button>
<!--{/if}-->
{/block}

{block name="content"}
<div class="layui-tab layui-tab-card">
    <ul class="layui-tab-title">
        {foreach ['index'=>lang('全部菜单'),'recycle'=>lang('回 收 站')] as $k=>$v}
        {if isset($type) and $type eq $k and isset($pid) and $pid == ''}
        <li class="layui-this" data-open="{:url('index')}?type={$k}">{$v}</li>
        {else}
        <li data-open="{:url('index')}?type={$k}">{$v}</li>
        {/if}{/foreach}

        <!--顶级菜单-->
        <!--{foreach $menupList as $k=>$v}-->
        <li data-open="{:url('index')}?type=index&pid={$v.id}" {if isset($pid) and $pid eq $v.id}class="layui-this"{/if} >{$v.title}</li>
        <!--{/foreach}-->
    </ul>
    <div class="layui-tab-content">
        <table id="MenuTable" data-url="{:request()->url()}" data-target-search="form.form-search"></table>
    </div>
</div>

<script>
    $(function () {
        $('#MenuTable').layTable({
            even: true, height: 'full', page: false,
            sort: {field: 'sort desc,id', type: 'asc'},
            where: {type: '{$type|default="index"}', pid: '{$pid|default=""}'},
            filter: function (items) {
                var type = this.where.type;
                return items.filter(function (item) {
                    return !(type === 'index' && parseInt(item.status) === 0);
                });
            },
            cols: [[
                {checkbox: true, field: 'sps'},
                {field: 'sort', title: '{:lang("排序权重")}', width: 100, align: 'center', templet: '#SortInputTpl'},
                {field: 'icon', title: '{:lang("图 标")}', width: 80, align: 'center', templet: '<div><i class="{{d.icon}} font-s18"></i></div>'},
                {field: 'title', title: '{:lang("菜单名称")}', minWidth: 220, templet: '<div><span class="color-desc">{{d.spl}}</span>{{d.title}}</div>'},
                {field: 'url', title: '{:lang("跳转链接")}', minWidth: 200},
                {field: 'status', title: '{:lang("使用状态")}', minWidth: 120, align: 'center', templet: '#StatusSwitchTpl'},
                {toolbar: '#toolbar', title: '{:lang("操作面板")}', minWidth: 150, align: 'center', fixed: 'right'},
            ]]
        });

        // 数据状态切换操作
        layui.form.on('switch(StatusSwitch)', function (object) {
            object.data = {status: object.elem.checked > 0 ? 1 : 0};
            object.data.id = object.value.split('|')[object.data.status] || object.value;
            $.form.load("{:url('state')}", object.data, 'post', function (ret) {
                if (ret.code < 1) $.msg.error(ret.info, 3, function () {
                    $('#MenuTable').trigger('reload');
                }); else {
                    $('#MenuTable').trigger('reload');
                }
                return false;
            }, false);
        });
    });
</script>

<!-- 数据状态切换模板 -->
<script type="text/html" id="StatusSwitchTpl">
    <!--{if auth("state")}-->
    {{# if( "{$type|default='index'}"==='index' || (d.spc<1 || d.status<1)){ }}
    <input type="checkbox" value="{{d.sps}}|{{d.spp}}" lay-text="{:lang('已激活')}|{:lang('已禁用')}" lay-filter="StatusSwitch" lay-skin="switch" {{-d.status>0?'checked':''}}>
    {{# }else{ }}
    {{-d.status ? '<b class="color-green">{:lang('已激活')}</b>' : '<b class="color-red">{:lang('已禁用')}</b>'}}
    {{# } }}
    <!--{else}-->
    {{-d.status ? '<b class="color-green">{:lang('已激活')}</b>' : '<b class="color-red">{:lang('已禁用')}</b>'}}
    <!--{/if}-->
</script>

<!-- 列表排序权重模板 -->
<script type="text/html" id="SortInputTpl">
    <input type="number" min="0" data-blur-number="0" data-action-blur="{:sysuri()}" data-value="id#{{d.id}};action#sort;sort#{value}" data-loading="false" value="{{d.sort}}" class="layui-input text-center">
</script>

<!-- 操控面板的模板 -->
<script type="text/html" id="toolbar">

    <!-- {if isset($type) and $type eq 'index'} -->
    <!-- {if auth('add')} -->
    {{# if(d.spt<2){ }}
    <a class="layui-btn layui-btn-sm layui-btn-primary" data-title="添加系统菜单" data-modal='{:url("add")}?pid={{d.id}}'>{:lang('添 加')}</a>
    {{# }else{ }}
    <a class="layui-btn layui-btn-sm layui-btn-disabled">{:lang('添 加')}</a>
    {{# } }}
    <!-- {/if} -->
    {if auth('edit')}
    <a class="layui-btn layui-btn-sm" data-event-dbclick data-title="编辑系统菜单" data-modal='{:url("edit")}?id={{d.id}}'>{:lang('编 辑')}</a>
    {/if}
    <!-- {else} -->
    {if auth('remove')}
    {{# if( (d.spc<1 || d.status<1)){ }}
    <a class="layui-btn layui-btn-sm layui-btn-danger" data-confirm="确定要删除菜单吗？" data-action="{:url('remove')}" data-value="id#{{d.sps}}">删 除</a>
    {{# }else{ }}
    <a class="layui-btn layui-btn-disabled layui-btn-sm">删 除</a>
    {{# } }}
    {/if}
    <!-- {/if} -->

</script>
{/block}