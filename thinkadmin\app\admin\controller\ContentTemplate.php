<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\DocumentTemplate;
use app\admin\model\PaperType;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 正文模板管理
 * @class ContentTemplate
 * @package app\admin\controller
 */
class ContentTemplate extends Controller
{
    /**
     * 正文模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        try {
            // 使用标准ThinkPHP写法，避免QueryHelper bug
            $this->title = '正文模板管理';

            // 获取论文类型选项
            $paperTypes = PaperType::where(['status' => 1])->column('name', 'id');

            // 构建查询
            $map = ['type' => 'content'];

            // 处理搜索条件
            $params = $this->request->param();

            if (!empty($params['name'])) {
                $map[] = ['name', 'like', '%' . $params['name'] . '%'];
            }

            if (isset($params['paper_type_id']) && $params['paper_type_id'] !== '') {
                $map['paper_type_id'] = $params['paper_type_id'];
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $map['status'] = $params['status'];
            }

            if (isset($params['is_default']) && $params['is_default'] !== '') {
                $map['is_default'] = $params['is_default'];
            }

            // 执行查询和分页
            $list = DocumentTemplate::where($map)
                ->order('is_default desc,usage_count desc,id desc')
                ->paginate([
                    'list_rows' => 20,
                    'query' => $params
                ]);

            // 注释掉AJAX返回，强制使用HTML渲染
            // if ($this->request->isAjax()) {
            //     return json([
            //         'code' => 1,
            //         'msg' => '获取数据成功',
            //         'data' => [
            //             'list' => $list->items(),
            //             'total' => $list->total(),
            //             'paperTypes' => $paperTypes
            //         ]
            //     ]);
            // }

            // 设置模板变量
            $this->assign([
                'title' => '正文模板管理',
                'list' => $list,
                'pagehtml' => $list->render(),
                'paperTypes' => $paperTypes,
                'get' => $params
            ]);

            return $this->fetch();

        } catch (\Exception $e) {
            // 如果出现任何错误，返回错误信息
            // if ($this->request->isAjax()) {
            //     return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
            // }

            $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 添加正文模板
     * @auth true
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name|模板名称' => 'require|max:100',
                'template_content|模板内容' => 'require',
                'paper_type_id|论文类型' => 'require|integer',
            ];

            $this->validate($data, $validate);

            // 处理默认模板逻辑
            if (isset($data['is_default']) && $data['is_default'] == 1) {
                DocumentTemplate::where(['type' => 'content', 'paper_type_id' => $data['paper_type_id']])
                    ->update(['is_default' => 0]);
            }

            $data['type'] = 'content';
            $data['create_time'] = time();

            if (DocumentTemplate::create($data)) {
                $this->success('添加成功！', 'index');
            } else {
                $this->error('添加失败！');
            }
        }

        $this->assign('paperTypes', PaperType::where(['status' => 1])->column('name', 'id'));
        return $this->fetch('content_template/form');
    }

    /**
     * 编辑正文模板
     * @auth true
     */
    public function edit()
    {
        $id = $this->request->param('id', 0);
        $template = DocumentTemplate::find($id);

        if (!$template) {
            $this->error('模板不存在！');
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();

            // 数据验证
            $validate = [
                'name|模板名称' => 'require|max:100',
                'template_content|模板内容' => 'require',
                'paper_type_id|论文类型' => 'require|integer',
            ];

            $this->validate($data, $validate);

            // 处理默认模板逻辑
            if (isset($data['is_default']) && $data['is_default'] == 1) {
                DocumentTemplate::where(['type' => 'content', 'paper_type_id' => $data['paper_type_id']])
                    ->where('id', '<>', $id)
                    ->update(['is_default' => 0]);
            }

            $data['update_time'] = time();

            if ($template->save($data)) {
                $this->success('编辑成功！', 'index');
            } else {
                $this->error('编辑失败！');
            }
        }

        $this->assign('vo', $template);
        $this->assign('paperTypes', PaperType::where(['status' => 1])->column('name', 'id'));
        return $this->fetch('content_template/form');
    }

    /**
     * 查看正文模板详情
     * @auth true
     */
    public function view()
    {
        $id = $this->request->param('id', 0);
        $template = DocumentTemplate::find($id);

        if (!$template) {
            $this->error('模板不存在！');
        }

        $this->assign('vo', $template);
        $this->assign('paperTypes', PaperType::where(['status' => 1])->column('name', 'id'));
        return $this->fetch('content_template/view');
    }

    /**
     * 复制正文模板
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->param('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::find($id);
        if (!$template) {
            $this->error('模板不存在！');
        }

        $copyData = $template->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . ' - 副本';
        $copyData['is_default'] = 0;
        $copyData['usage_count'] = 0;
        $copyData['create_time'] = time();
        $copyData['update_time'] = time();

        if (DocumentTemplate::create($copyData)) {
            $this->success('模板复制成功！');
        } else {
            $this->error('模板复制失败！');
        }
    }

    /**
     * 删除正文模板
     * @auth true
     */
    public function remove()
    {
        $id = $this->request->param('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::find($id);
        if (!$template) {
            $this->error('模板不存在！');
        }

        if ($template->delete()) {
            $this->success('删除成功！');
        } else {
            $this->error('删除失败！');
        }
    }

    /**
     * 修改模板状态
     * @auth true
     */
    public function state()
    {
        $id = $this->request->param('id', 0);
        $status = $this->request->param('status', 0);

        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::find($id);
        if (!$template) {
            $this->error('模板不存在！');
        }

        if ($template->save(['status' => $status])) {
            $this->success('状态修改成功！');
        } else {
            $this->error('状态修改失败！');
        }
    }

    /**
     * 设置默认模板
     * @auth true
     */
    public function setDefault()
    {
        $id = $this->request->param('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::find($id);
        if (!$template) {
            $this->error('模板不存在！');
        }

        // 先取消同类型的默认模板
        DocumentTemplate::where(['type' => 'content', 'paper_type_id' => $template->paper_type_id])
            ->update(['is_default' => 0]);

        // 设置当前模板为默认
        if ($template->save(['is_default' => 1])) {
            $this->success('设置默认模板成功！');
        } else {
            $this->error('设置默认模板失败！');
        }
    }
}
