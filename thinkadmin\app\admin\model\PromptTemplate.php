<?php

// +----------------------------------------------------------------------
// | Prompt Template Model for ThinkAdmin
// +----------------------------------------------------------------------
// | 版权所有 2014~2025 ThinkAdmin [ thinkadmin.top ]
// +----------------------------------------------------------------------
// | 官方网站: https://thinkadmin.top
// +----------------------------------------------------------------------
// | 开源协议 ( https://mit-license.org )
// | 免责声明 ( https://thinkadmin.top/disclaimer )
// +----------------------------------------------------------------------

declare(strict_types=1);

namespace app\admin\model;

use think\admin\Model;

/**
 * 提示词模板模型
 * @class PromptTemplate
 * @package app\admin\model
 */
class PromptTemplate extends Model
{
    /**
     * 数据表名称
     * @var string
     */
    protected $name = 'prompt_template';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['variables'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'paper_type_id' => 'integer',
        'is_default' => 'integer',
        'usage_count' => 'integer',
        'status' => 'integer',
        'variables' => 'array'
    ];

    /**
     * 获取器 - 变量定义
     * @param $value
     * @return array
     */
    public function getVariablesAttr($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * 修改器 - 变量定义
     * @param $value
     * @return string
     */
    public function setVariablesAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 关联论文类型
     * @return \think\model\relation\BelongsTo
     */
    public function paperType()
    {
        return $this->belongsTo(PaperType::class, 'paper_type_id', 'id');
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            0 => '禁用',
            1 => '启用'
        ];
        return $statusMap[$data['status']] ?? '未知';
    }

    /**
     * 获取类型文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            'outline' => '大纲生成',
            'writing' => '内容写作',
            'rewrite' => '内容改写',
            'summary' => '内容总结',
            'expansion' => '内容扩展',
            'polish' => '内容润色'
        ];
        return $typeMap[$data['type']] ?? $data['type'];
    }

    /**
     * 获取默认状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getIsDefaultTextAttr($value, $data)
    {
        return $data['is_default'] ? '是' : '否';
    }

    /**
     * 搜索器 - 模板名称
     * @param $query
     * @param $value
     */
    public function searchNameAttr($query, $value)
    {
        $query->whereLike('name', "%{$value}%");
    }

    /**
     * 搜索器 - 模板类型
     * @param $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        $query->where('type', $value);
    }

    /**
     * 搜索器 - 论文类型
     * @param $query
     * @param $value
     */
    public function searchPaperTypeIdAttr($query, $value)
    {
        $query->where('paper_type_id', $value);
    }

    /**
     * 搜索器 - 状态
     * @param $query
     * @param $value
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 搜索器 - 是否默认
     * @param $query
     * @param $value
     */
    public function searchIsDefaultAttr($query, $value)
    {
        $query->where('is_default', $value);
    }

    /**
     * 根据类型和论文类型获取默认模板
     * @param string $type 模板类型
     * @param int|null $paperTypeId 论文类型ID
     * @return array|null
     */
    public static function getDefaultTemplate(string $type, ?int $paperTypeId = null)
    {
        $query = self::mk()->where(['type' => $type, 'status' => 1, 'is_default' => 1]);
        
        if ($paperTypeId) {
            $query->where('paper_type_id', $paperTypeId);
        }
        
        $template = $query->order('id desc')->findOrEmpty();
        
        if ($template->isEmpty() && $paperTypeId) {
            // 如果没找到指定论文类型的默认模板，尝试查找通用模板
            $template = self::mk()
                ->where(['type' => $type, 'status' => 1, 'is_default' => 1])
                ->whereNull('paper_type_id')
                ->order('id desc')
                ->findOrEmpty();
        }
        
        return $template->isEmpty() ? null : $template->toArray();
    }

    /**
     * 增加使用次数
     * @param int $id 模板ID
     * @return bool
     */
    public static function increaseUsage(int $id): bool
    {
        return self::mk()->where('id', $id)->inc('usage_count')->update() !== false;
    }

    /**
     * 替换模板变量
     * @param string $content 模板内容
     * @param array $variables 变量值
     * @return string
     */
    public static function replaceVariables(string $content, array $variables = []): string
    {
        if (empty($variables)) {
            return $content;
        }
        
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }
        
        return $content;
    }

    /**
     * 验证模板变量
     * @param string $content 模板内容
     * @param array $requiredVars 必需变量
     * @return array 缺失的变量
     */
    public static function validateVariables(string $content, array $requiredVars = []): array
    {
        $missing = [];
        
        foreach ($requiredVars as $var) {
            if (strpos($content, '{' . $var . '}') === false) {
                $missing[] = $var;
            }
        }
        
        return $missing;
    }

    /**
     * 提取模板中的变量
     * @param string $content 模板内容
     * @return array
     */
    public static function extractVariables(string $content): array
    {
        preg_match_all('/\{([^}]+)\}/', $content, $matches);
        return array_unique($matches[1] ?? []);
    }
}
