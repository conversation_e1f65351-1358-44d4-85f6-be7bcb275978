<?php

declare (strict_types=1);

namespace app\admin\model;

use think\admin\Model;
use think\model\relation\BelongsTo;
use think\model\relation\HasMany;

/**
 * 查重任务模型
 * @class CheckTask
 * @package app\admin\model
 */
class CheckTask extends Model
{
    /**
     * 数据表名
     * @var string
     */
    protected $name = 'check_task';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = false;

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'user_id' => 'integer',
        'file_size' => 'integer',
        'word_count' => 'integer',
        'check_api_id' => 'integer',
        'similarity_rate' => 'float',
        'cost' => 'float'
    ];

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getCreateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 格式化输出时间
     * @param mixed $value
     * @return string
     */
    public function getUpdateTimeAttr($value): string
    {
        return format_datetime($value);
    }

    /**
     * 获取状态选项
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => '待提交',
            'submitted' => '已提交',
            'checking' => '查重中',
            'completed' => '已完成',
            'failed' => '失败'
        ];
    }

    /**
     * 获取状态文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = static::getStatusOptions();
        return $statusMap[$data['status']] ?? $data['status'];
    }

    /**
     * 获取文件大小格式化文本
     * @param $value
     * @param $data
     * @return string
     */
    public function getFileSizeTextAttr($value, $data)
    {
        $size = $data['file_size'] ?? 0;
        if ($size < 1024) {
            return $size . ' B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . ' KB';
        } else {
            return round($size / (1024 * 1024), 2) . ' MB';
        }
    }

    /**
     * 获取相似度等级
     * @param $value
     * @param $data
     * @return string
     */
    public function getSimilarityLevelAttr($value, $data)
    {
        $rate = $data['similarity_rate'] ?? 0;
        if ($rate <= 10) {
            return '优秀';
        } elseif ($rate <= 20) {
            return '良好';
        } elseif ($rate <= 30) {
            return '一般';
        } else {
            return '需要修改';
        }
    }

    /**
     * 关联查重接口
     * @return BelongsTo
     */
    public function checkApi(): BelongsTo
    {
        return $this->belongsTo(CheckApi::class, 'check_api_id', 'id');
    }

    /**
     * 关联查重详情
     * @return HasMany
     */
    public function checkDetails(): HasMany
    {
        return $this->hasMany(CheckDetail::class, 'task_id', 'id');
    }

    /**
     * 获取高相似度段落数量
     * @return int
     */
    public function getHighSimilarityCount(): int
    {
        return $this->checkDetails()
            ->where('similarity_rate', '>', 30)
            ->count();
    }

    /**
     * 获取平均段落相似度
     * @return float
     */
    public function getAverageParagraphSimilarity(): float
    {
        return (float) $this->checkDetails()->avg('similarity_rate');
    }

    /**
     * 检查是否可以重新提交
     * @return bool
     */
    public function canResubmit(): bool
    {
        return in_array($this->status, ['failed', 'pending']);
    }

    /**
     * 检查是否已完成
     * @return bool
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * 检查是否正在处理中
     * @return bool
     */
    public function isProcessing(): bool
    {
        return in_array($this->status, ['submitted', 'checking']);
    }

    /**
     * 更新任务状态
     * @param string $status
     * @param array $data
     * @return bool
     */
    public function updateStatus(string $status, array $data = []): bool
    {
        $updateData = array_merge($data, [
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s')
        ]);

        return $this->save($updateData);
    }

    /**
     * 获取报告文件路径
     * @return string
     */
    public function getReportPath(): string
    {
        if (!empty($this->report_path) && file_exists($this->report_path)) {
            return $this->report_path;
        }
        return '';
    }

    /**
     * 获取报告URL
     * @return string
     */
    public function getReportUrl(): string
    {
        return $this->report_url ?: '';
    }

    /**
     * 检查是否有报告
     * @return bool
     */
    public function hasReport(): bool
    {
        return !empty($this->report_url) || !empty($this->report_path);
    }
}
