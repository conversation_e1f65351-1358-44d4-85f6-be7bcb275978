# 插件生态

》》》 更多插件正在开发，敬请期待…… 《《《

目前插件更新比较频繁，如果有发现 **bug**，请尝试升级最新版本。

如果问题依旧，请到 [Gitee](https://gitee.com/zoujingli/ThinkAdmin/issues) 提交问题，提交的内容中需要标注插件名称及问题详情，最好附加环境版本信息。

使用现有插件前请务必认真阅读每个插件的介绍，如果需要开发自己的插件请查看 [插件开发](https://thinkadmin.top/system/plugin-register.html) 章节。

通常情况下，使用 **admin** 及 **wechat** 两个插件就可以完成系统定制开发，且这两个插件都是遵循 **MIT** 协议开源，不影响商业授权。

## 免费开源插件

> 后台基础 Admin 管理插件（ 已稳定可用 ）
>
> * 插件标识：`admin`
> * 插件包名：`zoujingli/think-plugs-admin`
> * 安装方式：`composer require zoujingli/think-plugs-admin`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-admin](https://gitee.com/zoujingli/think-plugs-admin)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-admin.html)
> * 开源协议：[MIT](https://mit-license.org) ( 免费开源，支持本地插件化定制 )

> 后台基础 WeChat 微信插件（ 已稳定可用 ）
> * 插件标识：`wechat`
> * 插件包名：`zoujingli/think-plugs-wechat`
> * 安装方式：`composer require zoujingli/think-plugs-wechat`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-wechat](https://gitee.com/zoujingli/think-plugs-wechat)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-wechat.html)
> * 开源协议：[MIT](https://mit-license.org) ( 免费开源，支持本地插件化定制 )

> 后台静态 Static 初始化插件 ( 通常不需要独立安装 )
> * 插件标识：`static`
> * 插件包名：`zoujingli/think-plugs-static`
> * 安装方式：`composer require zoujingli/think-plugs-static`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-static](https://gitee.com/zoujingli/think-plugs-static)
> * 开源协议：[MIT](https://mit-license.org) ( 免费开源，部分插件包含其他开源协议，具体可以查看源文件，不支持本地插件化定制 )

> 基础插件市场系统 ( 开发中，后期开放 ThinkAdmin 生态市场 )
> * 插件标识：`plugin-center`
> * 插件包名：`zoujingli/think-plugs-center`
> * 安装方式：`composer require zoujingli/think-plugs-center`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-center](https://gitee.com/zoujingli/think-plugs-center)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-center.html)
> * 开源协议：[Apache2](https://www.apache.org/licenses/LICENSE-2.0) ( 免费开源，建议保留版权注释，支持本地插件化定制 )

> 基础插件市场系统 ( 已稳定可用，建议使用 plugin-center )
> * 插件标识：`plugin-center-simple`
> * 插件包名：`zoujingli/think-plugs-center-simple`
> * 安装方式：`composer require zoujingli/think-plugs-center-simple`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-center-simple](https://gitee.com/zoujingli/think-plugs-center-simple)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-center-simple.html)
> * 开源协议：[Apache2](https://www.apache.org/licenses/LICENSE-2.0) ( 免费开源，建议保留版权注释，支持本地插件化定制 )

> 基于 Workerman 的 网络服务插件 ( 已稳定可用 )
> * 插件标识：`plugin-worker`
> * 插件包名：`zoujingli/think-plugs-worker`
> * 安装方式：`composer require zoujingli/think-plugs-worker`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-worker](https://gitee.com/zoujingli/think-plugs-worker)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-worker.html)
> * 开源协议：[Apache2](https://www.apache.org/licenses/LICENSE-2.0) ( 免费开源，建议保留版权注释，支持本地插件化定制 )

## 会员尊享插件

> 多端用户账号系统 ( 已稳定可用 )
> * 插件标识：`plugin-account`
> * 插件包名：`zoujingli/think-plugs-account`
> * 安装方式：`composer require zoujingli/think-plugs-account`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-account](https://gitee.com/zoujingli/think-plugs-account)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-account.html)
> * 授权协议：[VIP授权](https://thinkadmin.top/vip-introduce.html) ( 非VIP用户仅可用于学习，不得商用，支持本地插件化定制 )

> 多端支付管理系统 ( 已稳定可用 )
> * 插件标识：`plugin-payment`
> * 插件包名：`zoujingli/think-plugs-payment`
> * 安装方式：`composer require zoujingli/think-plugs-payment`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-payment](https://gitee.com/zoujingli/think-plugs-payment)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-payment.html)
> * 授权协议：[VIP授权](https://thinkadmin.top/vip-introduce.html) ( 非VIP用户仅可用于学习，不得商用，支持本地插件化定制 )

> 多端微商城系统 ( 已稳定可用 )
> * 插件标识：`plugin-wemall`
> * 插件包名：`zoujingli/think-plugs-wemall`
> * 安装方式：`composer require zoujingli/think-plugs-wemall`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-wemall](https://gitee.com/zoujingli/think-plugs-wemall)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-wemall.html)
> * 授权协议：[VIP授权](https://thinkadmin.top/vip-introduce.html) ( 非VIP用户仅可用于学习，不得商用，支持本地插件化定制 )

> 微信开放平台基础插件（ 已稳定可用 ）
> * 插件标识：`plugin-wechat-service`
> * 插件包名：`zoujingli/think-plugs-wechat-service`
> * 安装方式：`composer require zoujingli/think-plugs-wechat-service`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-wechat-service](https://gitee.com/zoujingli/think-plugs-wechat-service)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-wechat-service.html)
> * 授权协议：[VIP授权](https://thinkadmin.top/vip-introduce.html) ( 非VIP用户仅可用于学习，不得商用，支持本地插件化定制 )

## 收费授权插件

> 一物一码溯源系统 ( 开发中，还未发布 )
> * 插件标识：`plugin-wuma`
> * 插件包名：`zoujingli/think-plugs-wuma`
> * 安装方式：`composer require zoujingli/think-plugs-wuma`
> * 插件仓库：[https://gitee.com/zoujingli/think-plugs-wuma](https://gitee.com/zoujingli/think-plugs-wuma)
> * 文档地址：[查看文档](https://thinkadmin.top/plugin/think-plugs-wuma.html)
> * 授权协议：[收费授权](https://thinkadmin.top/fee-introduce.html) ( 未获得授权仅可用于学习，不得商用，支持本地插件化定制 )

## 案例安装体验

如果需要本地化定制开发，请参考[插件本地化定制](https://thinkadmin.top/system/plugin-developer.html)文档

```shell
# 创建项目
composer create-project zoujingli/thinkadmin

# 进入项目目录
cd thinkadmin

# 安装微信插件
composer require zoujingli/think-plugs-wechat

# 安装插件管理中心
composer require zoujingli/think-plugs-center 

# 安装多端账号管理
composer require zoujingli/think-plugs-account 

# 安装多端支付管理
composer require zoujingli/think-plugs-payment 

# 安装多端商城管理
composer require zoujingli/think-plugs-wemall

# 启动 WEB 内置服务
php think run --host 127.0.0.1
```