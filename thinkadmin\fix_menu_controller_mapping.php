<?php

/**
 * 修复菜单控制器映射
 */

echo "=== 修复菜单控制器映射 ===\n\n";

try {
    $dbPath = __DIR__ . '/database/sqlite.db';
    if (!file_exists($dbPath)) {
        echo "❌ 数据库文件不存在\n";
        exit(1);
    }
    
    $pdo = new PDO("sqlite:{$dbPath}");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ 数据库连接成功\n\n";
    
    // 定义需要修复的映射关系
    $mappings = [
        // 原snake_case -> 正确的CamelCase
        'admin/paper_project/index' => 'admin/PaperProject/index',
        'admin/paper_project/draft' => 'admin/DraftBox/index',  // 草稿箱使用专门的DraftBox控制器
        'admin/paper_type/index' => 'admin/PaperType/index',
        'admin/ai_model/index' => 'admin/AiModel/index',
        'admin/rewrite_record/index' => 'admin/RewriteRecord/index',
        'admin/system_notice/index' => 'admin/SystemNotice/index',
        'admin/export_template/index' => 'admin/ExportTemplate/index',
        'admin/outline_template/index' => 'admin/OutlineTemplate/index',
        'admin/message_template/index' => 'admin/MessageTemplate/index',
        'admin/n8n_workflow/index' => 'admin/N8nWorkflow/index',
        'admin/rewrite_model/index' => 'admin/RewriteModel/index',
        'admin/vip_package/index' => 'admin/VipPackage/index',
        'admin/package_config/index' => 'admin/PackageConfig/index',
        'admin/export_monitor/index' => 'admin/ExportMonitor/index',
        'admin/prompt_template/index' => 'admin/PromptTemplate/index',
        'admin/check_record/index' => 'admin/CheckRecord/index',
        'admin/user_points/index' => 'admin/UserPoints/index',
        'admin/recharge_record/index' => 'admin/RechargeRecord/index',
        'admin/email_config/index' => 'admin/EmailConfig/index',
        'admin/api_key/index' => 'admin/ApiKey/index',
        'admin/rewrite_task/index' => 'admin/RewriteTask/index',
        'admin/content_template/index' => 'admin/ContentTemplate/index',
        'admin/notification_log/index' => 'admin/NotificationLog/index',
        'admin/webhook_config/index' => 'admin/WebhookConfig/index',
        'admin/content_filter/index' => 'admin/ContentFilter/index',
        'admin/basic_config/index' => 'admin/BasicConfig/index',
    ];
    
    echo "1. 修复菜单控制器映射:\n";
    $updatedCount = 0;
    
    foreach ($mappings as $oldNode => $newNode) {
        // 检查是否存在对应的控制器文件
        $controllerPath = str_replace('admin/', '', $newNode);
        $parts = explode('/', $controllerPath);
        $controllerName = $parts[0];
        $controllerFile = "app/admin/controller/{$controllerName}.php";
        
        if (file_exists($controllerFile)) {
            // 更新菜单项
            $stmt = $pdo->prepare("UPDATE system_menu SET node = ?, url = ? WHERE node = ?");
            $result = $stmt->execute([$newNode, $newNode, $oldNode]);
            
            if ($stmt->rowCount() > 0) {
                echo "  ✅ 更新: {$oldNode} -> {$newNode}\n";
                $updatedCount++;
            }
        } else {
            echo "  ❌ 控制器不存在: {$controllerFile}\n";
        }
    }
    
    echo "\n2. 更新统计:\n";
    echo "  📊 共更新了 {$updatedCount} 个菜单项\n";
    
    // 验证更新结果
    echo "\n3. 验证更新结果:\n";
    $stmt = $pdo->query("SELECT title, node, url FROM system_menu WHERE status = 1 AND node != '' ORDER BY sort ASC, id ASC");
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $validCount = 0;
    $invalidCount = 0;
    
    foreach ($menus as $menu) {
        $node = $menu['node'];
        $title = $menu['title'];
        
        if (strpos($node, 'admin/') === 0) {
            $controllerPath = str_replace('admin/', '', $node);
            $parts = explode('/', $controllerPath);
            
            if (count($parts) >= 2) {
                $controllerName = $parts[0];
                $controllerFile = "app/admin/controller/{$controllerName}.php";
                
                if (file_exists($controllerFile)) {
                    $validCount++;
                    echo "  ✅ {$title}: {$controllerName} - 有效\n";
                } else {
                    $invalidCount++;
                    echo "  ❌ {$title}: {$controllerName} - 无效\n";
                }
            }
        }
    }
    
    echo "\n4. 最终统计:\n";
    echo "  ✅ 有效菜单项: {$validCount}\n";
    echo "  ❌ 无效菜单项: {$invalidCount}\n";
    
    if ($invalidCount == 0) {
        echo "  🎉 所有菜单项都有对应的控制器！\n";
    }
    
} catch (Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
}

echo "\n=== 修复完成 ===\n";
