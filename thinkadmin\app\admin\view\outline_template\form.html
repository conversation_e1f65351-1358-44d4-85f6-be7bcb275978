{extend name='form'}

{block name="content"}
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-box" action="{:request()->url()}" data-auto="true" method="post">
            <div class="layui-form-item">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <input name="name" value="{$vo.name|default=''}" required lay-verify="required" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">适用类型</label>
                <div class="layui-input-block">
                    <select name="paper_type_id" lay-verify="required">
                        <option value="">请选择论文类型</option>
                        <option value="0" {if isset($vo.paper_type_id) and $vo.paper_type_id eq '0'}selected{/if}>通用模板</option>
                        {volist name="paperTypes" id="v" key="k"}
                        {if isset($vo.paper_type_id) and $vo.paper_type_id eq $k}
                        <option selected value="{$k}">{$v}</option>
                        {else}
                        <option value="{$k}">{$v}</option>
                        {/if}
                        {/volist}
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">模板描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入模板描述" class="layui-textarea">{$vo.description|default=''}</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">大纲内容</label>
                <div class="layui-input-block">
                    <textarea name="content" required lay-verify="required" placeholder="请输入大纲内容，每行一个章节" class="layui-textarea" style="min-height: 200px;">{$vo.content|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        每行一个章节，支持多级标题。例如：<br>
                        第一章 绪论<br>
                        &nbsp;&nbsp;1.1 研究背景<br>
                        &nbsp;&nbsp;1.2 研究意义<br>
                        第二章 文献综述<br>
                        &nbsp;&nbsp;2.1 国内研究现状<br>
                        &nbsp;&nbsp;2.2 国外研究现状
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">字数要求</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <input name="min_words" value="{$vo.min_words|default='0'}" placeholder="最少字数" class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline" style="width: 120px;">
                    <input name="max_words" value="{$vo.max_words|default='0'}" placeholder="最多字数" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux">字数范围，0表示不限制</div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_default" value="1" title="是" {if isset($vo.is_default) and $vo.is_default eq 1}checked{/if}>
                    <input type="radio" name="is_default" value="0" title="否" {if !isset($vo.is_default) or $vo.is_default eq 0}checked{/if}>
                    <div class="layui-form-mid layui-word-aux">设为默认模板后，其他同类型模板将自动取消默认</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">排序权重</label>
                <div class="layui-input-block">
                    <input name="sort" value="{$vo.sort|default='0'}" placeholder="请输入排序权重" class="layui-input">
                    <div class="layui-form-mid layui-word-aux">数值越大越靠前</div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" {if !isset($vo.status) or $vo.status eq 1}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" {if isset($vo.status) and $vo.status eq 0}checked{/if}>
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-form-item text-center">
                {if empty($vo.id)}
                <button class="layui-btn" type='submit'>添加模板</button>
                {else}
                <button class="layui-btn" type='submit'>编辑模板</button>
                {/if}
                <button class="layui-btn layui-btn-danger" type='button' data-confirm="确定要取消编辑吗？" data-close>取消编辑</button>
            </div>

        </form>
    </div>
</div>
{/block}

{block name='script'}
<script>
    layui.use(['form'], function () {
        var form = layui.form;
        
        // 监听是否默认选择
        form.on('radio(is_default)', function(data){
            if(data.value == '1') {
                layer.msg('设为默认模板后，其他同类型模板将自动取消默认', {icon: 1});
            }
        });
    });
</script>
{/block}
