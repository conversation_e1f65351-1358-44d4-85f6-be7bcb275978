<?php

declare (strict_types=1);

namespace app\admin\controller;

use app\admin\model\DocumentTemplate;
use app\admin\model\PaperType;
use think\admin\Controller;
use think\admin\helper\QueryHelper;

/**
 * 导出样式模板管理
 * @class ExportTemplate
 * @package app\admin\controller
 */
class ExportTemplate extends Controller
{
    /**
     * 导出样式模板管理
     * @auth true
     * @menu true
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
        public function index()
    {
        ExportTemplateModel::mQuery()->layTable(function () {
            $this->title = '导出模板管理';
        }, static function (QueryHelper $query) {
            $query->like('name,title,description')->equal('status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }, static function (QueryHelper $query) {
            // 只显示导出相关的模板类型
            $query->where('type', 'in', ['cover', 'reference', 'appendix']);
            $query->like('name,description')->equal('paper_type_id,type,status');
            $query->dateBetween('create_time');
            $query->order('id desc');
        });
    }

    /**
     * 添加导出样式模板
     * @auth true
     */public function add()
    {
        DocumentTemplate::mForm('form');
    }

    /**
     * 编辑导出样式模板
     * @auth true
     */
    public function edit()
    {
        DocumentTemplate::mForm('form');
    }

    /**
     * 表单数据处理
     * @param array $vo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected function _form_filter(array &$vo)
    {
        if ($this->request->isGet()) {
            $this->paperTypes = PaperType::mk()->where(['status' => 1])->column('name', 'id');
            $this->typeOptions = [
                'cover' => '封面模板',
                'reference' => '参考文献模板',
                'appendix' => '附录模板'
            ];
            
            // 如果是编辑，获取当前模板信息
            if (!empty($vo['id'])) {
                $vo['style_config'] = $vo['style_config'] ?: [];
            }
        } else {
            // 限制只能创建导出相关的模板类型
            if (!in_array($vo['type'], ['cover', 'reference', 'appendix'])) {
                $this->error('只能创建封面、参考文献或附录模板！');
            }
            
            // 处理样式配置
            if (!empty($vo['style_config']) && is_string($vo['style_config'])) {
                $styleConfig = json_decode($vo['style_config'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->error('样式配置格式错误！');
                }
                $vo['style_config'] = $styleConfig;
            }
            
            // 设置创建时间
            if (empty($vo['id'])) {
                $vo['create_time'] = date('Y-m-d H:i:s');
            }
            $vo['update_time'] = date('Y-m-d H:i:s');
        }
    }

    /**
     * 表单结果处理
     * @param boolean $result
     */
    protected function _form_result(bool $result)
    {
        if ($result) {
            $this->success('模板保存成功！', 'javascript:history.back()');
        }
    }

    /**
     * 删除导出样式模板
     * @auth true
     */
    public function remove()
    {
        DocumentTemplate::mDelete();
    }

    /**
     * 修改导出样式模板状态
     * @auth true
     */
    public function state()
    {
        DocumentTemplate::mSave($this->_vali([
            'status.in:0,1'  => '状态值范围异常！',
        ]));
    }

    /**
     * 复制导出样式模板
     * @auth true
     */
    public function copy()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }

        // 创建副本
        $copyData = $template->toArray();
        unset($copyData['id']);
        $copyData['name'] = $copyData['name'] . '_副本';
        $copyData['is_default'] = 0;
        $copyData['usage_count'] = 0;
        $copyData['create_time'] = date('Y-m-d H:i:s');
        $copyData['update_time'] = date('Y-m-d H:i:s');

        $newTemplate = DocumentTemplate::mk()->save($copyData);
        if ($newTemplate) {
            $this->success('模板复制成功！');
        } else {
            $this->error('模板复制失败！');
        }
    }

    /**
     * 设置默认模板
     * @auth true
     */
    public function setDefault()
    {
        $id = $this->request->post('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::mk()->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }

        // 取消同类型的其他默认模板
        DocumentTemplate::mk()
            ->where(['type' => $template->type, 'paper_type_id' => $template->paper_type_id])
            ->save(['is_default' => 0]);

        // 设置当前模板为默认
        if ($template->save(['is_default' => 1])) {
            $this->success('设置默认模板成功！');
        } else {
            $this->error('设置默认模板失败！');
        }
    }

    /**
     * 预览导出样式模板
     * @auth true
     */
    public function preview()
    {
        $id = $this->request->get('id', 0);
        if (empty($id)) {
            $this->error('模板ID不能为空！');
        }

        $template = DocumentTemplate::mk()->with(['paperType'])->findOrEmpty($id);
        if ($template->isEmpty()) {
            $this->error('模板不存在！');
        }

        $this->assign('template', $template);
        $this->assign('title', '模板预览');
        
        return $this->fetch('export_template/preview');
    }

    /**
     * 导出模板配置
     * @auth true
     */
    public function export()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要导出的模板！');
        }

        $idArray = explode(',', $ids);
        $templates = DocumentTemplate::mk()
            ->with(['paperType'])
            ->whereIn('id', $idArray)
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->order('id desc')
            ->select();

        if ($templates->isEmpty()) {
            $this->error('没有找到要导出的模板！');
        }

        // 构建导出数据
        $exportData = [];
        $exportData[] = ['ID', '模板名称', '模板类型', '论文类型', '是否默认', '使用次数', '状态', '创建时间'];
        
        foreach ($templates as $template) {
            $exportData[] = [
                $template->id,
                $template->name,
                $template->type_text,
                $template->paperType->name ?? '通用',
                $template->is_default ? '是' : '否',
                $template->usage_count,
                $template->status ? '启用' : '禁用',
                $template->create_time
            ];
        }

        // 这里应该调用实际的导出功能，比如生成Excel文件
        // 暂时返回成功消息
        $this->success('导出功能开发中...');
    }

    /**
     * 批量删除导出样式模板
     * @auth true
     */
    public function batchRemove()
    {
        $ids = $this->request->post('ids', '');
        if (empty($ids)) {
            $this->error('请选择要删除的模板！');
        }

        $idArray = explode(',', $ids);
        
        // 检查是否有默认模板
        $defaultCount = DocumentTemplate::mk()
            ->whereIn('id', $idArray)
            ->where('is_default', 1)
            ->count();
            
        if ($defaultCount > 0) {
            $this->error('不能删除默认模板，请先取消默认设置！');
        }
        
        $result = DocumentTemplate::mk()->whereIn('id', $idArray)->delete();
        
        if ($result) {
            $this->success('批量删除成功！');
        } else {
            $this->error('批量删除失败！');
        }
    }

    /**
     * 获取模板统计数据
     * @auth true
     */
    public function statistics()
    {
        // 总模板数（仅导出相关）
        $totalTemplates = DocumentTemplate::mk()
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->count();
        
        // 各类型模板数
        $typeStats = DocumentTemplate::mk()
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->field('type, COUNT(*) as count')
            ->group('type')
            ->select()
            ->toArray();

        // 今日新增模板数
        $todayTemplates = DocumentTemplate::mk()
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->whereTime('create_time', 'today')
            ->count();

        // 本月新增模板数
        $monthTemplates = DocumentTemplate::mk()
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->whereTime('create_time', 'month')
            ->count();

        // 使用次数最多的模板
        $mostUsedTemplate = DocumentTemplate::mk()
            ->where('type', 'in', ['cover', 'reference', 'appendix'])
            ->order('usage_count desc')
            ->findOrEmpty();

        $statistics = [
            'total_templates' => $totalTemplates,
            'today_templates' => $todayTemplates,
            'month_templates' => $monthTemplates,
            'type_stats' => $typeStats,
            'most_used_template' => $mostUsedTemplate->toArray()
        ];

        return json($statistics);
    }
            // 如果QueryHelper出现问题，使用简化查询
            $this->title = 'ExportTemplate管理';
            $this->error('页面加载失败：' . $e->getMessage());
        }
}
